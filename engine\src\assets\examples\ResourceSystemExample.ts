/**
 * 增强资源管理系统示例
 * 展示如何使用增强版资源管理系统
 */
import * as THREE from 'three';
import { AssetType } from '../ResourceManager';
import { EnhancedResourceSystem } from '../EnhancedResourceSystem';
import { DependencyType } from '../EnhancedResourceDependencyManager';

/**
 * 资源管理系统示例
 */
export class ResourceSystemExample {
  /** 资源管理系统 */
  private resourceSystem: EnhancedResourceSystem;

  /** Three.js场景 */
  private scene: THREE.Scene;

  /** Three.js相机 */
  private camera: THREE.PerspectiveCamera;

  /** Three.js渲染器 */
  private renderer: THREE.WebGLRenderer;

  /** 加载状态元素 */
  private loadingElement: HTMLElement | null = null;

  /** 进度元素 */
  private progressElement: HTMLElement | null = null;

  /** 状态元素 */
  private statusElement: HTMLElement | null = null;

  /** 是否已初始化 */
  private initialized: boolean = false;

  /**
   * 创建资源管理系统示例实例
   */
  constructor() {
    // 创建资源管理系统
    this.resourceSystem = new EnhancedResourceSystem({
      debug: true,
      resourceManagerOptions: {
        maxCacheSize: 1024 * 1024 * 500, // 500MB
        maxConcurrentLoads: 8,
        enableVersioning: true,
        resourceVersion: '1.0.0'
      },
      dependencyManagerOptions: {
        enableCycleDetection: true,
        enableOptimization: true
      },
      preloaderOptions: {
        maxConcurrentLoads: 4,
        enableProgressEstimation: true
      }
    });

    // 创建Three.js场景
    this.scene = new THREE.Scene();
    this.camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
    this.renderer = new THREE.WebGLRenderer({ antialias: true });
    this.renderer.setSize(window.innerWidth, window.innerHeight);
    this.renderer.setClearColor(0x222222);
    document.body.appendChild(this.renderer.domElement);

    // 设置相机位置
    this.(camera as any).getPosition().z = 5;

    // 创建UI元素
    this.createUI();

    // 监听窗口大小变化
    window.addEventListener('resize', this.onWindowResize.bind(this));
  }

  /**
   * 创建UI元素
   */
  private createUI(): void {
    // 创建加载状态元素
    this.loadingElement = document.createElement('div');
    this.loadingElement.style.position = 'absolute';
    this.loadingElement.style.top = '10px';
    this.loadingElement.style.left = '10px';
    this.loadingElement.style.color = 'white';
    this.loadingElement.style.fontFamily = 'Arial, sans-serif';
    this.loadingElement.style.fontSize = '14px';
    this.loadingElement.style.backgroundColor = 'rgba(0, 0, 0, 0.5)';
    this.loadingElement.style.padding = '10px';
    this.loadingElement.style.borderRadius = '5px';
    this.loadingElement.style.zIndex = '1000';
    this.loadingElement.textContent = '加载中...';
    document.body.appendChild(this.loadingElement);

    // 创建进度元素
    this.progressElement = document.createElement('div');
    this.progressElement.style.position = 'absolute';
    this.progressElement.style.top = '50px';
    this.progressElement.style.left = '10px';
    this.progressElement.style.color = 'white';
    this.progressElement.style.fontFamily = 'Arial, sans-serif';
    this.progressElement.style.fontSize = '14px';
    this.progressElement.style.backgroundColor = 'rgba(0, 0, 0, 0.5)';
    this.progressElement.style.padding = '10px';
    this.progressElement.style.borderRadius = '5px';
    this.progressElement.style.zIndex = '1000';
    this.progressElement.textContent = '进度: 0%';
    document.body.appendChild(this.progressElement);

    // 创建状态元素
    this.statusElement = document.createElement('div');
    this.statusElement.style.position = 'absolute';
    this.statusElement.style.top = '90px';
    this.statusElement.style.left = '10px';
    this.statusElement.style.color = 'white';
    this.statusElement.style.fontFamily = 'Arial, sans-serif';
    this.statusElement.style.fontSize = '14px';
    this.statusElement.style.backgroundColor = 'rgba(0, 0, 0, 0.5)';
    this.statusElement.style.padding = '10px';
    this.statusElement.style.borderRadius = '5px';
    this.statusElement.style.zIndex = '1000';
    this.statusElement.textContent = '状态: 未初始化';
    document.body.appendChild(this.statusElement);

    // 创建按钮
    const buttonStyle = `
      position: absolute;
      padding: 10px;
      margin: 5px;
      background-color: #4CAF50;
      color: white;
      border: none;
      border-radius: 5px;
      cursor: pointer;
      font-family: Arial, sans-serif;
      font-size: 14px;
      z-index: 1000;
    `;

    // 加载基础资源按钮
    const loadBasicButton = document.createElement('button');
    loadBasicButton.style.cssText = buttonStyle;
    loadBasicButton.style.top = '130px';
    loadBasicButton.style.left = '10px';
    loadBasicButton.textContent = '加载基础资源';
    loadBasicButton.addEventListener('click', this.loadBasicResources.bind(this));
    document.body.appendChild(loadBasicButton);

    // 加载模型按钮
    const loadModelsButton = document.createElement('button');
    loadModelsButton.style.cssText = buttonStyle;
    loadModelsButton.style.top = '170px';
    loadModelsButton.style.left = '10px';
    loadModelsButton.textContent = '加载模型';
    loadModelsButton.addEventListener('click', this.loadModelResources.bind(this));
    document.body.appendChild(loadModelsButton);

    // 清理缓存按钮
    const cleanupButton = document.createElement('button');
    cleanupButton.style.cssText = buttonStyle;
    cleanupButton.style.top = '210px';
    cleanupButton.style.left = '10px';
    cleanupButton.textContent = '清理缓存';
    cleanupButton.addEventListener('click', this.cleanupCache.bind(this));
    document.body.appendChild(cleanupButton);

    // 显示统计信息按钮
    const statsButton = document.createElement('button');
    statsButton.style.cssText = buttonStyle;
    statsButton.style.top = '250px';
    statsButton.style.left = '10px';
    statsButton.textContent = '显示统计信息';
    statsButton.addEventListener('click', this.showStats.bind(this));
    document.body.appendChild(statsButton);
  }

  /**
   * 初始化
   */
  public initialize(): void {
    if (this.initialized) {
      return;
    }

    // 初始化资源管理系统
    this.resourceSystem.initialize();

    // 设置事件监听器
    this.setupEventListeners();

    this.initialized = true;
    this.updateStatus('已初始化');
  }

  /**
   * 设置事件监听器
   */
  private setupEventListeners(): void {
    // 资源加载事件
    this.resourceSystem.on('resourceLoadStart', (data) => {
      console.log(`开始加载资源: ${data.id}`);
    });

    this.resourceSystem.on('resourceLoadComplete', (data) => {
      console.log(`完成加载资源: ${data.id}`);
    });

    this.resourceSystem.on('resourceLoadError', (data) => {
      console.error(`加载资源失败: ${data.id}`, data.error);
    });

    // 预加载事件
    this.resourceSystem.on('preloadStart', (data) => {
      this.updateLoading(`开始加载组: ${data.group}`);
    });

    this.resourceSystem.on('preloadComplete', (data) => {
      this.updateLoading(`完成加载组: ${data.group}`);
      this.updateProgress(100);
    });

    this.resourceSystem.on('preloadError', (data) => {
      this.updateLoading(`加载组失败: ${data.group}`);
    });

    this.resourceSystem.on('preloadProgressUpdated', (data) => {
      const percent = Math.round(data.progress * 100);
      this.updateProgress(percent);
    });
  }

  /**
   * 更新加载状态
   * @param text 状态文本
   */
  private updateLoading(text: string): void {
    if (this.loadingElement) {
      this.loadingElement.textContent = text;
    }
  }

  /**
   * 更新进度
   * @param percent 进度百分比
   */
  private updateProgress(percent: number): void {
    if (this.progressElement) {
      this.progressElement.textContent = `进度: ${percent}%`;
    }
  }

  /**
   * 更新状态
   * @param text 状态文本
   */
  private updateStatus(text: string): void {
    if (this.statusElement) {
      this.statusElement.textContent = `状态: ${text}`;
    }
  }

  /**
   * 窗口大小变化处理
   */
  private onWindowResize(): void {
    this.camera.aspect = window.innerWidth / window.innerHeight;
    this.camera.updateProjectionMatrix();
    this.renderer.setSize(window.innerWidth, window.innerHeight);
  }

  /**
   * 加载基础资源
   */
  private async loadBasicResources(): Promise<void> {
    this.updateStatus('加载基础资源');

    // 定义基础资源组
    const basicGroup = {
      name: 'basic',
      priority: 100,
      resources: [
        {
          id: 'texture1',
          type: AssetType.TEXTURE,
          url: 'assets/textures/texture1.jpg',
          priority: 90
        },
        {
          id: 'texture2',
          type: AssetType.TEXTURE,
          url: 'assets/textures/texture2.jpg',
          priority: 80
        },
        {
          id: 'cubemap',
          type: AssetType.CUBEMAP,
          url: 'assets/textures/cubemap',
          priority: 70
        },
        {
          id: 'audio1',
          type: AssetType.AUDIO,
          url: 'assets/audio/sound1.mp3',
          priority: 60
        }
      ]
    };

    // 添加预加载组
    this.resourceSystem.addPreloadGroup(basicGroup);

    try {
      // 加载组
      await this.resourceSystem.loadPreloadGroup('basic', (progress) => {
        console.log(`加载进度: ${Math.round(progress.progress * 100)}%`);
      });

      this.updateStatus('基础资源加载完成');

      // 使用加载的资源
      this.useBasicResources();
    } catch (error) {
      console.error('加载基础资源失败:', error);
      this.updateStatus('基础资源加载失败');
    }
  }

  /**
   * 使用基础资源
   */
  private useBasicResources(): void {
    // 获取纹理
    const texture1 = this.resourceSystem.getResource('texture1');
    const texture2 = this.resourceSystem.getResource('texture2');
    const cubemap = this.resourceSystem.getResource('cubemap');

    if (texture1 && texture2) {
      // 创建立方体
      const geometry = new THREE.BoxGeometry(1, 1, 1);
      const material1 = new THREE.MeshBasicMaterial({ map: texture1 });
      const material2 = new THREE.MeshBasicMaterial({ map: texture2 });

      const cube1 = new THREE.Mesh(geometry, material1);
      (cube1 as any).getPosition().x = -1.5;
      this.scene.add(cube1);

      const cube2 = new THREE.Mesh(geometry, material2);
      (cube2 as any).getPosition().x = 1.5;
      this.scene.add(cube2);
    }

    if (cubemap) {
      // 设置环境贴图
      this.scene.background = cubemap;
    }

    // 开始渲染
    this.animate();
  }

  /**
   * 加载模型资源
   */
  private async loadModelResources(): Promise<void> {
    this.updateStatus('加载模型资源');

    // 定义模型资源组
    const modelsGroup = {
      name: 'models',
      priority: 80,
      dependencies: ['basic'], // 依赖基础资源组
      resources: [
        {
          id: 'model1',
          type: AssetType.MODEL,
          url: 'assets/models/model1.gltf',
          priority: 90
        },
        {
          id: 'model2',
          type: AssetType.MODEL,
          url: 'assets/models/model2.gltf',
          priority: 80
        }
      ]
    };

    // 添加预加载组
    this.resourceSystem.addPreloadGroup(modelsGroup);

    try {
      // 加载组
      await this.resourceSystem.loadPreloadGroup('models', (progress) => {
        console.log(`加载进度: ${Math.round(progress.progress * 100)}%`);
      });

      this.updateStatus('模型资源加载完成');

      // 使用加载的资源
      this.useModelResources();
    } catch (error) {
      console.error('加载模型资源失败:', error);
      this.updateStatus('模型资源加载失败');
    }
  }

  /**
   * 使用模型资源
   */
  private useModelResources(): void {
    // 获取模型
    const model1 = this.resourceSystem.getResource('model1');
    const model2 = this.resourceSystem.getResource('model2');

    if (model1) {
      // 添加模型到场景
      model1.(scene as any).setPosition(-3, 0, 0);
      model1.(scene as any).setScale(0.5, 0.5, 0.5);
      this.scene.add(model1.scene);
    }

    if (model2) {
      // 添加模型到场景
      model2.(scene as any).setPosition(3, 0, 0);
      model2.(scene as any).setScale(0.5, 0.5, 0.5);
      this.scene.add(model2.scene);
    }
  }

  /**
   * 清理缓存
   */
  private cleanupCache(): void {
    this.updateStatus('清理缓存');
    this.resourceSystem.cleanupCache();

    // 显示缓存统计信息
    this.showStats();
  }

  /**
   * 显示统计信息
   */
  private showStats(): void {
    const stats = this.resourceSystem.getCacheStats();

    const statsText = `
      总资源数: ${stats.totalResources}
      已加载资源数: ${stats.loadedResources}
      错误资源数: ${stats.errorResources}
      总大小: ${this.formatSize(stats.totalSize)}
      最大大小: ${this.formatSize(stats.maxSize)}
      使用率: ${stats.usagePercentage.toFixed(2)}%
    `;

    alert(statsText);
    this.updateStatus('显示统计信息');
  }

  /**
   * 格式化大小
   * @param bytes 字节数
   * @returns 格式化后的大小字符串
   */
  private formatSize(bytes: number): string {
    if (bytes < 1024) {
      return bytes + ' B';
    } else if (bytes < 1024 * 1024) {
      return (bytes / 1024).toFixed(2) + ' KB';
    } else if (bytes < 1024 * 1024 * 1024) {
      return (bytes / (1024 * 1024)).toFixed(2) + ' MB';
    } else {
      return (bytes / (1024 * 1024 * 1024)).toFixed(2) + ' GB';
    }
  }

  /**
   * 动画循环
   */
  private animate(): void {
    requestAnimationFrame(this.animate.bind(this));

    // 旋转场景中的所有立方体
    this.scene.traverse((object) => {
      if (object instanceof THREE.Mesh && object.geometry instanceof THREE.BoxGeometry) {
        object.rotation.x += 0.01;
        object.rotation.y += 0.01;
      }
    });

    this.renderer.render(this.scene, this.camera);
  }

  /**
   * 销毁
   */
  public dispose(): void {
    // 销毁资源管理系统
    (this.resourceSystem as any).dispose();

    // 销毁Three.js资源
    this.scene.traverse((object) => {
      if (object instanceof THREE.Mesh) {
        (object.geometry as any).dispose();

        if (Array.isArray(object.material)) {
          object.material.forEach(material => (material as any).dispose());
        } else {
          (object.material as any).dispose();
        }
      }
    });

    (this.renderer as any).dispose();

    // 移除渲染器元素
    if (this.renderer.domElement.parentNode) {
      this.renderer.domElement.parentNode.removeChild(this.renderer.domElement);
    }

    // 移除UI元素
    if (this.loadingElement && this.loadingElement.parentNode) {
      this.loadingElement.parentNode.removeChild(this.loadingElement);
    }

    if (this.progressElement && this.progressElement.parentNode) {
      this.progressElement.parentNode.removeChild(this.progressElement);
    }

    if (this.statusElement && this.statusElement.parentNode) {
      this.statusElement.parentNode.removeChild(this.statusElement);
    }

    // 移除窗口大小变化监听器
    window.removeEventListener('resize', this.onWindowResize.bind(this));

    this.initialized = false;
    this.updateStatus('已销毁');
  }
}