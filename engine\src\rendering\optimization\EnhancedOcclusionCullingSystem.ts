/**
 * 增强的遮挡剔除系统
 * 提供高效的遮挡剔除算法，减少不可见对象的渲染
 */
import * as THREE from 'three';
import { System } from '../../core/System';
import type { Entity } from '../../core/Entity';
import { type type Camera   } from '../Camera';
import { Scene } from '../../scene/Scene';
import type { Transform } from '../../scene/Transform';
import { CullableComponent } from './CullableComponent';
import { OcclusionCullingSystem, OcclusionCullingAlgorithm, OcclusionCullingSystemOptions } from './OcclusionCullingSystem';
import { Debug } from '../../utils/Debug';
import { EventEmitter } from '../../utils/EventEmitter';
import { Octree } from '../../utils/Octree';
import { PerformanceMonitor } from '../../utils/PerformanceMonitor';

/**
 * 增强的遮挡剔除系统事件类型
 */
export enum EnhancedOcclusionCullingSystemEventType {
  /** 实体被剔除 */
  ENTITY_CULLED = 'entity_culled',
  /** 实体被恢复 */
  ENTITY_RESTORED = 'entity_restored',
  /** 组件添加 */
  COMPONENT_ADDED = 'component_added',
  /** 组件移除 */
  COMPONENT_REMOVED = 'component_removed',
  /** 算法变更 */
  ALGORITHM_CHANGED = 'algorithm_changed',
  /** 性能统计更新 */
  STATS_UPDATED = 'stats_updated'
}

/**
 * 增强的遮挡剔除系统配置接口
 */
export interface EnhancedOcclusionCullingSystemOptions extends OcclusionCullingSystemOptions {
  /** 是否使用自适应算法选择 */
  useAdaptiveAlgorithm?: boolean;
  /** 是否使用多级遮挡剔除 */
  useMultiLevelCulling?: boolean;
  /** 是否使用预测剔除 */
  usePredictiveCulling?: boolean;
  /** 是否使用时间一致性 */
  useTemporalCoherence?: boolean;
  /** 是否使用GPU加速 */
  useGPUAcceleration?: boolean;
  /** 是否使用保守剔除 */
  useConservativeCulling?: boolean;
  /** 是否收集性能统计 */
  collectStats?: boolean;
  /** 是否使用自动优化 */
  useAutoOptimization?: boolean;
  /** 自动优化间隔（毫秒） */
  autoOptimizationInterval?: number;
}

/**
 * 遮挡剔除性能统计
 */
export interface OcclusionCullingStats {
  /** 总对象数量 */
  totalObjects: number;
  /** 剔除对象数量 */
  culledObjects: number;
  /** 剔除率 */
  cullingRate: number;
  /** 剔除时间（毫秒） */
  cullingTime: number;
  /** 渲染时间（毫秒） */
  renderTime: number;
  /** 总时间（毫秒） */
  totalTime: number;
  /** 算法类型 */
  algorithm: OcclusionCullingAlgorithm;
  /** 时间戳 */
  timestamp: number;
}

/**
 * 增强的遮挡剔除系统
 * 提供高效的遮挡剔除算法，减少不可见对象的渲染
 */
export class EnhancedOcclusionCullingSystem extends OcclusionCullingSystem {
  /** 系统类型 */
  public static readonly TYPE: string = 'EnhancedOcclusionCullingSystem';

  /** 是否使用自适应算法选择 */
  private useAdaptiveAlgorithm: boolean;
  /** 是否使用多级遮挡剔除 */
  private useMultiLevelCulling: boolean;
  /** 是否使用预测剔除 */
  private usePredictiveCulling: boolean;
  /** 是否使用时间一致性 */
  private useTemporalCoherence: boolean;
  /** 是否使用GPU加速 */
  private useGPUAcceleration: boolean;
  /** 是否使用保守剔除 */
  private useConservativeCulling: boolean;
  /** 是否收集性能统计 */
  private collectStats: boolean;
  /** 是否使用自动优化 */
  private useAutoOptimization: boolean;
  /** 自动优化间隔（毫秒） */
  private autoOptimizationInterval: number;
  /** 上次自动优化时间 */
  private lastAutoOptimizationTime: number;
  /** 性能统计历史 */
  private statsHistory: OcclusionCullingStats[];
  /** 最大历史记录数 */
  private maxHistoryLength: number;
  /** 上一帧剔除的实体 */
  private previousCulledEntities: Set<Entity>;
  /** 预测的相机位置 */
  private predictedCameraPosition: THREE.Vector3;
  /** 相机速度 */
  private cameraVelocity: THREE.Vector3;
  /** 上一帧相机位置 */
  private previousCameraPosition: THREE.Vector3;
  /** 事件发射器 */
  private eventEmitter: EventEmitter;
  /** 层次Z缓冲区 */
  private hierarchicalZBuffer: Float32Array | null;
  /** 层次Z缓冲区宽度 */
  private hzbWidth: number;
  /** 层次Z缓冲区高度 */
  private hzbHeight: number;
  /** 层次Z缓冲区级别数 */
  private hzbLevels: number;

  /**
   * 创建增强的遮挡剔除系统
   * @param options 系统选项
   */
  constructor(options: EnhancedOcclusionCullingSystemOptions = {}) {
    super(options);

    // 设置增强选项
    this.useAdaptiveAlgorithm = options.useAdaptiveAlgorithm !== undefined ? options.useAdaptiveAlgorithm : true;
    this.useMultiLevelCulling = options.useMultiLevelCulling !== undefined ? options.useMultiLevelCulling : true;
    this.usePredictiveCulling = options.usePredictiveCulling !== undefined ? options.usePredictiveCulling : true;
    this.useTemporalCoherence = options.useTemporalCoherence !== undefined ? options.useTemporalCoherence : true;
    this.useGPUAcceleration = options.useGPUAcceleration !== undefined ? options.useGPUAcceleration : true;
    this.useConservativeCulling = options.useConservativeCulling !== undefined ? options.useConservativeCulling : false;
    this.collectStats = options.collectStats !== undefined ? options.collectStats : true;
    this.useAutoOptimization = options.useAutoOptimization !== undefined ? options.useAutoOptimization : true;
    this.autoOptimizationInterval = options.autoOptimizationInterval || 5000;
    this.lastAutoOptimizationTime = 0;
    this.statsHistory = [];
    this.maxHistoryLength = 100;
    this.previousCulledEntities = new Set<Entity>();
    this.predictedCameraPosition = new THREE.Vector3();
    this.cameraVelocity = new THREE.Vector3();
    this.previousCameraPosition = new THREE.Vector3();
    this.eventEmitter = new EventEmitter();
    this.hierarchicalZBuffer = null;
    this.hzbWidth = 0;
    this.hzbHeight = 0;
    this.hzbLevels = 0;

    // 如果使用GPU加速，检查是否支持
    if (this.useGPUAcceleration) {
      this.checkGPUSupport();
    }

    // 如果使用层次Z缓冲区算法，初始化层次Z缓冲区
    if (this.algorithm === OcclusionCullingAlgorithm.HIERARCHICAL_Z_BUFFER) {
      this.initializeHierarchicalZBuffer();
    }
  }

  /**
   * 获取系统类型
   * @returns 系统类型
   */
  public getType(): string {
    return EnhancedOcclusionCullingSystem.TYPE;
  }

  /**
   * 初始化系统
   */
  public initialize(): void {
    super.initialize();

    // 如果使用自适应算法选择，选择最佳算法
    if (this.useAdaptiveAlgorithm) {
      this.selectBestAlgorithm();
    }
  }

  /**
   * 更新系统
   * @param deltaTime 帧间隔时间（秒）
   */
  public update(deltaTime: number): void {
    if (!this.enabled) {
      return;
    }

    // 获取相机和场景
    const camera = this.getCamera();
    const scene = this.getScene();

    if (!camera || !scene) {
      return;
    }

    // 记录开始时间
    const startTime = performance.now();

    // 更新相机信息
    this.updateCameraInfo(camera, deltaTime);

    // 如果使用自适应算法选择，定期选择最佳算法
    if (this.useAutoOptimization && performance.now() - this.lastAutoOptimizationTime > this.autoOptimizationInterval) {
      this.optimizeSettings();
      this.lastAutoOptimizationTime = performance.now();
    }

    // 如果使用多级遮挡剔除，先进行粗略剔除
    if (this.useMultiLevelCulling) {
      this.performCoarseCulling(camera, scene);
    }

    // 如果使用预测剔除，预测相机位置并进行剔除
    if (this.usePredictiveCulling) {
      this.performPredictiveCulling(camera, scene, deltaTime);
    }

    // 执行主要遮挡剔除
    this.performOcclusionCulling(camera, scene);

    // 记录结束时间
    const endTime = performance.now();
    this.cullingTime = endTime - startTime;

    // 如果收集性能统计，更新统计信息
    if (this.collectStats) {
      this.updateStats(endTime - startTime);
    }

    // 更新调试可视化
    if (this.useDebugVisualization) {
      this.updateDebugVisualization();
    }

    // 保存当前剔除的实体，用于时间一致性
    this.previousCulledEntities = new Set(this.culledEntities);
  }

  /**
   * 更新相机信息
   * @param camera 相机
   * @param deltaTime 帧间隔时间（秒）
   */
  private updateCameraInfo(camera: Camera, deltaTime: number): void {
    // 获取相机位置
    const cameraPosition = camera.getThreeCamera().position;

    // 如果有上一帧相机位置，计算相机速度
    if (this.previousCameraPosition.lengthSq() > 0) {
      this.cameraVelocity.copy(cameraPosition).sub(this.previousCameraPosition);
      if (deltaTime > 0) {
        this.cameraVelocity.divideScalar(deltaTime);
      }
    }

    // 保存当前相机位置
    this.previousCameraPosition.copy(cameraPosition);

    // 预测下一帧相机位置
    this.predictedCameraPosition.copy(cameraPosition).add(this.cameraVelocity.clone().multiplyScalar(deltaTime));
  }

  /**
   * 检查GPU支持
   */
  private checkGPUSupport(): void {
    // 检查是否支持WebGL2
    const canvas = document.createElement('canvas');
    const gl = canvas.getContext('webgl2');

    if (!gl) {
      Debug.warn('EnhancedOcclusionCullingSystem', '不支持WebGL2，已禁用GPU加速');
      this.useGPUAcceleration = false;
      return;
    }

    // 检查是否支持所需的扩展
    const extensions = [
      'EXT_color_buffer_float',
      'OES_texture_float_linear'
    ];

    for (const ext of extensions) {
      if (!gl.getExtension(ext)) {
        Debug.warn('EnhancedOcclusionCullingSystem', `不支持扩展 ${ext}，已禁用GPU加速`);
        this.useGPUAcceleration = false;
        return;
      }
    }
  }

  /**
   * 初始化层次Z缓冲区
   */
  private initializeHierarchicalZBuffer(): void {
    // 获取渲染器
    const renderer = this.getRenderer();
    if (!renderer) {
      return;
    }

    // 获取渲染器大小
    const size = renderer.getSize(new THREE.Vector2());
    this.hzbWidth = size.width;
    this.hzbHeight = size.height;

    // 计算层次Z缓冲区级别数
    this.hzbLevels = Math.floor(Math.log2(Math.max(this.hzbWidth, this.hzbHeight))) + 1;

    // 创建层次Z缓冲区
    const totalSize = this.calculateTotalHZBSize(this.hzbWidth, this.hzbHeight, this.hzbLevels);
    this.hierarchicalZBuffer = new Float32Array(totalSize);

    Debug.log('EnhancedOcclusionCullingSystem', `初始化层次Z缓冲区: ${this.hzbWidth}x${this.hzbHeight}, ${this.hzbLevels}级, ${totalSize}个元素`);
  }

  /**
   * 计算层次Z缓冲区总大小
   * @param width 宽度
   * @param height 高度
   * @param levels 级别数
   * @returns 总大小
   */
  private calculateTotalHZBSize(width: number, height: number, levels: number): number {
    let totalSize = 0;
    let levelWidth = width;
    let levelHeight = height;

    for (let i = 0; i < levels; i++) {
      totalSize += levelWidth * levelHeight;
      levelWidth = Math.max(1, Math.floor(levelWidth / 2));
      levelHeight = Math.max(1, Math.floor(levelHeight / 2));
    }

    return totalSize;
  }

  /**
   * 选择最佳算法
   */
  private selectBestAlgorithm(): void {
    // 获取性能监控器
    const performanceMonitor = PerformanceMonitor.getInstance();
    const gpuPerformance = performanceMonitor.getGPUUsage();
    const cpuPerformance = performanceMonitor.getCPUUsage();

    // 根据性能选择最佳算法
    let bestAlgorithm: OcclusionCullingAlgorithm;

    if (this.useGPUAcceleration && gpuPerformance < 0.7) {
      // 如果GPU性能良好且支持GPU加速，使用硬件遮挡查询
      bestAlgorithm = OcclusionCullingAlgorithm.HARDWARE_OCCLUSION_QUERY;
    } else if (cpuPerformance < 0.7) {
      // 如果CPU性能良好，使用层次Z缓冲区
      bestAlgorithm = OcclusionCullingAlgorithm.HIERARCHICAL_Z_BUFFER;
    } else {
      // 如果性能不佳，使用简单的遮挡查询
      bestAlgorithm = OcclusionCullingAlgorithm.OCCLUSION_QUERY;
    }

    // 如果算法发生变化，更新算法
    if (bestAlgorithm !== this.algorithm) {
      const oldAlgorithm = this.algorithm;
      this.algorithm = bestAlgorithm;

      // 如果新算法是层次Z缓冲区，初始化层次Z缓冲区
      if (bestAlgorithm === OcclusionCullingAlgorithm.HIERARCHICAL_Z_BUFFER && !this.hierarchicalZBuffer) {
        this.initializeHierarchicalZBuffer();
      }

      // 发出算法变更事件
      this.eventEmitter.emit(EnhancedOcclusionCullingSystemEventType.ALGORITHM_CHANGED, bestAlgorithm, oldAlgorithm);
      Debug.log('EnhancedOcclusionCullingSystem', `算法变更: ${oldAlgorithm} -> ${bestAlgorithm}`);
    }
  }

  /**
   * 优化设置
   */
  private optimizeSettings(): void {
    // 获取性能监控器
    const performanceMonitor = PerformanceMonitor.getInstance();
    const fps = performanceMonitor.getFPS();
    const targetFPS = 60;

    // 如果FPS低于目标，尝试优化设置
    if (fps < targetFPS * 0.8) {
      // 如果FPS非常低，禁用一些高级功能
      if (fps < targetFPS * 0.5) {
        this.useMultiLevelCulling = false;
        this.usePredictiveCulling = false;
        this.useTemporalCoherence = true;
        this.useConservativeCulling = true;
      } else {
        // 否则，只禁用预测剔除
        this.usePredictiveCulling = false;
        this.useMultiLevelCulling = true;
        this.useTemporalCoherence = true;
        this.useConservativeCulling = false;
      }

      // 选择最佳算法
      this.selectBestAlgorithm();
    } else if (fps > targetFPS * 1.2) {
      // 如果FPS很高，可以启用更多高级功能
      this.useMultiLevelCulling = true;
      this.usePredictiveCulling = true;
      this.useTemporalCoherence = true;
      this.useConservativeCulling = false;
    }
  }

  /**
   * 执行粗略剔除
   * @param camera 相机
   * @param scene 场景
   */
  private performCoarseCulling(camera: Camera, scene: Scene): void {
    // 使用八叉树或其他空间分区结构进行粗略剔除
    if (this.octree) {
      // 获取相机视锥体
      const frustum = new THREE.Frustum();
      const projScreenMatrix = new THREE.Matrix4();
      projScreenMatrix.multiplyMatrices(
        camera.getThreeCamera().projectionMatrix,
        camera.getThreeCamera().matrixWorldInverse
      );
      frustum.setFromProjectionMatrix(projScreenMatrix);

      // 使用八叉树进行视锥体剔除
      const visibleNodes = this.octree.getFrustumIntersectedNodes(frustum);

      // 标记不在视锥体内的实体为已剔除
      for (const [entity, component] of this.cullableComponents.entries()) {
        const transform = entity.getComponent('Transform') as any as any as any as Transform;
        if (!transform) {
          continue;
        }

        const position = transform.getWorldPosition(new THREE.Vector3());
        const node = this.octree.getNodeContainingPosition(position);

        if (node && !visibleNodes.includes(node)) {
          this.cullEntity(entity);
        }
      }
    }
  }

  /**
   * 执行预测剔除
   * @param camera 相机
   * @param scene 场景
   * @param deltaTime 帧间隔时间（秒）
   */
  private performPredictiveCulling(camera: Camera, scene: Scene, deltaTime: number): void {
    // 使用预测的相机位置进行剔除
    const predictedCamera = camera.clone();
    predictedCamera.getThreeCamera().position.copy(this.predictedCameraPosition);

    // 更新预测相机的视图矩阵
    predictedCamera.getThreeCamera().updateMatrixWorld();

    // 使用预测相机进行剔除
    this.performOcclusionCulling(predictedCamera, scene);
  }

  /**
   * 更新统计信息
   * @param cullingTime 剔除时间（毫秒）
   */
  private updateStats(cullingTime: number): void {
    // 计算统计信息
    const totalObjects = this.cullableComponents.size;
    const culledObjects = this.culledEntities.size;
    const cullingRate = totalObjects > 0 ? culledObjects / totalObjects : 0;
    const renderTime = PerformanceMonitor.getInstance().getLastFrameTime() - cullingTime;
    const totalTime = cullingTime + renderTime;

    // 创建统计对象
    const stats: OcclusionCullingStats = {
      totalObjects,
      culledObjects,
      cullingRate,
      cullingTime,
      renderTime,
      totalTime,
      algorithm: this.algorithm,
      timestamp: Date.now()
    };

    // 添加到历史记录
    this.statsHistory.push(stats);

    // 限制历史记录长度
    if (this.statsHistory.length > this.maxHistoryLength) {
      this.statsHistory.shift();
    }

    // 发出统计更新事件
    this.eventEmitter.emit(EnhancedOcclusionCullingSystemEventType.STATS_UPDATED, stats);
  }

  /**
   * 获取统计历史
   * @returns 统计历史
   */
  public getStatsHistory(): OcclusionCullingStats[] {
    return this.statsHistory;
  }

  /**
   * 获取最新统计
   * @returns 最新统计
   */
  public getLatestStats(): OcclusionCullingStats | null {
    if (this.statsHistory.length === 0) {
      return null;
    }
    return this.statsHistory[this.statsHistory.length - 1];
  }

  /**
   * 添加事件监听器
   * @param type 事件类型
   * @param listener 监听器
   */
  public addEventListener(type: string, listener: Function): void {
    this.eventEmitter.on(type, listener);
  }

  /**
   * 移除事件监听器
   * @param type 事件类型
   * @param listener 监听器
   */
  public removeEventListener(type: string, listener: Function): void {
    this.eventEmitter.off(type, listener);
  }
}
