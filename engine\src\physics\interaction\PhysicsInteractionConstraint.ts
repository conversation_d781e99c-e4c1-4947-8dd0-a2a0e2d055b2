/**
 * 物理交互约束
 * 用于实现角色与物理对象之间的约束关系
 */
import * as THREE from 'three';
import * as CANNON from 'cannon-es';
import type { Entity } from '../../core/Entity';
import { PhysicsSystem } from '../PhysicsSystem';
import { type PhysicsBody  } from '../PhysicsBody';
import { Debug } from '../../utils/Debug';

/**
 * 物理交互约束配置
 */
export interface PhysicsInteractionConstraintConfig {
  /** 约束类型 */
  constraintType?: string;
  /** 约束参数 */
  constraintParams?: any;
  /** 是否使用弹簧约束 */
  useSpring?: boolean;
  /** 弹簧刚度 */
  springStiffness?: number;
  /** 弹簧阻尼 */
  springDamping?: number;
  /** 最大约束力 */
  maxForce?: number;
  /** 碰撞组 */
  collisionGroup?: number;
  /** 碰撞掩码 */
  collisionMask?: number;
}

/**
 * 物理交互约束
 * 用于实现角色与物理对象之间的约束关系
 */
export class PhysicsInteractionConstraint {
  /** 角色实体 */
  private characterEntity: Entity;

  /** 目标实体 */
  private targetEntity: Entity;

  /** 交互类型 */
  private interactionType: string;

  /** 物理系统 */
  private physicsSystem: PhysicsSystem;

  /** 约束对象 */
  private constraint: CANNON.Constraint | null = null;

  /** 弹簧对象（如果使用弹簧） */
  private spring: CANNON.Spring | null = null;

  /** 约束参数 */
  private constraintParams: any;

  /** 是否使用弹簧约束 */
  private useSpring: boolean;

  /** 弹簧刚度 */
  private springStiffness: number;

  /** 弹簧阻尼 */
  private springDamping: number;

  /** 最大约束力 */
  private maxForce: number;

  /** 碰撞组 */
  private collisionGroup: number;

  /** 碰撞掩码 */
  private collisionMask: number;

  /** 是否已初始化 */
  private initialized: boolean = false;

  /** 是否已销毁 */
  private destroyed: boolean = false;

  /**
   * 创建物理交互约束
   * @param characterEntity 角色实体
   * @param targetEntity 目标实体
   * @param interactionType 交互类型
   * @param physicsSystem 物理系统
   * @param config 约束配置
   */
  constructor(
    characterEntity: Entity,
    targetEntity: Entity,
    interactionType: string,
    physicsSystem: PhysicsSystem,
    config: PhysicsInteractionConstraintConfig = {}
  ) {
    this.characterEntity = characterEntity;
    this.targetEntity = targetEntity;
    this.interactionType = interactionType;
    this.physicsSystem = physicsSystem;

    // 设置默认配置
    this.constraintParams = config.constraintParams || {};
    this.useSpring = config.useSpring !== undefined ? config.useSpring : true;
    this.springStiffness = config.springStiffness || 50.0;
    this.springDamping = config.springDamping || 1.0;
    this.maxForce = config.maxForce || 1000.0;
    this.collisionGroup = config.collisionGroup || 1;
    this.collisionMask = config.collisionMask || -1;
  }

  /**
   * 初始化约束
   * @returns 是否成功初始化
   */
  public initialize(): boolean {
    if (this.initialized || this.destroyed) {
      return false;
    }

    // 获取角色物理体
    const characterBody = this.characterEntity.getComponent<PhysicsBody>(PhysicsBody.type);
    if (!characterBody) {
      Debug.warn('PhysicsInteractionConstraint', `角色实体 ${this.characterEntity.id} 没有物理体组件`);
      return false;
    }

    // 获取目标物理体
    const targetBody = this.targetEntity.getComponent<PhysicsBody>(PhysicsBody.type);
    if (!targetBody) {
      Debug.warn('PhysicsInteractionConstraint', `目标实体 ${this.targetEntity.id} 没有物理体组件`);
      return false;
    }

    // 获取CANNON物理体
    const characterCannonBody = characterBody.getCannonBody();
    const targetCannonBody = targetBody.getCannonBody();
    if (!characterCannonBody || !targetCannonBody) {
      Debug.warn('PhysicsInteractionConstraint', '无法获取CANNON物理体');
      return false;
    }

    // 根据交互类型创建不同的约束
    switch (this.interactionType) {
      case 'push':
        // 推动不需要约束
        this.initialized = true;
        return true;
      case 'pull':
        return this.createPullConstraint(characterCannonBody, targetCannonBody);
      case 'lift':
        return this.createLiftConstraint(characterCannonBody, targetCannonBody);
      case 'throw':
        // 投掷不需要约束
        this.initialized = true;
        return true;
      case 'climb':
        return this.createClimbConstraint(characterCannonBody, targetCannonBody);
      case 'hang':
        return this.createHangConstraint(characterCannonBody, targetCannonBody);
      default:
        Debug.warn('PhysicsInteractionConstraint', `未知的交互类型: ${this.interactionType}`);
        return false;
    }
  }

  /**
   * 创建拉动约束
   * @param characterBody 角色物理体
   * @param targetBody 目标物理体
   * @returns 是否成功创建
   */
  private createPullConstraint(characterBody: CANNON.Body, targetBody: CANNON.Body): boolean {
    try {
      // 获取角色位置
      const characterPosition = new THREE.Vector3(
        (characterBody as any).getPosition().x,
        (characterBody as any).getPosition().y,
        (characterBody as any).getPosition().z
      );

      // 获取目标位置
      const targetPosition = new THREE.Vector3(
        (targetBody as any).getPosition().x,
        (targetBody as any).getPosition().y,
        (targetBody as any).getPosition().z
      );

      // 计算方向向量
      const direction = new THREE.Vector3().subVectors(targetPosition, characterPosition).normalize();

      // 计算约束点（角色前方）
      const characterPivot = new CANNON.Vec3(direction.x, direction.y, direction.z).scale(0.5);

      // 计算目标约束点（目标中心）
      const targetPivot = new CANNON.Vec3(0, 0, 0);

      // 创建约束
      if (this.useSpring) {
        // 创建弹簧约束
        this.spring = new CANNON.Spring(characterBody, targetBody, {
          localAnchorA: characterPivot,
          localAnchorB: targetPivot,
          restLength: 1.0,
          stiffness: this.springStiffness,
          damping: this.springDamping
        });

        // 弹簧需要手动添加到物理世界的步进循环中
        // 这里我们创建一个点对点约束作为替代
        Debug.warn('PhysicsInteractionConstraint', '使用点对点约束代替弹簧约束');

        this.constraint = new CANNON.PointToPointConstraint(
          characterBody,
          characterPivot,
          targetBody,
          targetPivot,
          this.maxForce
        );

        // 添加到物理世界
        this.physicsSystem.getPhysicsWorld().addConstraint(this.constraint);
      } else {
        // 创建点对点约束
        this.constraint = new CANNON.PointToPointConstraint(
          characterBody,
          characterPivot,
          targetBody,
          targetPivot,
          this.maxForce
        );

        // 添加到物理世界
        this.physicsSystem.getPhysicsWorld().addConstraint(this.constraint);
      }

      this.initialized = true;
      return true;
    } catch (error) {
      Debug.error('PhysicsInteractionConstraint', `创建拉动约束失败: ${error}`);
      return false;
    }
  }

  /**
   * 创建举起约束
   * @param characterBody 角色物理体
   * @param targetBody 目标物理体
   * @returns 是否成功创建
   */
  private createLiftConstraint(characterBody: CANNON.Body, targetBody: CANNON.Body): boolean {
    try {
      // 计算角色前方和上方的位置
      const characterForward = new THREE.Vector3(0, 0, -1).applyQuaternion(
        new THREE.Quaternion(
          characterBody.quaternion.x,
          characterBody.quaternion.y,
          characterBody.quaternion.z,
          characterBody.quaternion.w
        )
      );

      // 计算约束点（角色前方和上方）
      const characterPivot = new CANNON.Vec3(
        characterForward.x * 0.5,
        1.0,
        characterForward.z * 0.5
      );

      // 计算目标约束点（目标中心）
      const targetPivot = new CANNON.Vec3(0, 0, 0);

      // 创建约束
      if (this.useSpring) {
        // 创建弹簧约束
        this.spring = new CANNON.Spring(characterBody, targetBody, {
          localAnchorA: characterPivot,
          localAnchorB: targetPivot,
          restLength: 0.1,
          stiffness: this.springStiffness * 2,
          damping: this.springDamping * 2
        });

        // 弹簧需要手动添加到物理世界的步进循环中
        // 这里我们创建一个点对点约束作为替代
        Debug.warn('PhysicsInteractionConstraint', '使用点对点约束代替弹簧约束');

        this.constraint = new CANNON.PointToPointConstraint(
          characterBody,
          characterPivot,
          targetBody,
          targetPivot,
          this.maxForce
        );

        // 添加到物理世界
        this.physicsSystem.getPhysicsWorld().addConstraint(this.constraint);
      } else {
        // 创建点对点约束
        this.constraint = new CANNON.PointToPointConstraint(
          characterBody,
          characterPivot,
          targetBody,
          targetPivot,
          this.maxForce
        );

        // 添加到物理世界
        this.physicsSystem.getPhysicsWorld().addConstraint(this.constraint);
      }

      // 修改目标物理体的碰撞组和掩码
      const originalGroup = targetBody.collisionFilterGroup;
      const originalMask = targetBody.collisionFilterMask;

      targetBody.collisionFilterGroup = this.collisionGroup;
      targetBody.collisionFilterMask = this.collisionMask;

      // 存储原始值，以便在销毁时恢复
      this.constraintParams = {
        originalGroup,
        originalMask
      };

      this.initialized = true;
      return true;
    } catch (error) {
      Debug.error('PhysicsInteractionConstraint', `创建举起约束失败: ${error}`);
      return false;
    }
  }

  /**
   * 创建攀爬约束
   * @param characterBody 角色物理体
   * @param targetBody 目标物理体
   * @returns 是否成功创建
   */
  private createClimbConstraint(characterBody: CANNON.Body, targetBody: CANNON.Body): boolean {
    try {
      // 创建锁定约束（攀爬时角色和目标锁定在一起）
      this.constraint = new CANNON.LockConstraint(characterBody, targetBody, {
        maxForce: this.maxForce
      });

      // 添加到物理世界
      this.physicsSystem.getPhysicsWorld().addConstraint(this.constraint);

      this.initialized = true;
      return true;
    } catch (error) {
      Debug.error('PhysicsInteractionConstraint', `创建攀爬约束失败: ${error}`);
      return false;
    }
  }

  /**
   * 创建悬挂约束
   * @param characterBody 角色物理体
   * @param targetBody 目标物理体
   * @returns 是否成功创建
   */
  private createHangConstraint(characterBody: CANNON.Body, targetBody: CANNON.Body): boolean {
    try {
      // 获取角色位置
      const characterPosition = new THREE.Vector3(
        (characterBody as any).getPosition().x,
        (characterBody as any).getPosition().y,
        (characterBody as any).getPosition().z
      );

      // 获取目标位置
      const targetPosition = new THREE.Vector3(
        (targetBody as any).getPosition().x,
        (targetBody as any).getPosition().y,
        (targetBody as any).getPosition().z
      );

      // 计算方向向量
      const direction = new THREE.Vector3().subVectors(targetPosition, characterPosition).normalize();

      // 计算约束点（角色上方）
      const characterPivot = new CANNON.Vec3(0, 0.5, 0);

      // 计算目标约束点（目标下方）
      const targetPivot = new CANNON.Vec3(0, -0.5, 0);

      // 创建点对点约束
      this.constraint = new CANNON.PointToPointConstraint(
        characterBody,
        characterPivot,
        targetBody,
        targetPivot,
        this.maxForce
      );

      // 添加到物理世界
      this.physicsSystem.getPhysicsWorld().addConstraint(this.constraint);

      this.initialized = true;
      return true;
    } catch (error) {
      Debug.error('PhysicsInteractionConstraint', `创建悬挂约束失败: ${error}`);
      return false;
    }
  }

  /**
   * 更新约束
   * @param deltaTime 帧间隔时间（秒）
   */
  public update(deltaTime: number): void {
    if (!this.initialized || this.destroyed || !this.constraint) {
      return;
    }

    // 根据交互类型更新约束
    switch (this.interactionType) {
      case 'pull':
        this.updatePullConstraint(deltaTime);
        break;
      case 'lift':
        this.updateLiftConstraint(deltaTime);
        break;
      case 'climb':
        this.updateClimbConstraint(deltaTime);
        break;
      case 'hang':
        this.updateHangConstraint(deltaTime);
        break;
    }
  }

  /**
   * 更新拉动约束
   * @param deltaTime 帧间隔时间（秒）
   */
  private updatePullConstraint(deltaTime: number): void {
    // 拉动约束的更新逻辑
  }

  /**
   * 更新举起约束
   * @param deltaTime 帧间隔时间（秒）
   */
  private updateLiftConstraint(deltaTime: number): void {
    // 举起约束的更新逻辑
  }

  /**
   * 更新攀爬约束
   * @param deltaTime 帧间隔时间（秒）
   */
  private updateClimbConstraint(deltaTime: number): void {
    // 攀爬约束的更新逻辑
  }

  /**
   * 更新悬挂约束
   * @param deltaTime 帧间隔时间（秒）
   */
  private updateHangConstraint(deltaTime: number): void {
    // 悬挂约束的更新逻辑
  }

  /**
   * 销毁约束
   */
  public destroy(): void {
    if (this.destroyed || !this.initialized) {
      return;
    }

    // 移除约束
    if (this.constraint) {
      // 移除约束
      this.physicsSystem.getPhysicsWorld().removeConstraint(this.constraint);
      this.constraint = null;
    }

    // 恢复目标物理体的碰撞组和掩码
    if (this.interactionType === 'lift' && this.constraintParams) {
      // 获取目标物理体
      const targetBody = this.targetEntity.getComponent<PhysicsBody>(PhysicsBody.type);
      if (targetBody) {
        const cannonBody = targetBody.getCannonBody();
        if (cannonBody) {
          cannonBody.collisionFilterGroup = this.constraintParams.originalGroup;
          cannonBody.collisionFilterMask = this.constraintParams.originalMask;
        }
      }
    }

    this.destroyed = true;
  }

  /**
   * 获取角色实体
   */
  public getCharacterEntity(): Entity {
    return this.characterEntity;
  }

  /**
   * 获取目标实体
   */
  public getTargetEntity(): Entity {
    return this.targetEntity;
  }

  /**
   * 获取交互类型
   */
  public getInteractionType(): string {
    return this.interactionType;
  }

  /**
   * 获取约束对象
   */
  public getConstraint(): CANNON.Constraint | null {
    return this.constraint;
  }

  /**
   * 是否已初始化
   */
  public isInitialized(): boolean {
    return this.initialized;
  }

  /**
   * 是否已销毁
   */
  public isDestroyed(): boolean {
    return this.destroyed;
  }
}