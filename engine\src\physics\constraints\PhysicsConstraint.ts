/**
 * 物理约束基类
 * 用于创建物理约束
 */
import * as CANNON from 'cannon-es';
import { Component } from '../../core/Component';
import type { Entity } from '../../core/Entity';
import type { PhysicsBody } from '../PhysicsBody';

/**
 * 物理约束类型
 */
export enum ConstraintType {
  /** 点对点约束 */
  POINT_TO_POINT = 'pointToPoint',
  /** 铰链约束 */
  HINGE = 'hinge',
  /** 距离约束 */
  DISTANCE = 'distance',
  /** 锁定约束 */
  LOCK = 'lock',
  /** 弹簧约束 */
  SPRING = 'spring',
  /** 圆锥扭转约束 */
  CONE_TWIST = 'coneTwist',
  /** 滑动约束 */
  SLIDER = 'slider',
  /** 固定约束 */
  FIXED = 'fixed',
  /** 车轮约束 */
  WHEEL = 'wheel'
}

/**
 * 物理约束基类
 */
export abstract class PhysicsConstraint extends Component {
  /** 组件类型 */
  public static readonly type: string = 'PhysicsConstraint';

  /** 约束类型 */
  protected constraintType: ConstraintType;

  /** CANNON.js约束 */
  protected constraint: CANNON.Constraint | null = null;

  /** 物理世界 */
  protected world: CANNON.World | null = null;

  /** 是否已初始化 */
  protected initialized: boolean = false;

  /** 目标实体 */
  protected targetEntity: Entity | null = null;

  /** 碰撞启用 */
  protected collideConnected: boolean = true;

  /** 是否启用 */
  protected enabled: boolean = true;

  /**
   * 创建物理约束
   * @param type 约束类型
   * @param targetEntity 目标实体
   * @param options 约束选项
   */
  constructor(type: ConstraintType, targetEntity: Entity | null = null, options: any = {}) {
    super(PhysicsConstraint.type);

    this.constraintType = type;
    this.targetEntity = targetEntity;
    this.collideConnected = options.collideConnected !== undefined ? options.collideConnected : true;
  }

  /**
   * 初始化约束
   * @param world 物理世界
   */
  public initialize(world: CANNON.World): void {
    if (this.initialized) return;

    this.world = world;

    // 创建约束
    this.createConstraint();

    // 如果创建成功，添加到物理世界
    if (this.constraint) {
      world.addConstraint(this.constraint);
      this.initialized = true;
    }
  }

  /**
   * 创建约束
   * 子类必须实现此方法
   */
  protected abstract createConstraint(): void;

  /**
   * 获取源实体的物理体
   * @returns 源实体的物理体
   */
  protected getSourceBody(): CANNON.Body | null {
    if (!this.entity) return null;

    const physicsBody = this.entity.getComponent<PhysicsBody>('PhysicsBody');
    if (!physicsBody) return null;

    return physicsBody.getCannonBody();
  }

  /**
   * 获取目标实体的物理体
   * @returns 目标实体的物理体
   */
  protected getTargetBody(): CANNON.Body | null {
    if (!this.targetEntity) return null;

    const physicsBody = this.targetEntity.getComponent<PhysicsBody>('PhysicsBody');
    if (!physicsBody) return null;

    return physicsBody.getCannonBody();
  }

  /**
   * 设置目标实体
   * @param entity 目标实体
   */
  public setTargetEntity(entity: Entity | null): void {
    // 如果已初始化，需要重新创建约束
    if (this.initialized && this.constraint && this.world) {
      this.world.removeConstraint(this.constraint);
      this.constraint = null;
      this.initialized = false;
    }

    this.targetEntity = entity;

    // 如果有物理世界，重新初始化
    if (this.world) {
      this.initialize(this.world);
    }
  }

  /**
   * 获取目标实体
   * @returns 目标实体
   */
  public getTargetEntity(): Entity | null {
    return this.targetEntity;
  }

  /**
   * 获取约束类型
   * @returns 约束类型
   */
  public getConstraintType(): ConstraintType {
    return this.constraintType;
  }

  /**
   * 获取CANNON.js约束
   * @returns CANNON.js约束
   */
  public getCannonConstraint(): CANNON.Constraint | null {
    return this.constraint;
  }

  /**
   * 启用约束
   */
  public enable(): void {
    if (!this.enabled && this.constraint && this.world) {
      // CANNON.js约束没有内置的enable方法，我们通过添加到世界来启用
      if (this.world.constraints.indexOf(this.constraint) === -1) {
        this.world.addConstraint(this.constraint);
      }
      this.enabled = true;
    }
  }

  /**
   * 禁用约束
   */
  public disable(): void {
    if (this.enabled && this.constraint && this.world) {
      // CANNON.js约束没有内置的disable方法，我们通过从世界移除来禁用
      if (this.world.constraints.indexOf(this.constraint) !== -1) {
        this.world.removeConstraint(this.constraint);
      }
      this.enabled = false;
    }
  }

  /**
   * 是否启用
   * @returns 是否启用
   */
  public isEnabled(): boolean {
    return this.enabled;
  }

  /**
   * 设置是否允许连接的物体之间碰撞
   * @param collide 是否允许碰撞
   */
  public setCollideConnected(collide: boolean): void {
    this.collideConnected = collide;

    if (this.constraint) {
      this.constraint.collideConnected = collide;
    }
  }

  /**
   * 是否允许连接的物体之间碰撞
   * @returns 是否允许碰撞
   */
  public isCollideConnected(): boolean {
    return this.collideConnected;
  }

  /**
   * 销毁约束
   */
  public dispose(): void {
    if (this.initialized && this.constraint && this.world) {
      this.world.removeConstraint(this.constraint);
      this.constraint = null;
      this.world = null;
      this.initialized = false;
    }

    (super as any).dispose();
  }
}
