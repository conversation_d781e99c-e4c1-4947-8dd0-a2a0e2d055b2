/**
 * 视觉脚本核心节点
 * 提供基本的流程控制和调试节点
 */
import { EventNode } from '../nodes/EventNode';
import { FlowNode } from '../nodes/FlowNode';
import { FunctionNode } from '../nodes/FunctionNode';
import { NodeCategory, NodeType, SocketDirection, SocketType } from '../nodes/Node';
import { NodeRegistry, NodeTypeInfo } from '../nodes/NodeRegistry';

/**
 * 开始事件节点
 * 当视觉脚本开始执行时触发
 */
export class OnStartNode extends EventNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输出流程插槽
    this.addOutput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '当视觉脚本开始执行时触发'
    });
  }
  
  /**
   * 当视觉脚本开始执行时调用
   */
  public onStart(): void {
    // 触发流程
    this.triggerFlow('flow');
  }
}

/**
 * 更新事件节点
 * 每帧更新时触发
 */
export class OnUpdateNode extends EventNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输出流程插槽
    this.addOutput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '每帧更新时触发'
    });
    
    // 添加帧间隔时间输出
    this.addOutput({
      name: 'deltaTime',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'number',
      description: '帧间隔时间（秒）'
    });
  }
  
  /**
   * 当视觉脚本更新时调用
   * @param deltaTime 帧间隔时间（秒）
   */
  public onUpdate(deltaTime: number): void {
    // 设置帧间隔时间输出
    this.setOutputValue('deltaTime', deltaTime);
    
    // 触发流程
    this.triggerFlow('flow');
  }
}

/**
 * 分支节点
 * 根据条件选择执行路径
 */
export class BranchNode extends FlowNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '执行输入'
    });
    
    // 添加条件输入
    this.addInput({
      name: 'condition',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'boolean',
      description: '条件',
      defaultValue: false
    });
    
    // 添加输出流程插槽
    this.addOutput({
      name: 'true',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '条件为真时执行'
    });
    
    this.addOutput({
      name: 'false',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '条件为假时执行'
    });
  }
  
  /**
   * 处理输入并确定输出流程
   * @param inputs 输入值
   * @returns 输出流程名称
   */
  protected process(inputs: Record<string, any>): string | null {
    // 获取条件值
    const condition = inputs.condition === true;
    
    // 根据条件选择输出流程
    return condition ? 'true' : 'false';
  }
}

/**
 * 序列节点
 * 按顺序执行多个流程
 */
export class SequenceNode extends FlowNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '执行输入'
    });
    
    // 添加输出流程插槽
    this.addOutput({
      name: 'flow1',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '第一个执行输出'
    });
    
    this.addOutput({
      name: 'flow2',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '第二个执行输出'
    });
    
    this.addOutput({
      name: 'flow3',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '第三个执行输出'
    });
    
    this.addOutput({
      name: 'completed',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '所有流程执行完成后触发'
    });
  }
  
  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    // 按顺序触发所有流程
    this.triggerFlow('flow1');
    this.triggerFlow('flow2');
    this.triggerFlow('flow3');
    this.triggerFlow('completed');
    
    return 'completed';
  }
}

/**
 * 打印日志节点
 * 在控制台打印日志
 */
export class PrintLogNode extends FlowNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '执行输入'
    });
    
    // 添加消息输入
    this.addInput({
      name: 'message',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'string',
      description: '要打印的消息',
      defaultValue: ''
    });
    
    // 添加日志级别输入
    this.addInput({
      name: 'level',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'string',
      description: '日志级别',
      defaultValue: 'log'
    });
    
    // 添加输出流程插槽
    this.addOutput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '执行输出'
    });
  }
  
  /**
   * 处理输入并确定输出流程
   * @param inputs 输入值
   * @returns 输出流程名称
   */
  protected process(inputs: Record<string, any>): string | null {
    // 获取消息和日志级别
    const message = inputs.message || '';
    const level = inputs.level || 'log';
    
    // 打印日志
    switch (level) {
      case 'warn':
        console.warn(`[VisualScript] ${message}`);
        break;
      case 'error':
        console.error(`[VisualScript] ${message}`);
        break;
      case 'info':
        console.info(`[VisualScript] ${message}`);
        break;
      case 'debug':
        console.debug(`[VisualScript] ${message}`);
        break;
      default:
        console.log(`[VisualScript] ${message}`);
        break;
    }
    
    return 'flow';
  }
}

/**
 * 延时节点
 * 延时执行流程
 */
export class DelayNode extends FlowNode {
  /** 定时器ID */
  private timerId: any = null;
  
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '执行输入'
    });
    
    // 添加延时输入
    this.addInput({
      name: 'seconds',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'number',
      description: '延时时间（秒）',
      defaultValue: 1
    });
    
    // 添加输出流程插槽
    this.addOutput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '延时后执行'
    });
  }
  
  /**
   * 处理输入并确定输出流程
   * @param inputs 输入值
   * @returns 输出流程名称
   */
  protected process(inputs: Record<string, any>): string | null {
    // 获取延时时间
    const seconds = Math.max(0, inputs.seconds || 0);
    
    // 清除之前的定时器
    if (this.timerId !== null) {
      clearTimeout(this.timerId);
      this.timerId = null;
    }
    
    // 设置定时器
    this.timerId = setTimeout(() => {
      this.triggerFlow('flow');
      this.timerId = null;
    }, seconds * 1000);
    
    // 不立即触发输出流程
    return null;
  }
  
  /**
   * 销毁节点
   */
  public dispose(): void {
    // 清除定时器
    if (this.timerId !== null) {
      clearTimeout(this.timerId);
      this.timerId = null;
    }
    
    (super as any).dispose();
  }
}

/**
 * 注册核心节点
 * @param registry 节点注册表
 */
export function registerCoreNodes(registry: NodeRegistry): void {
  // 注册开始事件节点
  registry.registerNodeType({
    type: 'core/events/onStart',
    category: NodeCategory.EVENT,
    constructor: OnStartNode,
    label: '开始',
    description: '当视觉脚本开始执行时触发',
    icon: 'play',
    color: '#4CAF50',
    tags: ['event', 'core', 'lifecycle']
  });
  
  // 注册更新事件节点
  registry.registerNodeType({
    type: 'core/events/onUpdate',
    category: NodeCategory.EVENT,
    constructor: OnUpdateNode,
    label: '更新',
    description: '每帧更新时触发',
    icon: 'update',
    color: '#2196F3',
    tags: ['event', 'core', 'lifecycle']
  });
  
  // 注册分支节点
  registry.registerNodeType({
    type: 'core/flow/branch',
    category: NodeCategory.FLOW,
    constructor: BranchNode,
    label: '分支',
    description: '根据条件选择执行路径',
    icon: 'branch',
    color: '#FF9800',
    tags: ['flow', 'core', 'control']
  });
  
  // 注册序列节点
  registry.registerNodeType({
    type: 'core/flow/sequence',
    category: NodeCategory.FLOW,
    constructor: SequenceNode,
    label: '序列',
    description: '按顺序执行多个流程',
    icon: 'sequence',
    color: '#9C27B0',
    tags: ['flow', 'core', 'control']
  });
  
  // 注册打印日志节点
  registry.registerNodeType({
    type: 'core/debug/print',
    category: NodeCategory.DEBUG,
    constructor: PrintLogNode,
    label: '打印日志',
    description: '在控制台打印日志',
    icon: 'print',
    color: '#F44336',
    tags: ['debug', 'core', 'utility']
  });
  
  // 注册延时节点
  registry.registerNodeType({
    type: 'core/flow/delay',
    category: NodeCategory.FLOW,
    constructor: DelayNode,
    label: '延时',
    description: '延时执行流程',
    icon: 'delay',
    color: '#00BCD4',
    tags: ['flow', 'core', 'time']
  });
}
