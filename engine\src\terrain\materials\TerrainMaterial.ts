/**
 * 地形材质
 * 用于渲染地形
 */
import * as THREE from 'three';
import { TerrainTextureLayer } from '../components/TerrainComponent';
import { terrainVertexShader } from '../shaders/TerrainVertexShader';
import { terrainFragmentShader } from '../shaders/TerrainFragmentShader';

/**
 * 地形材质选项
 */
export interface TerrainMaterialOptions {
  /** 纹理层 */
  layers: TerrainTextureLayer[];
  /** 分辨率 */
  resolution: number;
  /** 宽度 */
  width: number;
  /** 高度 */
  height: number;
  /** 最大高度 */
  maxHeight: number;
}

/**
 * 地形材质
 */
export class TerrainMaterial extends THREE.ShaderMaterial {
  /** 纹理层 */
  private layers: TerrainTextureLayer[];
  /** 分辨率 */
  private resolution: number;
  /** 宽度 */
  private width: number;
  /** 高度 */
  private height: number;
  /** 最大高度 */
  private maxHeight: number;
  /** 混合贴图 */
  private blendMaps: THREE.DataTexture[];
  /** 纹理加载器 */
  private textureLoader: THREE.TextureLoader;

  /**
   * 创建地形材质
   * @param options 选项
   */
  constructor(options: TerrainMaterialOptions) {
    // 创建基础材质
    super({
      vertexShader: terrainVertexShader,
      fragmentShader: terrainFragmentShader,
      uniforms: {
        // 基础参数
        uTerrainSize: { value: new THREE.Vector2(options.width, options.height) },
        uMaxHeight: { value: options.maxHeight },
        
        // 纹理参数
        uLayerCount: { value: 0 },
        uTextures: { value: [] },
        uNormalMaps: { value: [] },
        uRoughnessMaps: { value: [] },
        uDisplacementMaps: { value: [] },
        uAOMaps: { value: [] },
        uTilingFactors: { value: [] },
        uMinHeights: { value: [] },
        uMaxHeights: { value: [] },
        uMinSlopes: { value: [] },
        uMaxSlopes: { value: [] },
        
        // 混合参数
        uBlendMaps: { value: [] },
        uUseBlendMaps: { value: false },
        
        // 光照参数
        uLightPosition: { value: new THREE.Vector3(0, 1000, 0) },
        uLightColor: { value: new THREE.Color(1, 1, 1) },
        uAmbientColor: { value: new THREE.Color(0.2, 0.2, 0.2) },
        
        // 雾参数
        uFogColor: { value: new THREE.Color(0.5, 0.6, 0.7) },
        uFogNear: { value: 500 },
        uFogFar: { value: 2000 },
        uUseFog: { value: true }
      },
      lights: true,
      fog: true
    });

    // 保存参数
    this.layers = options.layers;
    this.resolution = options.resolution;
    this.width = options.width;
    this.height = options.height;
    this.maxHeight = options.maxHeight;
    this.blendMaps = [];
    this.textureLoader = new THREE.TextureLoader();

    // 初始化材质
    this.initializeMaterial();
  }

  /**
   * 初始化材质
   */
  private initializeMaterial(): void {
    // 设置材质属性
    this.side = THREE.DoubleSide;
    this.transparent = false;
    this.needsUpdate = true;

    // 加载纹理
    this.loadTextures();

    // 创建混合贴图
    this.createBlendMaps();
  }

  /**
   * 加载纹理
   */
  private loadTextures(): void {
    const textures: THREE.Texture[] = [];
    const normalMaps: THREE.Texture[] = [];
    const roughnessMaps: THREE.Texture[] = [];
    const displacementMaps: THREE.Texture[] = [];
    const aoMaps: THREE.Texture[] = [];
    const tilingFactors: number[] = [];
    const minHeights: number[] = [];
    const maxHeights: number[] = [];
    const minSlopes: number[] = [];
    const maxSlopes: number[] = [];

    // 加载每层纹理
    for (let i = 0; i < this.layers.length; i++) {
      const layer = this.layers[i];

      // 加载漫反射纹理
      if (typeof layer.texture === 'string') {
        const texture = this.textureLoader.load(layer.texture);
        texture.wrapS = THREE.RepeatWrapping;
        texture.wrapT = THREE.RepeatWrapping;
        textures.push(texture);
      } else {
        textures.push(layer.texture);
      }

      // 加载法线贴图
      if (layer.normalMap) {
        if (typeof layer.normalMap === 'string') {
          const normalMap = this.textureLoader.load(layer.normalMap);
          normalMap.wrapS = THREE.RepeatWrapping;
          normalMap.wrapT = THREE.RepeatWrapping;
          normalMaps.push(normalMap);
        } else {
          normalMaps.push(layer.normalMap);
        }
      } else {
        // 创建默认法线贴图
        const defaultNormalMap = new THREE.Texture();
        defaultNormalMap.image = { width: 1, height: 1 };
        defaultNormalMap.needsUpdate = true;
        normalMaps.push(defaultNormalMap);
      }

      // 加载高光贴图
      if (layer.roughnessMap) {
        if (typeof layer.roughnessMap === 'string') {
          const roughnessMap = this.textureLoader.load(layer.roughnessMap);
          roughnessMap.wrapS = THREE.RepeatWrapping;
          roughnessMap.wrapT = THREE.RepeatWrapping;
          roughnessMaps.push(roughnessMap);
        } else {
          roughnessMaps.push(layer.roughnessMap);
        }
      } else {
        // 创建默认高光贴图
        const defaultRoughnessMap = new THREE.Texture();
        defaultRoughnessMap.image = { width: 1, height: 1 };
        defaultRoughnessMap.needsUpdate = true;
        roughnessMaps.push(defaultRoughnessMap);
      }

      // 加载置换贴图
      if (layer.displacementMap) {
        if (typeof layer.displacementMap === 'string') {
          const displacementMap = this.textureLoader.load(layer.displacementMap);
          displacementMap.wrapS = THREE.RepeatWrapping;
          displacementMap.wrapT = THREE.RepeatWrapping;
          displacementMaps.push(displacementMap);
        } else {
          displacementMaps.push(layer.displacementMap);
        }
      } else {
        // 创建默认置换贴图
        const defaultDisplacementMap = new THREE.Texture();
        defaultDisplacementMap.image = { width: 1, height: 1 };
        defaultDisplacementMap.needsUpdate = true;
        displacementMaps.push(defaultDisplacementMap);
      }

      // 加载环境光遮蔽贴图
      if (layer.aoMap) {
        if (typeof layer.aoMap === 'string') {
          const aoMap = this.textureLoader.load(layer.aoMap);
          aoMap.wrapS = THREE.RepeatWrapping;
          aoMap.wrapT = THREE.RepeatWrapping;
          aoMaps.push(aoMap);
        } else {
          aoMaps.push(layer.aoMap);
        }
      } else {
        // 创建默认环境光遮蔽贴图
        const defaultAOMap = new THREE.Texture();
        defaultAOMap.image = { width: 1, height: 1 };
        defaultAOMap.needsUpdate = true;
        aoMaps.push(defaultAOMap);
      }

      // 设置纹理参数
      tilingFactors.push(layer.tiling || 1);
      minHeights.push(layer.minHeight !== undefined ? layer.minHeight / this.maxHeight : 0);
      maxHeights.push(layer.maxHeight !== undefined ? layer.maxHeight / this.maxHeight : 1);
      minSlopes.push(layer.minSlope !== undefined ? layer.minSlope : 0);
      maxSlopes.push(layer.maxSlope !== undefined ? layer.maxSlope : 90);
    }

    // 更新着色器参数
    this.uniforms.uLayerCount.value = this.layers.length;
    this.uniforms.uTextures.value = textures;
    this.uniforms.uNormalMaps.value = normalMaps;
    this.uniforms.uRoughnessMaps.value = roughnessMaps;
    this.uniforms.uDisplacementMaps.value = displacementMaps;
    this.uniforms.uAOMaps.value = aoMaps;
    this.uniforms.uTilingFactors.value = tilingFactors;
    this.uniforms.uMinHeights.value = minHeights;
    this.uniforms.uMaxHeights.value = maxHeights;
    this.uniforms.uMinSlopes.value = minSlopes;
    this.uniforms.uMaxSlopes.value = maxSlopes;
  }

  /**
   * 创建混合贴图
   */
  private createBlendMaps(): void {
    // 创建默认混合贴图
    const blendMapSize = this.resolution;
    const blendMapData = new Uint8Array(blendMapSize * blendMapSize * 4);
    
    // 初始化混合贴图
    for (let i = 0; i < this.layers.length; i++) {
      // 创建混合贴图
      const blendMap = new THREE.DataTexture(
        blendMapData.slice(),
        blendMapSize,
        blendMapSize,
        THREE.RGBAFormat
      );
      blendMap.needsUpdate = true;
      this.blendMaps.push(blendMap);
    }
    
    // 更新着色器参数
    this.uniforms.uBlendMaps.value = this.blendMaps;
    this.uniforms.uUseBlendMaps.value = this.blendMaps.length > 0;
  }

  /**
   * 更新混合贴图
   * @param layerIndex 层索引
   * @param x X坐标
   * @param y Y坐标
   * @param radius 半径
   * @param strength 强度
   * @param falloff 衰减
   */
  public updateBlendMap(layerIndex: number, x: number, y: number, radius: number, strength: number, falloff: number): void {
    if (layerIndex < 0 || layerIndex >= this.blendMaps.length) {
      return;
    }
    
    const blendMap = this.blendMaps[layerIndex];
    const blendMapSize = this.resolution;
    const blendMapData = blendMap.image.data as Uint8Array;
    
    // 计算笔刷范围
    const centerX = Math.floor(((x + this.width / 2) / this.width) * blendMapSize);
    const centerY = Math.floor(((y + this.height / 2) / this.height) * blendMapSize);
    const brushRadius = (radius / this.width) * blendMapSize;
    
    const minX = Math.max(0, Math.floor(centerX - brushRadius));
    const maxX = Math.min(blendMapSize - 1, Math.ceil(centerX + brushRadius));
    const minY = Math.max(0, Math.floor(centerY - brushRadius));
    const maxY = Math.min(blendMapSize - 1, Math.ceil(centerY + brushRadius));
    
    // 更新混合贴图
    for (let py = minY; py <= maxY; py++) {
      for (let px = minX; px <= maxX; px++) {
        const distance = Math.sqrt((px - centerX) * (px - centerX) + (py - centerY) * (py - centerY));
        if (distance <= brushRadius) {
          const falloffFactor = Math.pow(1 - distance / brushRadius, falloff);
          const index = (py * blendMapSize + px) * 4;
          
          // 更新混合权重
          blendMapData[index] = Math.min(255, blendMapData[index] + strength * falloffFactor);
          
          // 更新其他层的混合权重
          for (let i = 0; i < this.blendMaps.length; i++) {
            if (i !== layerIndex) {
              const otherBlendMapData = this.blendMaps[i].image.data as Uint8Array;
              otherBlendMapData[index] = Math.max(0, otherBlendMapData[index] - strength * falloffFactor);
            }
          }
        }
      }
    }
    
    // 更新纹理
    blendMap.needsUpdate = true;
    for (let i = 0; i < this.blendMaps.length; i++) {
      if (i !== layerIndex) {
        this.blendMaps[i].needsUpdate = true;
      }
    }
  }

  /**
   * 释放资源
   */
  public dispose(): void {
    // 释放纹理
    if (this.uniforms.uTextures.value) {
      for (const texture of this.uniforms.uTextures.value) {
        (texture as any).dispose();
      }
    }
    
    if (this.uniforms.uNormalMaps.value) {
      for (const normalMap of this.uniforms.uNormalMaps.value) {
        (normalMap as any).dispose();
      }
    }
    
    if (this.uniforms.uRoughnessMaps.value) {
      for (const roughnessMap of this.uniforms.uRoughnessMaps.value) {
        (roughnessMap as any).dispose();
      }
    }
    
    if (this.uniforms.uDisplacementMaps.value) {
      for (const displacementMap of this.uniforms.uDisplacementMaps.value) {
        (displacementMap as any).dispose();
      }
    }
    
    if (this.uniforms.uAOMaps.value) {
      for (const aoMap of this.uniforms.uAOMaps.value) {
        (aoMap as any).dispose();
      }
    }
    
    // 释放混合贴图
    for (const blendMap of this.blendMaps) {
      (blendMap as any).dispose();
    }
    
    (super as any).dispose();
  }
}
