/**
 * 物理碰撞器组件
 * 为实体提供碰撞形状
 */
import * as THREE from 'three';
import * as CANNON from 'cannon-es';
import { Component } from '../core/Component';

export enum ColliderType {
  BOX = 'box',
  SPHERE = 'sphere',
  CYLINDER = 'cylinder',
  CAPSULE = 'capsule',
  CONVEX = 'convex',
  TRIMESH = 'trimesh',
  PLANE = 'plane',
  COMPOUND = 'compound',
}

export interface ColliderOptions {
  /** 碰撞器类型 */
  type: ColliderType;
  /** 碰撞器参数 */
  params?: any;
  /** 碰撞器位置偏移 */
  offset?: { x: number; y: number; z: number };
  /** 碰撞器旋转偏移 */
  rotation?: { x: number; y: number; z: number };
  /** 是否为触发器 */
  isTrigger?: boolean;
}

export class PhysicsCollider extends Component {
  /** 组件类型 */
  public static readonly type: string = 'PhysicsCollider';

  /** 碰撞器类型 */
  private colliderType: ColliderType;

  /** 碰撞器参数 */
  private params: any;

  /** 碰撞器位置偏移 */
  private offset: CANNON.Vec3;

  /** 碰撞器旋转偏移 */
  private rotation: CANNON.Quaternion;

  /** 碰撞形状 */
  private shapes: CANNON.Shape[] = [];

  /** 物理世界 */
  private world: CANNON.World | null = null;

  /** 是否已初始化 */
  private initialized: boolean = false;

  /** 是否为触发器 */
  private trigger: boolean = false;

  /**
   * 创建物理碰撞器组件
   * @param options 碰撞器选项
   */
  constructor(options: ColliderOptions) {
    super(PhysicsCollider.type);

    this.colliderType = options.type;
    this.params = options.params || {};
    this.trigger = options.isTrigger || false;

    // 设置偏移
    const offset = options.offset || { x: 0, y: 0, z: 0 };
    this.offset = new CANNON.Vec3(offset.x, offset.y, offset.z);

    // 设置旋转
    const rotation = options.rotation || { x: 0, y: 0, z: 0 };
    const quaternion = new THREE.Quaternion().setFromEuler(
      new THREE.Euler(rotation.x, rotation.y, rotation.z)
    );
    this.rotation = new CANNON.Quaternion(quaternion.x, quaternion.y, quaternion.z, quaternion.w);
  }

  /**
   * 初始化碰撞器
   * @param world 物理世界
   */
  public initialize(world: CANNON.World): void {
    if (this.initialized) return;

    this.world = world;

    // 创建碰撞形状
    this.createShape();

    this.initialized = true;
  }

  /**
   * 创建碰撞形状
   */
  private createShape(): void {
    let shape: CANNON.Shape | null = null;

    switch (this.colliderType) {
      case ColliderType.BOX:
        shape = this.createBoxShape();
        break;

      case ColliderType.SPHERE:
        shape = this.createSphereShape();
        break;

      case ColliderType.CYLINDER:
        shape = this.createCylinderShape();
        break;

      case ColliderType.CAPSULE:
        shape = this.createCapsuleShape();
        break;

      case ColliderType.CONVEX:
        shape = this.createConvexShape();
        break;

      case ColliderType.TRIMESH:
        shape = this.createTrimeshShape();
        break;

      case ColliderType.PLANE:
        shape = this.createPlaneShape();
        break;

      case ColliderType.COMPOUND:
        this.createCompoundShape();
        return;

      default:
        console.error(`不支持的碰撞器类型: ${this.colliderType}`);
        return;
    }

    if (shape) {
      // 设置形状的位置和旋转（使用类型断言）
      (shape as any).position = this.offset;
      (shape as any).quaternion = this.rotation;

      // 设置触发器属性
      if (this.trigger) {
        shape.collisionResponse = false;
      }

      this.shapes.push(shape);
    }
  }

  /**
   * 创建盒体碰撞形状
   * @returns 盒体碰撞形状
   */
  private createBoxShape(): CANNON.Box {
    const halfExtents = this.params.halfExtents || { x: 0.5, y: 0.5, z: 0.5 };
    return new CANNON.Box(new CANNON.Vec3(halfExtents.x, halfExtents.y, halfExtents.z));
  }

  /**
   * 创建球体碰撞形状
   * @returns 球体碰撞形状
   */
  private createSphereShape(): CANNON.Sphere {
    const radius = this.params.radius || 0.5;
    return new CANNON.Sphere(radius);
  }

  /**
   * 创建圆柱体碰撞形状
   * @returns 圆柱体碰撞形状
   */
  private createCylinderShape(): CANNON.Cylinder {
    const radiusTop = this.params.radiusTop || 0.5;
    const radiusBottom = this.params.radiusBottom || 0.5;
    const height = this.params.height || 1;
    const numSegments = this.params.numSegments || 8;

    return new CANNON.Cylinder(radiusTop, radiusBottom, height, numSegments);
  }

  /**
   * 创建胶囊体碰撞形状
   * @returns 胶囊体碰撞形状（使用复合形状模拟）
   */
  private createCapsuleShape(): CANNON.Shape {
    // CANNON.js没有内置的胶囊体，使用一个圆柱体和两个半球组成的复合形状来模拟
    const radius = this.params.radius || 0.5;
    const height = this.params.height || 1;
    const numSegments = this.params.numSegments || 8;

    // 创建圆柱体
    const cylinder = new CANNON.Cylinder(radius, radius, height, numSegments);

    // 创建两个半球
    const sphere1 = new CANNON.Sphere(radius);
    const sphere2 = new CANNON.Sphere(radius);

    // 创建复合形状（使用类型断言）
    const compound = new (CANNON as any).Compound();

    // 添加圆柱体
    compound.addChild(cylinder, new CANNON.Vec3(0, 0, 0));

    // 添加上半球
    compound.addChild(sphere1, new CANNON.Vec3(0, height / 2, 0));

    // 添加下半球
    compound.addChild(sphere2, new CANNON.Vec3(0, -height / 2, 0));

    return compound;
  }

  /**
   * 创建凸包碰撞形状
   * @returns 凸包碰撞形状
   */
  private createConvexShape(): CANNON.Shape {
    if (!this.params.vertices || !this.params.faces) {
      console.error('创建凸包碰撞形状需要提供顶点和面');
      // 返回默认盒体（使用类型断言）
      return new CANNON.Box(new CANNON.Vec3(0.5, 0.5, 0.5)) as unknown as CANNON.ConvexPolyhedron;
    }

    const vertices = this.params.vertices.map((v: any) => new CANNON.Vec3(v.x, v.y, v.z));
    const faces = this.params.faces;

    return new CANNON.ConvexPolyhedron({ vertices, faces });
  }

  /**
   * 创建三角网格碰撞形状
   * @returns 三角网格碰撞形状
   */
  private createTrimeshShape(): CANNON.Shape {
    if (!this.params.vertices || !this.params.indices) {
      console.error('创建三角网格碰撞形状需要提供顶点和索引');
      // 返回默认盒体（使用类型断言）
      return new CANNON.Box(new CANNON.Vec3(0.5, 0.5, 0.5)) as unknown as CANNON.Trimesh;
    }

    const vertices = this.params.vertices;
    const indices = this.params.indices;

    // 将 TypedArray 转换为普通数组
    const verticesArray = Array.isArray(vertices) ? vertices : Array.from(vertices);
    const indicesArray = Array.isArray(indices) ? indices : Array.from(indices);

    return new CANNON.Trimesh(verticesArray, indicesArray);
  }

  /**
   * 创建平面碰撞形状
   * @returns 平面碰撞形状
   */
  private createPlaneShape(): CANNON.Plane {
    return new CANNON.Plane();
  }

  /**
   * 创建复合碰撞形状
   */
  private createCompoundShape(): void {
    if (!this.params.shapes || !Array.isArray(this.params.shapes)) {
      console.error('创建复合碰撞形状需要提供形状数组');
      this.shapes.push(new CANNON.Box(new CANNON.Vec3(0.5, 0.5, 0.5))); // 添加默认盒体
      return;
    }

    for (const shapeInfo of this.params.shapes) {
      const collider = new PhysicsCollider(shapeInfo);
      collider.initialize(this.world!);

      const shapes = collider.getShapes();
      this.shapes.push(...shapes);
    }
  }

  /**
   * 从网格创建碰撞形状
   * @param mesh Three.js网格
   * @returns 碰撞形状
   */
  public static createFromMesh(mesh: THREE.Mesh): PhysicsCollider {
    if (!mesh.geometry) {
      console.error('网格没有几何体');
      return new PhysicsCollider({ type: ColliderType.BOX });
    }

    // 获取包围盒
    const boundingBox = new THREE.Box3().setFromObject(mesh);
    const size = new THREE.Vector3();
    boundingBox.getSize(size);

    // 根据几何体类型选择合适的碰撞器
    if (mesh.geometry instanceof THREE.BoxGeometry) {
      return new PhysicsCollider({
        type: ColliderType.BOX,
        params: {
          halfExtents: { x: size.x / 2, y: size.y / 2, z: size.z / 2 }
        }
      });
    } else if (mesh.geometry instanceof THREE.SphereGeometry) {
      return new PhysicsCollider({
        type: ColliderType.SPHERE,
        params: {
          radius: Math.max(size.x, size.y, size.z) / 2
        }
      });
    } else if (mesh.geometry instanceof THREE.CylinderGeometry) {
      return new PhysicsCollider({
        type: ColliderType.CYLINDER,
        params: {
          radiusTop: size.x / 2,
          radiusBottom: size.x / 2,
          height: size.y
        }
      });
    } else {
      // 对于复杂几何体，使用凸包或三角网格
      // 这里简化处理，使用包围盒
      return new PhysicsCollider({
        type: ColliderType.BOX,
        params: {
          halfExtents: { x: size.x / 2, y: size.y / 2, z: size.z / 2 }
        }
      });
    }
  }

  /**
   * 获取碰撞形状
   * @returns 碰撞形状数组
   */
  public getShapes(): CANNON.Shape[] {
    return this.shapes;
  }

  /**
   * 获取碰撞器类型
   * @returns 碰撞器类型
   */
  public getColliderType(): ColliderType {
    return this.colliderType;
  }

  /**
   * 获取碰撞器参数
   * @returns 碰撞器参数
   */
  public getParams(): any {
    return this.params;
  }

  /**
   * 获取碰撞器位置偏移
   * @returns 碰撞器位置偏移
   */
  public getOffset(): CANNON.Vec3 {
    return this.offset;
  }

  /**
   * 获取碰撞器旋转偏移
   * @returns 碰撞器旋转偏移
   */
  public getRotation(): CANNON.Quaternion {
    return this.rotation;
  }

  /**
   * 检查是否为触发器
   * @returns 是否为触发器
   */
  public isTrigger(): boolean {
    return this.trigger;
  }

  /**
   * 设置是否为触发器
   * @param isTrigger 是否为触发器
   */
  public setTrigger(isTrigger: boolean): void {
    this.trigger = isTrigger;

    // 更新所有形状的碰撞响应
    for (const shape of this.shapes) {
      shape.collisionResponse = !isTrigger;
    }
  }

  /**
   * 销毁碰撞器
   */
  public dispose(): void {
    this.shapes = [];
    this.world = null;
    this.initialized = false;

    // 调用基类的dispose方法
    (super as any).dispose();
  }
}
