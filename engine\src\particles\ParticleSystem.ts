/**
 * 粒子系统
 * 用于创建和管理粒子效果
 */
import * as THREE from 'three';
import { System } from '../core/System';
import type { Entity } from '../core/Entity';
import { ParticleEmitter } from './ParticleEmitter';
import { Particle } from './Particle';

export interface ParticleSystemOptions {
  /** 最大粒子数量 */
  maxParticles?: number;
  /** 是否使用GPU加速 */
  useGPU?: boolean;
  /** 是否启用碰撞 */
  enableCollision?: boolean;
  /** 是否启用物理模拟 */
  enablePhysics?: boolean;
  /** 是否启用粒子排序 */
  enableSorting?: boolean;
}

export class ParticleSystem extends System {
  /** 系统类型 */
  public static readonly type: string = 'ParticleSystem';

  /** 粒子发射器列表 */
  private emitters: ParticleEmitter[] = [];

  /** 最大粒子数量 */
  private maxParticles: number;

  /** 是否使用GPU加速 */
  private useGPU: boolean;

  /** 是否启用碰撞 */
  private enableCollision: boolean;

  /** 是否启用物理模拟 */
  private enablePhysics: boolean;

  /** 是否启用粒子排序 */
  private enableSorting: boolean;

  /** 物理系统引用 */
  private physicsSystem: any = null;

  /** 粒子池 */
  private particlePool: Particle[] = [];

  /** 活跃粒子数量 */
  private activeParticleCount: number = 0;

  /**
   * 创建粒子系统
   * @param options 粒子系统选项
   */
  constructor(options: ParticleSystemOptions = {}) {
    super(2); // 优先级2，在渲染系统之前，物理系统之后更新

    this.maxParticles = options.maxParticles || 10000;
    this.useGPU = options.useGPU !== undefined ? options.useGPU : true;
    this.enableCollision = options.enableCollision || false;
    this.enablePhysics = options.enablePhysics || false;
    this.enableSorting = options.enableSorting || false;

    // 初始化粒子池
    this.initializeParticlePool();
  }

  /**
   * 初始化粒子池
   */
  private initializeParticlePool(): void {
    this.particlePool = [];

    for (let i = 0; i < this.maxParticles; i++) {
      this.particlePool.push(new Particle());
    }
  }

  /**
   * 初始化系统
   */
  public initialize(): void {
    if (this.engine) {
      // 获取物理系统
      if (this.enablePhysics) {
        this.physicsSystem = this.engine.getSystem('PhysicsSystem');
        if (!this.physicsSystem) {
          console.warn('物理系统不可用，粒子物理模拟将被禁用');
          this.enablePhysics = false;
        }
      }

      // 获取世界中的所有实体
      const world = this.engine.getWorld();

      // 查找具有粒子发射器组件的实体
      const entities = world.getAllEntities();
      for (const entity of entities) {
        this.setupEntityParticles(entity);
      }

      // 监听实体创建事件
      world.on('entityCreated', this.handleEntityCreated.bind(this));

      // 监听实体移除事件
      world.on('entityRemoved', this.handleEntityRemoved.bind(this));
    }
  }

  /**
   * 处理实体创建事件
   * @param entity 创建的实体
   */
  private handleEntityCreated(entity: Entity): void {
    this.setupEntityParticles(entity);
  }

  /**
   * 处理实体移除事件
   * @param entity 移除的实体
   */
  private handleEntityRemoved(entity: Entity): void {
    this.removeEntityParticles(entity);
  }

  /**
   * 设置实体的粒子发射器
   * @param entity 实体
   */
  private setupEntityParticles(entity: Entity): void {
    if (!entity) return;

    // 检查实体是否有粒子发射器组件
    const emitter = entity.getComponent<ParticleEmitter>(ParticleEmitter.type);
    if (emitter) {
      // 初始化发射器
      emitter.initialize(this);

      // 添加到发射器列表
      this.emitters.push(emitter);
    }
  }

  /**
   * 移除实体的粒子发射器
   * @param entity 实体
   */
  private removeEntityParticles(entity: Entity): void {
    if (!entity) return;

    // 查找实体的粒子发射器
    const index = this.emitters.findIndex(emitter => emitter.getEntity() === entity);
    if (index !== -1) {
      // 停止发射器
      this.emitters[index].stop();

      // 从列表中移除
      this.emitters.splice(index, 1);
    }
  }

  /**
   * 更新系统
   * @param deltaTime 帧间隔时间（秒）
   */
  public update(deltaTime: number): void {
    // 更新所有发射器
    for (const emitter of this.emitters) {
      // 如果发射器已停止且没有活跃粒子，跳过更新
      if (!emitter.isActive() && emitter.getActiveParticleCount() === 0) {
        continue;
      }

      // 更新发射器
      emitter.update(deltaTime);
    }

    // 更新活跃粒子计数
    this.updateActiveParticleCount();

    // 如果启用排序，对粒子进行排序
    if (this.enableSorting) {
      this.sortParticles();
    }
  }

  /**
   * 更新活跃粒子计数
   */
  private updateActiveParticleCount(): void {
    let count = 0;
    for (const emitter of this.emitters) {
      count += emitter.getActiveParticleCount();
    }
    this.activeParticleCount = count;
  }

  /**
   * 对粒子进行排序
   */
  private sortParticles(): void {
    if (!this.engine) return;

    // 获取相机
    const camera = this.engine.getActiveCamera();
    if (!camera) return;

    // 获取相机位置
    const cameraPosition = camera.getPosition();
    if (!cameraPosition) return;

    const cameraPos = new THREE.Vector3(cameraPosition.x, cameraPosition.y, cameraPosition.z);

    // 对每个发射器的粒子进行排序
    for (const emitter of this.emitters) {
      emitter.sortParticles(cameraPos);
    }
  }

  /**
   * 创建粒子
   * @returns 粒子实例，如果粒子池已满则返回null
   */
  public createParticle(): Particle | null {
    // 检查是否达到最大粒子数量
    if (this.activeParticleCount >= this.maxParticles) {
      return null;
    }

    // 从粒子池中获取一个未使用的粒子
    for (const particle of this.particlePool) {
      if (!particle.active) {
        particle.active = true;
        this.activeParticleCount++;
        return particle;
      }
    }

    return null;
  }

  /**
   * 释放粒子
   * @param particle 要释放的粒子
   */
  public releaseParticle(particle: Particle): void {
    if (particle.active) {
      particle.active = false;
      particle.reset();
      this.activeParticleCount--;
    }
  }

  /**
   * 添加粒子发射器
   * @param emitter 粒子发射器
   */
  public addEmitter(emitter: ParticleEmitter): void {
    // 初始化发射器
    emitter.initialize(this);

    // 添加到发射器列表
    this.emitters.push(emitter);
  }

  /**
   * 移除粒子发射器
   * @param emitter 粒子发射器
   */
  public removeEmitter(emitter: ParticleEmitter): void {
    const index = this.emitters.indexOf(emitter);
    if (index !== -1) {
      // 停止发射器
      emitter.stop();

      // 从列表中移除
      this.emitters.splice(index, 1);
    }
  }

  /**
   * 获取所有粒子发射器
   * @returns 粒子发射器数组
   */
  public getEmitters(): ParticleEmitter[] {
    return [...this.emitters];
  }

  /**
   * 获取活跃粒子数量
   * @returns 活跃粒子数量
   */
  public getActiveParticleCount(): number {
    return this.activeParticleCount;
  }

  /**
   * 获取最大粒子数量
   * @returns 最大粒子数量
   */
  public getMaxParticles(): number {
    return this.maxParticles;
  }

  /**
   * 是否使用GPU加速
   * @returns 是否使用GPU加速
   */
  public isUsingGPU(): boolean {
    return this.useGPU;
  }

  /**
   * 是否启用碰撞
   * @returns 是否启用碰撞
   */
  public isCollisionEnabled(): boolean {
    return this.enableCollision;
  }

  /**
   * 是否启用物理模拟
   * @returns 是否启用物理模拟
   */
  public isPhysicsEnabled(): boolean {
    return this.enablePhysics;
  }

  /**
   * 获取物理系统
   * @returns 物理系统
   */
  public getPhysicsSystem(): any {
    return this.physicsSystem;
  }

  /**
   * 清除所有粒子
   */
  public clearAllParticles(): void {
    for (const emitter of this.emitters) {
      emitter.clearParticles();
    }
    this.activeParticleCount = 0;
  }

  /**
   * 销毁系统
   */
  public dispose(): void {
    // 移除事件监听器
    if (this.engine) {
      const world = this.engine.getWorld();
      world.off('entityCreated', this.handleEntityCreated.bind(this));
      world.off('entityRemoved', this.handleEntityRemoved.bind(this));
    }

    // 停止所有发射器
    for (const emitter of this.emitters) {
      emitter.stop();
      emitter.clearParticles();
    }

    this.emitters = [];
    this.particlePool = [];
    this.activeParticleCount = 0;

    (super as any).dispose();
  }
}
