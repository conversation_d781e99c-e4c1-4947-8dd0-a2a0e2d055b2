/**
 * 水体GPU实例化渲染
 * 使用GPU实例化技术渲染水体特效（如泡沫、水花等）
 */
import * as THREE from 'three';
import { System } from '../../core/System';
import type { Entity } from '../../core/Entity';
import type { World } from '../../core/World';
import type { Camera } from '../Camera';
import { WaterBodyComponent } from '../../physics/water/WaterBodyComponent';
import { Debug } from '../../utils/Debug';
import { PerformanceMonitor } from '../../utils/PerformanceMonitor';
import { EventEmitter } from '../../utils/EventEmitter';

/**
 * 水体特效类型
 */
export enum WaterEffectType {
  /** 泡沫 */
  FOAM = 'foam',
  /** 水花 */
  SPLASH = 'splash',
  /** 水滴 */
  DROPLET = 'droplet',
  /** 波纹 */
  RIPPLE = 'ripple',
  /** 水雾 */
  MIST = 'mist'
}

/**
 * 水体特效实例
 */
export interface WaterEffectInstance {
  /** 唯一ID */
  id: string;
  /** 类型 */
  type: WaterEffectType;
  /** 位置 */
  position: THREE.Vector3;
  /** 旋转 */
  rotation: THREE.Euler;
  /** 缩放 */
  scale: THREE.Vector3;
  /** 颜色 */
  color: THREE.Color;
  /** 不透明度 */
  opacity: number;
  /** 生命周期 */
  lifetime: number;
  /** 已存在时间 */
  age: number;
  /** 是否活跃 */
  active: boolean;
  /** 用户数据 */
  userData: any;
}

/**
 * 水体实例化渲染配置
 */
export interface WaterInstancedRendererConfig {
  /** 是否启用 */
  enabled?: boolean;
  /** 是否自动更新 */
  autoUpdate?: boolean;
  /** 更新频率 */
  updateFrequency?: number;
  /** 最大实例数 */
  maxInstances?: number;
  /** 是否使用调试可视化 */
  useDebugVisualization?: boolean;
  /** 是否使用GPU剔除 */
  useGPUCulling?: boolean;
  /** 是否使用实例化阴影 */
  useInstancedShadows?: boolean;
}

/**
 * 水体实例化渲染系统事件类型
 */
export enum WaterInstancedRendererEventType {
  /** 实例创建 */
  INSTANCE_CREATED = 'instanceCreated',
  /** 实例销毁 */
  INSTANCE_DESTROYED = 'instanceDestroyed',
  /** 实例更新 */
  INSTANCE_UPDATED = 'instanceUpdated'
}

/**
 * 水体实例化渲染系统
 */
export class WaterInstancedRenderer extends System {
  /** 系统类型 */
  public static readonly TYPE = 'WaterInstancedRenderer';

  /** 配置 */
  private config: Required<WaterInstancedRendererConfig>;
  /** 水体实体映射 */
  private waterEntities: Map<Entity, WaterBodyComponent> = new Map();
  /** 特效实例映射 */
  private effectInstances: Map<string, WaterEffectInstance> = new Map();
  /** 特效类型映射 */
  private effectTypeMap: Map<WaterEffectType, {
    mesh: THREE.InstancedMesh;
    geometry: THREE.BufferGeometry;
    material: THREE.Material;
    count: number;
    instances: Map<string, number>;
    dummy: THREE.Object3D;
  }> = new Map();
  /** 帧计数器 */
  private frameCount: number = 0;
  /** 事件发射器 */
  private eventEmitter: EventEmitter = new EventEmitter();
  /** 性能监视器 */
  private performanceMonitor: PerformanceMonitor = new PerformanceMonitor();
  /** 调试对象 */
  private debugObjects: THREE.Object3D[] = [];
  /** 临时对象 */
  private tempObject: THREE.Object3D = new THREE.Object3D();
  /** 临时颜色 */
  private tempColor: THREE.Color = new THREE.Color();

  /**
   * 构造函数
   * @param world 世界
   * @param config 配置
   */
  constructor(world: World, config: WaterInstancedRendererConfig = {}) {
    super(world);

    // 设置默认配置
    this.config = {
      enabled: config.enabled !== undefined ? config.enabled : true,
      autoUpdate: config.autoUpdate !== undefined ? config.autoUpdate : true,
      updateFrequency: config.updateFrequency || 1,
      maxInstances: config.maxInstances || 1000,
      useDebugVisualization: config.useDebugVisualization !== undefined ? config.useDebugVisualization : false,
      useGPUCulling: config.useGPUCulling !== undefined ? config.useGPUCulling : true,
      useInstancedShadows: config.useInstancedShadows !== undefined ? config.useInstancedShadows : true
    };

    // 初始化特效类型
    this.initializeEffectTypes();

    // 初始化调试可视化
    if (this.config.useDebugVisualization) {
      this.initializeDebugVisualization();
    }
  }

  /**
   * 初始化系统
   */
  public initialize(): void {
    super.initialize();
    Debug.log('WaterInstancedRenderer', '水体实例化渲染系统初始化');
  }

  /**
   * 初始化特效类型
   */
  private initializeEffectTypes(): void {
    // 创建泡沫特效
    this.createEffectType(
      WaterEffectType.FOAM,
      new THREE.SphereGeometry(0.5, 12, 12),
      new THREE.MeshStandardMaterial({
        color: 0xffffff,
        transparent: true,
        opacity: 0.8,
        roughness: 0.2,
        metalness: 0.0,
        envMapIntensity: 0.5
      })
    );

    // 创建水花特效 - 使用更复杂的几何体
    const splashGeometry = new THREE.BufferGeometry();
    const splashVertices = [];
    const splashIndices = [];

    // 创建水花的主体（锥形）
    const coneSegments = 12;
    const coneHeight = 1.0;
    const coneRadius = 0.2;

    // 添加顶点
    splashVertices.push(0, coneHeight, 0); // 顶点

    // 添加底部圆周顶点
    for (let i = 0; i < coneSegments; i++) {
      const angle = (i / coneSegments) * Math.PI * 2;
      const x = Math.cos(angle) * coneRadius;
      const z = Math.sin(angle) * coneRadius;
      splashVertices.push(x, 0, z);

      // 添加随机水滴
      if (i % 2 === 0) {
        const dropHeight = 0.3 + Math.random() * 0.3;
        const dropRadius = 0.05 + Math.random() * 0.05;
        const dropAngle = angle + (Math.random() * 0.2 - 0.1);
        const dropDist = coneRadius * 1.2;
        const dropX = Math.cos(dropAngle) * dropDist;
        const dropZ = Math.sin(dropAngle) * dropDist;

        splashVertices.push(dropX, dropHeight, dropZ);
      }
    }

    // 添加面（三角形）
    for (let i = 0; i < coneSegments; i++) {
      const next = (i + 1) % coneSegments;
      splashIndices.push(0, i + 1, next + 1); // 顶点到底部的三角形
    }

    // 设置几何体属性
    splashGeometry.setIndex(splashIndices);
    splashGeometry.setAttribute('position', new THREE.Float32BufferAttribute(splashVertices, 3));
    splashGeometry.computeVertexNormals();

    // 创建水花特效
    this.createEffectType(
      WaterEffectType.SPLASH,
      splashGeometry,
      new THREE.MeshStandardMaterial({
        color: 0x88ccff,
        transparent: true,
        opacity: 0.7,
        roughness: 0.3,
        metalness: 0.2,
        envMapIntensity: 0.8,
        side: THREE.DoubleSide
      })
    );

    // 创建水滴特效 - 使用更真实的水滴形状
    const dropletGeometry = new THREE.SphereGeometry(0.1, 12, 12);
    // 拉伸水滴形状
    const dropletPositions = dropletGeometry.attributes.position.array;
    for (let i = 0; i < dropletPositions.length; i += 3) {
      const y = dropletPositions[i + 1];
      if (y > 0) {
        // 顶部拉长
        dropletPositions[i + 1] = y * 1.5;
      }
    }
    dropletGeometry.computeVertexNormals();

    this.createEffectType(
      WaterEffectType.DROPLET,
      dropletGeometry,
      new THREE.MeshPhysicalMaterial({
        color: 0x88ccff,
        transparent: true,
        opacity: 0.9,
        roughness: 0.1,
        metalness: 0.0,
        clearcoat: 1.0,
        clearcoatRoughness: 0.1,
        envMapIntensity: 1.0,
        side: THREE.DoubleSide
      })
    );

    // 创建波纹特效 - 使用更平滑的波纹
    this.createEffectType(
      WaterEffectType.RIPPLE,
      new THREE.RingGeometry(0.5, 0.6, 32),
      new THREE.MeshBasicMaterial({
        color: 0xffffff,
        transparent: true,
        opacity: 0.5,
        side: THREE.DoubleSide,
        blending: THREE.AdditiveBlending
      })
    );

    // 创建水雾特效 - 使用更复杂的几何体
    const mistGeometry = new THREE.PlaneGeometry(1, 1, 4, 4);
    // 添加一些随机扰动
    const mistPositions = mistGeometry.attributes.position.array;
    for (let i = 0; i < mistPositions.length; i += 3) {
      mistPositions[i] += (Math.random() - 0.5) * 0.1;
      mistPositions[i + 2] += (Math.random() - 0.5) * 0.1;
    }
    mistGeometry.computeVertexNormals();

    this.createEffectType(
      WaterEffectType.MIST,
      mistGeometry,
      new THREE.MeshBasicMaterial({
        color: 0xffffff,
        transparent: true,
        opacity: 0.3,
        side: THREE.DoubleSide,
        blending: THREE.AdditiveBlending,
        depthWrite: false
      })
    );
  }

  /**
   * 创建特效类型
   * @param type 特效类型
   * @param geometry 几何体
   * @param material 材质
   */
  private createEffectType(
    type: WaterEffectType,
    geometry: THREE.BufferGeometry,
    material: THREE.Material
  ): void {
    // 创建实例化网格
    const mesh = new THREE.InstancedMesh(
      geometry,
      material,
      this.config.maxInstances
    );
    mesh.name = `WaterEffect_${type}`;
    mesh.frustumCulled = this.config.useGPUCulling;
    mesh.castShadow = this.config.useInstancedShadows;
    mesh.receiveShadow = this.config.useInstancedShadows;
    mesh.count = 0;

    // 添加到场景
    this.world.getScene()?.add(mesh);

    // 创建临时对象
    const dummy = new THREE.Object3D();

    // 添加到映射
    this.effectTypeMap.set(type, {
      mesh,
      geometry,
      material,
      count: 0,
      instances: new Map(),
      dummy
    });

    Debug.log('WaterInstancedRenderer', `创建水体特效类型: ${type}`);
  }

  /**
   * 初始化调试可视化
   */
  private initializeDebugVisualization(): void {
    // 创建调试容器
    const debugContainer = new THREE.Object3D();
    debugContainer.name = 'WaterInstancedRendererDebug';
    this.world.getScene()?.add(debugContainer);
  }

  /**
   * 更新系统
   * @param deltaTime 帧间隔时间（秒）
   */
  public update(deltaTime: number): void {
    if (!this.enabled || !this.config.autoUpdate) {
      return;
    }

    // 按照更新频率更新
    this.frameCount++;
    if (this.frameCount % this.config.updateFrequency !== 0) {
      return;
    }

    // 如果启用了性能监控，开始计时
    if (this.config.useDebugVisualization) {
      this.performanceMonitor.start('waterInstancedUpdate');
    }

    // 更新所有特效实例
    this.updateEffectInstances(deltaTime);

    // 更新调试可视化
    if (this.config.useDebugVisualization) {
      this.updateDebugVisualization();
      this.performanceMonitor.end('waterInstancedUpdate');
    }
  }

  /**
   * 添加水体实体
   * @param entity 实体
   * @param component 水体组件
   */
  public addWaterEntity(entity: Entity, component: WaterBodyComponent): void {
    this.waterEntities.set(entity, component);
  }

  /**
   * 移除水体实体
   * @param entity 实体
   */
  public removeWaterEntity(entity: Entity): void {
    this.waterEntities.delete(entity);
  }

  /**
   * 更新特效实例
   * @param deltaTime 帧间隔时间（秒）
   */
  private updateEffectInstances(deltaTime: number): void {
    // 遍历所有特效实例
    for (const [id, instance] of this.effectInstances.entries()) {
      // 更新实例年龄
      instance.age += deltaTime;

      // 如果实例超过生命周期，则销毁
      if (instance.age >= instance.lifetime) {
        this.destroyEffectInstance(id);
        continue;
      }

      // 更新实例
      this.updateEffectInstance(instance, deltaTime);
    }
  }

  /**
   * 更新特效实例
   * @param instance 特效实例
   * @param deltaTime 帧间隔时间（秒）
   */
  private updateEffectInstance(instance: WaterEffectInstance, deltaTime: number): void {
    // 获取特效类型
    const effectType = this.effectTypeMap.get(instance.type);
    if (!effectType) {
      return;
    }

    // 获取实例索引
    const index = effectType.instances.get(instance.id);
    if (index === undefined) {
      return;
    }

    // 计算生命周期进度
    const progress = instance.age / instance.lifetime;

    // 根据特效类型更新实例
    switch (instance.type) {
      case WaterEffectType.FOAM:
        this.updateFoamInstance(instance, progress, deltaTime);
        break;
      case WaterEffectType.SPLASH:
        this.updateSplashInstance(instance, progress, deltaTime);
        break;
      case WaterEffectType.DROPLET:
        this.updateDropletInstance(instance, progress, deltaTime);
        break;
      case WaterEffectType.RIPPLE:
        this.updateRippleInstance(instance, progress, deltaTime);
        break;
      case WaterEffectType.MIST:
        this.updateMistInstance(instance, progress, deltaTime);
        break;
    }

    // 更新实例矩阵
    this.updateInstanceMatrix(instance, index);

    // 更新实例颜色
    this.updateInstanceColor(instance, index);

    // 发出事件
    this.eventEmitter.emit(WaterInstancedRendererEventType.INSTANCE_UPDATED, instance);
  }

  /**
   * 更新泡沫实例
   * @param instance 特效实例
   * @param progress 生命周期进度
   * @param deltaTime 帧间隔时间（秒）
   */
  private updateFoamInstance(
    instance: WaterEffectInstance,
    progress: number,
    deltaTime: number
  ): void {
    // 更新不透明度
    instance.opacity = 1 - progress;

    // 更新缩放
    instance.scale.x = 1 + progress * 0.5;
    instance.scale.y = 0.2;
    instance.scale.z = 1 + progress * 0.5;
  }

  /**
   * 更新水花实例
   * @param instance 特效实例
   * @param progress 生命周期进度
   * @param deltaTime 帧间隔时间（秒）
   */
  private updateSplashInstance(
    instance: WaterEffectInstance,
    progress: number,
    deltaTime: number
  ): void {
    // 更新不透明度
    instance.opacity = 1 - progress;

    // 更新缩放
    instance.scale.x = 1 + progress * 2;
    instance.scale.y = 1 + progress * 3;
    instance.scale.z = 1 + progress * 2;

    // 更新位置
    (instance as any).getPosition().y += deltaTime * 2 * (1 - progress);
  }

  /**
   * 更新水滴实例
   * @param instance 特效实例
   * @param progress 生命周期进度
   * @param deltaTime 帧间隔时间（秒）
   */
  private updateDropletInstance(
    instance: WaterEffectInstance,
    progress: number,
    deltaTime: number
  ): void {
    // 初始化速度数据（如果不存在）
    if (instance.userData.velocityY === undefined) {
      instance.userData.velocityY = 2.0 + Math.random() * 1.0;

      // 如果没有设置速度，添加一些随机速度
      if (instance.userData.velocityX === undefined) {
        instance.userData.velocityX = (Math.random() - 0.5) * 0.5;
      }
      if (instance.userData.velocityZ === undefined) {
        instance.userData.velocityZ = (Math.random() - 0.5) * 0.5;
      }

      // 添加旋转速度
      instance.userData.rotationSpeedX = (Math.random() - 0.5) * 2.0;
      instance.userData.rotationSpeedY = (Math.random() - 0.5) * 2.0;
      instance.userData.rotationSpeedZ = (Math.random() - 0.5) * 2.0;

      // 添加拖尾效果标记
      instance.userData.lastPositions = [];
      instance.userData.trailTimer = 0;
    }

    // 更新不透明度（使用平滑曲线）
    const opacityCurve = 1 - Math.pow(progress, 2); // 二次曲线，开始时不透明度高，结束时迅速降低
    instance.opacity = opacityCurve * 0.9;

    // 保存上一帧位置（用于拖尾效果）
    if (instance.userData.trailTimer <= 0) {
      instance.userData.lastPositions.push({
        position: instance.position.clone(),
        age: 0
      });

      // 限制拖尾长度
      if (instance.userData.lastPositions.length > 5) {
        instance.userData.lastPositions.shift();
      }

      instance.userData.trailTimer = 0.05; // 每0.05秒记录一次位置
    } else {
      instance.userData.trailTimer -= deltaTime;
    }

    // 更新拖尾年龄
    for (const trail of instance.userData.lastPositions) {
      trail.age += deltaTime;
    }

    // 更新位置（考虑重力和空气阻力）
    const gravity = 9.8;
    const airResistance = 0.3;

    // 应用重力
    instance.userData.velocityY -= gravity * deltaTime;

    // 应用空气阻力
    const speed = Math.sqrt(
      instance.userData.velocityX * instance.userData.velocityX +
      instance.userData.velocityY * instance.userData.velocityY +
      instance.userData.velocityZ * instance.userData.velocityZ
    );

    if (speed > 0.01) {
      const dragForce = speed * speed * airResistance;
      const dragX = (instance.userData.velocityX / speed) * dragForce;
      const dragY = (instance.userData.velocityY / speed) * dragForce;
      const dragZ = (instance.userData.velocityZ / speed) * dragForce;

      instance.userData.velocityX -= dragX * deltaTime;
      instance.userData.velocityY -= dragY * deltaTime;
      instance.userData.velocityZ -= dragZ * deltaTime;
    }

    // 更新位置
    (instance as any).getPosition().x += instance.userData.velocityX * deltaTime;
    (instance as any).getPosition().y += instance.userData.velocityY * deltaTime;
    (instance as any).getPosition().z += instance.userData.velocityZ * deltaTime;

    // 更新旋转（基于速度方向）
    instance.rotation.x += instance.userData.rotationSpeedX * deltaTime;
    instance.rotation.y += instance.userData.rotationSpeedY * deltaTime;
    instance.rotation.z += instance.userData.rotationSpeedZ * deltaTime;

    // 根据速度调整缩放（速度越快，水滴越扁平）
    const verticalSpeed = Math.abs(instance.userData.velocityY);
    const flattenFactor = Math.min(1.0, verticalSpeed / 10.0);
    instance.scale.y = 1.0 - flattenFactor * 0.3;
    instance.scale.x = 1.0 + flattenFactor * 0.2;
    instance.scale.z = 1.0 + flattenFactor * 0.2;

    // 创建拖尾效果（如果需要）
    if (verticalSpeed > 5.0 && instance.userData.lastPositions.length > 1) {
      // 这里可以添加拖尾粒子效果的创建逻辑
      // 由于我们使用的是实例化渲染，无法直接添加拖尾
      // 但可以在这里触发事件，让其他系统创建拖尾效果
    }
  }

  /**
   * 更新波纹实例
   * @param instance 特效实例
   * @param progress 生命周期进度
   * @param deltaTime 帧间隔时间（秒）
   */
  private updateRippleInstance(
    instance: WaterEffectInstance,
    progress: number,
    deltaTime: number
  ): void {
    // 更新不透明度
    instance.opacity = 1 - progress;

    // 更新缩放
    instance.scale.x = 0.5 + progress * 2;
    instance.scale.y = 0.5 + progress * 2;
    instance.scale.z = 1;
  }

  /**
   * 更新水雾实例
   * @param instance 特效实例
   * @param progress 生命周期进度
   * @param deltaTime 帧间隔时间（秒）
   */
  private updateMistInstance(
    instance: WaterEffectInstance,
    progress: number,
    deltaTime: number
  ): void {
    // 更新不透明度
    instance.opacity = 0.3 * (1 - progress);

    // 更新缩放
    instance.scale.x = 1 + progress * 3;
    instance.scale.y = 1 + progress * 3;
    instance.scale.z = 1;

    // 更新位置
    (instance as any).getPosition().y += deltaTime * 0.5;
    (instance as any).getPosition().x += (instance.userData.windX || 0) * deltaTime;
    (instance as any).getPosition().z += (instance.userData.windZ || 0) * deltaTime;
  }

  /**
   * 更新实例矩阵
   * @param instance 特效实例
   * @param index 实例索引
   */
  private updateInstanceMatrix(instance: WaterEffectInstance, index: number): void {
    // 获取特效类型
    const effectType = this.effectTypeMap.get(instance.type);
    if (!effectType) {
      return;
    }

    // 设置临时对象的位置、旋转和缩放
    this.tempObject.position.copy(instance.position);
    this.tempObject.rotation.copy(instance.rotation);
    this.tempObject.scale.copy(instance.scale);

    // 更新矩阵
    this.tempObject.updateMatrix();

    // 设置实例矩阵
    effectType.mesh.setMatrixAt(index, this.tempObject.matrix);

    // 标记矩阵需要更新
    effectType.mesh.instanceMatrix.needsUpdate = true;
  }

  /**
   * 更新实例颜色
   * @param instance 特效实例
   * @param index 实例索引
   */
  private updateInstanceColor(instance: WaterEffectInstance, index: number): void {
    // 获取特效类型
    const effectType = this.effectTypeMap.get(instance.type);
    if (!effectType) {
      return;
    }

    // 设置临时颜色
    this.tempColor.copy(instance.color);

    // 设置实例颜色
    effectType.mesh.setColorAt(index, this.tempColor);

    // 标记颜色需要更新
    if (effectType.mesh.instanceColor) {
      effectType.mesh.instanceColor.needsUpdate = true;
    }

    // 更新材质不透明度
    if (effectType.material instanceof THREE.Material && effectType.material.transparent) {
      effectType.material.opacity = instance.opacity;
      effectType.material.needsUpdate = true;
    }
  }

  /**
   * 创建特效实例
   * @param type 特效类型
   * @param position 位置
   * @param options 选项
   * @returns 特效实例ID
   */
  public createEffectInstance(
    type: WaterEffectType,
    position: THREE.Vector3,
    options: {
      rotation?: THREE.Euler;
      scale?: THREE.Vector3;
      color?: THREE.Color;
      opacity?: number;
      lifetime?: number;
      userData?: any;
    } = {}
  ): string {
    // 获取特效类型
    const effectType = this.effectTypeMap.get(type);
    if (!effectType) {
      Debug.warn('WaterInstancedRenderer', `未知的特效类型: ${type}`);
      return '';
    }

    // 检查是否达到最大实例数
    if (effectType.count >= this.config.maxInstances) {
      Debug.warn('WaterInstancedRenderer', `特效类型 ${type} 达到最大实例数: ${this.config.maxInstances}`);
      return '';
    }

    // 生成实例ID
    const id = `${type}_${Date.now()}_${Math.floor(Math.random() * 1000)}`;

    // 创建实例
    const instance: WaterEffectInstance = {
      id,
      type,
      position: position.clone(),
      rotation: options.rotation ? options.rotation.clone() : new THREE.Euler(),
      scale: options.scale ? options.scale.clone() : new THREE.Vector3(1, 1, 1),
      color: options.color ? options.color.clone() : new THREE.Color(1, 1, 1),
      opacity: options.opacity !== undefined ? options.opacity : 1,
      lifetime: options.lifetime || 1,
      age: 0,
      active: true,
      userData: options.userData || {}
    };

    // 添加到映射
    this.effectInstances.set(id, instance);

    // 获取实例索引
    const index = effectType.count;
    effectType.instances.set(id, index);
    effectType.count++;

    // 更新实例矩阵
    this.updateInstanceMatrix(instance, index);

    // 更新实例颜色
    this.updateInstanceColor(instance, index);

    // 更新网格计数
    effectType.mesh.count = effectType.count;

    // 发出事件
    this.eventEmitter.emit(WaterInstancedRendererEventType.INSTANCE_CREATED, instance);

    Debug.log('WaterInstancedRenderer', `创建特效实例: ${id}`);

    return id;
  }

  /**
   * 销毁特效实例
   * @param id 特效实例ID
   */
  public destroyEffectInstance(id: string): void {
    // 获取实例
    const instance = this.effectInstances.get(id);
    if (!instance) {
      return;
    }

    // 获取特效类型
    const effectType = this.effectTypeMap.get(instance.type);
    if (!effectType) {
      return;
    }

    // 获取实例索引
    const index = effectType.instances.get(id);
    if (index === undefined) {
      return;
    }

    // 从映射中移除
    this.effectInstances.delete(id);
    effectType.instances.delete(id);

    // 如果不是最后一个实例，则需要移动最后一个实例到当前位置
    if (index < effectType.count - 1) {
      // 获取最后一个实例
      let lastInstanceId = '';
      for (const [instanceId, instanceIndex] of effectType.instances.entries()) {
        if (instanceIndex === effectType.count - 1) {
          lastInstanceId = instanceId;
          break;
        }
      }

      if (lastInstanceId) {
        // 获取最后一个实例
        const lastInstance = this.effectInstances.get(lastInstanceId);
        if (lastInstance) {
          // 更新索引
          effectType.instances.set(lastInstanceId, index);

          // 更新矩阵
          this.updateInstanceMatrix(lastInstance, index);

          // 更新颜色
          this.updateInstanceColor(lastInstance, index);
        }
      }
    }

    // 减少计数
    effectType.count--;

    // 更新网格计数
    effectType.mesh.count = effectType.count;

    // 发出事件
    this.eventEmitter.emit(WaterInstancedRendererEventType.INSTANCE_DESTROYED, instance);

    Debug.log('WaterInstancedRenderer', `销毁特效实例: ${id}`);
  }

  /**
   * 更新调试可视化
   */
  private updateDebugVisualization(): void {
    // 清除旧的调试对象
    for (const obj of this.debugObjects) {
      this.world.getScene()?.remove(obj);
    }
    this.debugObjects = [];

    // 创建新的调试对象
    for (const [type, effectType] of this.effectTypeMap.entries()) {
      // 创建文本标签
      // 这里需要使用TextSprite或类似的库来创建3D文本
      // 暂时省略

      // 创建计数指示器
      const indicator = new THREE.Mesh(
        new THREE.SphereGeometry(0.5, 8, 8),
        new THREE.MeshBasicMaterial({ color: 0xffff00 })
      );
      (indicator as any).setPosition(0, 10, 0);
      this.world.getScene()?.add(indicator);
      this.debugObjects.push(indicator);
    }
  }
}
