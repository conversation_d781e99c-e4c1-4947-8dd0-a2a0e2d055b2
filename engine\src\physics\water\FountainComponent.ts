/**
 * 喷泉组件
 * 用于表示喷泉及其特殊物理属性和效果
 */
import * as THREE from 'three';
import { WaterBodyComponent, WaterBodyType } from './WaterBodyComponent';
import { type Entity  } from '../../core/Entity';
import { Debug } from '../../utils/Debug';
import { AudioSource, AudioSourceOptions } from '../../audio/AudioSource';
import { AudioSystem, AudioType } from '../../audio/AudioSystem';
import { UnderwaterParticleSystem, UnderwaterParticleType } from '../../rendering/water/UnderwaterParticleSystem';
import { TransformComponent   } from '../../core/TransformComponent';
import { SimplexNoise } from '../../utils/SimplexNoise';

/**
 * 喷泉配置
 */
export interface FountainConfig {
  /** 喷泉宽度 */
  width?: number;
  /** 喷泉高度 */
  height?: number;
  /** 喷泉深度 */
  depth?: number;
  /** 喷泉位置 */
  position?: THREE.Vector3;
  /** 喷泉旋转 */
  rotation?: THREE.Euler;
  /** 喷泉颜色 */
  color?: THREE.Color;
  /** 喷泉不透明度 */
  opacity?: number;
  /** 喷泉流速 */
  flowSpeed?: number;
  /** 喷泉流向 */
  flowDirection?: THREE.Vector3;
  /** 喷泉湍流强度 */
  turbulenceStrength?: number;
  /** 喷泉湍流频率 */
  turbulenceFrequency?: number;
  /** 喷泉湍流速度 */
  turbulenceSpeed?: number;
  /** 是否启用水雾效果 */
  enableMistEffect?: boolean;
  /** 水雾效果强度 */
  mistEffectStrength?: number;
  /** 是否启用水花效果 */
  enableSplashEffect?: boolean;
  /** 水花效果强度 */
  splashEffectStrength?: number;
  /** 是否启用水滴效果 */
  enableDropletEffect?: boolean;
  /** 水滴效果强度 */
  dropletEffectStrength?: number;
  /** 是否启用声音效果 */
  enableSoundEffect?: boolean;
  /** 声音效果音量 */
  soundEffectVolume?: number;
  /** 是否启用水流动力学 */
  enableFluidDynamics?: boolean;
  /** 喷泉类型 */
  fountainType?: FountainType;
  /** 喷泉模式 */
  fountainMode?: FountainMode;
  /** 喷泉喷射高度 */
  jetHeight?: number;
  /** 喷泉喷射角度 */
  jetAngle?: number;
  /** 喷泉喷射数量 */
  jetCount?: number;
  /** 喷泉喷射间隔 */
  jetInterval?: number;
  /** 喷泉喷射持续时间 */
  jetDuration?: number;
  /** 喷泉喷射延迟 */
  jetDelay?: number;
  /** 喷泉喷射随机性 */
  jetRandomness?: number;
  /** 是否启用 */
  enabled?: boolean;
}

/**
 * 喷泉类型
 */
export enum FountainType {
  /** 标准喷泉 */
  STANDARD = 'standard',
  /** 多喷头喷泉 */
  MULTI_JET = 'multi_jet',
  /** 舞蹈喷泉 */
  DANCING = 'dancing',
  /** 音乐喷泉 */
  MUSICAL = 'musical',
  /** 交互式喷泉 */
  INTERACTIVE = 'interactive',
  /** 螺旋喷泉 */
  SPIRAL = 'spiral',
  /** 花朵喷泉 */
  FLOWER = 'flower',
  /** 瀑布喷泉 */
  WATERFALL = 'waterfall',
  /** 圆顶喷泉 */
  DOME = 'dome',
  /** 交叉喷泉 */
  CROSSING = 'crossing'
}

/**
 * 喷泉模式
 */
export enum FountainMode {
  /** 连续模式 */
  CONTINUOUS = 'continuous',
  /** 脉冲模式 */
  PULSE = 'pulse',
  /** 交替模式 */
  ALTERNATING = 'alternating',
  /** 序列模式 */
  SEQUENCE = 'sequence',
  /** 随机模式 */
  RANDOM = 'random',
  /** 波浪模式 */
  WAVE = 'wave',
  /** 级联模式 */
  CASCADE = 'cascade',
  /** 爆发模式 */
  BURST = 'burst',
  /** 和谐模式 */
  HARMONIC = 'harmonic',
  /** 混沌模式 */
  CHAOTIC = 'chaotic'
}

/**
 * 喷泉组件
 */
export class FountainComponent extends WaterBodyComponent {
  /** 喷泉湍流强度 */
  private turbulenceStrength: number = 1.0;
  /** 喷泉湍流频率 */
  private turbulenceFrequency: number = 2.0;
  /** 喷泉湍流速度 */
  private turbulenceSpeed: number = 1.0;
  /** 是否启用水雾效果 */
  private enableMistEffect: boolean = true;
  /** 水雾效果强度 */
  private mistEffectStrength: number = 1.0;
  /** 是否启用水花效果 */
  private enableSplashEffect: boolean = true;
  /** 水花效果强度 */
  private splashEffectStrength: number = 1.0;
  /** 是否启用水滴效果 */
  private enableDropletEffect: boolean = true;
  /** 水滴效果强度 */
  private dropletEffectStrength: number = 1.0;
  /** 是否启用声音效果 */
  private enableSoundEffect: boolean = true;
  /** 声音效果音量 */
  private soundEffectVolume: number = 1.0;
  /** 是否启用水流动力学 */
  private enableFluidDynamics: boolean = true;
  /** 喷泉类型 */
  private fountainType: FountainType = FountainType.STANDARD;
  /** 喷泉模式 */
  private fountainMode: FountainMode = FountainMode.CONTINUOUS;
  /** 喷泉喷射高度 */
  private jetHeight: number = 10.0;
  /** 喷泉喷射角度 */
  private jetAngle: number = 0.0;
  /** 喷泉喷射数量 */
  private jetCount: number = 1;
  /** 喷泉喷射间隔 */
  private jetInterval: number = 1.0;
  /** 喷泉喷射持续时间 */
  private jetDuration: number = 2.0;
  /** 喷泉喷射延迟 */
  private jetDelay: number = 0.0;
  /** 喷泉喷射随机性 */
  private jetRandomness: number = 0.2;
  /** 音频源 */
  private audioSource: AudioSource | null = null;
  /** 水雾粒子系统 */
  private mistParticleSystem: any = null;
  /** 水花粒子系统 */
  private splashParticleSystem: any = null;
  /** 水滴粒子系统 */
  private dropletParticleSystem: any = null;
  /** 水流路径点 */
  private flowPathPoints: THREE.Vector3[] = [];
  /** 水流网格 */
  private flowMesh: THREE.Mesh | null = null;
  /** 底部水体 */
  private bottomWaterBody: WaterBodyComponent | null = null;
  /** 喷泉喷射计时器 */
  private jetTimer: number = 0.0;
  /** 喷泉喷射状态 */
  private jetActive: boolean = false;
  /** 喷泉喷射持续计时器 */
  private jetDurationTimer: number = 0.0;
  /** 喷泉模式计时器 */
  private modeTimer: number = 0.0;
  /** 上次随机时间 */
  private lastRandomTime: number = 0.0;
  /** 喷泉喷射索引 */
  private currentJetIndex: number = 0;
  /** 喷泉喷射活跃状态数组 */
  private jetActiveArray: boolean[] = [];
  /** 噪声生成器 */
  private noiseGenerator: SimplexNoise = new SimplexNoise();
  /** 喷泉喷射点 */
  private jetPoints: THREE.Vector3[] = [];
  /** 喷泉喷射方向 */
  private jetDirections: THREE.Vector3[] = [];
  /** 喷泉喷射强度 */
  private jetStrengths: number[] = [];

  /**
   * 创建喷泉组件
   * @param entity 实体
   * @param config 喷泉配置
   */
  constructor(entity: Entity, config: FountainConfig = {}) {
    // 确保水体类型设置为喷泉
    const fountainConfig = {
      ...config,
      type: WaterBodyType.FOUNTAIN
    };

    super(entity, fountainConfig);

    // 应用配置
    this.applyConfig(config);

    // 初始化喷泉特有属性
    this.initialize();
  }

  /**
   * 应用配置
   * @param config 喷泉配置
   */
  private applyConfig(config: FountainConfig): void {
    // 设置尺寸
    if (config.width !== undefined || config.height !== undefined || config.depth !== undefined) {
      this.setSize({
        width: config.width || this.getSize().width,
        height: config.height || this.getSize().height,
        depth: config.depth || this.getSize().depth
      });
    }

    // 设置位置和旋转
    if (config.position) {
      this.setPosition(config.position);
    }

    if (config.rotation) {
      this.setRotation(config.rotation);
    }

    // 设置颜色和不透明度
    if (config.color) this.setColor(config.color);
    if (config.opacity !== undefined) this.setOpacity(config.opacity);

    // 设置流速
    if (config.flowSpeed !== undefined) this.setFlowSpeed(config.flowSpeed);

    // 设置湍流参数
    if (config.turbulenceStrength !== undefined) this.turbulenceStrength = config.turbulenceStrength;
    if (config.turbulenceFrequency !== undefined) this.turbulenceFrequency = config.turbulenceFrequency;
    if (config.turbulenceSpeed !== undefined) this.turbulenceSpeed = config.turbulenceSpeed;

    // 设置效果参数
    if (config.enableMistEffect !== undefined) this.enableMistEffect = config.enableMistEffect;
    if (config.mistEffectStrength !== undefined) this.mistEffectStrength = config.mistEffectStrength;
    if (config.enableSplashEffect !== undefined) this.enableSplashEffect = config.enableSplashEffect;
    if (config.splashEffectStrength !== undefined) this.splashEffectStrength = config.splashEffectStrength;
    if (config.enableDropletEffect !== undefined) this.enableDropletEffect = config.enableDropletEffect;
    if (config.dropletEffectStrength !== undefined) this.dropletEffectStrength = config.dropletEffectStrength;
    if (config.enableSoundEffect !== undefined) this.enableSoundEffect = config.enableSoundEffect;
    if (config.soundEffectVolume !== undefined) this.soundEffectVolume = config.soundEffectVolume;
    if (config.enableFluidDynamics !== undefined) this.enableFluidDynamics = config.enableFluidDynamics;

    // 设置喷泉特有参数
    if (config.fountainType !== undefined) this.fountainType = config.fountainType;
    if (config.fountainMode !== undefined) this.fountainMode = config.fountainMode;
    if (config.jetHeight !== undefined) this.jetHeight = config.jetHeight;
    if (config.jetAngle !== undefined) this.jetAngle = config.jetAngle;
    if (config.jetCount !== undefined) this.jetCount = config.jetCount;
    if (config.jetInterval !== undefined) this.jetInterval = config.jetInterval;
    if (config.jetDuration !== undefined) this.jetDuration = config.jetDuration;
    if (config.jetDelay !== undefined) this.jetDelay = config.jetDelay;
    if (config.jetRandomness !== undefined) this.jetRandomness = config.jetRandomness;

    // 设置是否启用
    if (config.enabled !== undefined) this.setEnabled(config.enabled);
  }

  /**
   * 初始化喷泉组件
   */
  public initialize(): void {
    // 调用父类初始化
    super.initialize();

    // 设置默认流速
    if (this.getFlowSpeed() === 0) {
      this.setFlowSpeed(3.0);
    }

    // 初始化喷泉喷射点
    this.initializeJetPoints();

    // 创建水流网格
    this.createFlowMesh();

    // 初始化音频
    this.initializeAudio();

    // 初始化粒子系统
    this.initializeParticleSystems();

    // 创建底部水体
    this.createBottomWaterBody();

    Debug.log('FountainComponent', '喷泉组件初始化完成');
  }

  /**
   * 初始化喷泉喷射点
   */
  private initializeJetPoints(): void {
    // 清空现有喷射点
    this.jetPoints = [];
    this.jetDirections = [];
    this.jetStrengths = [];

    // 获取喷泉位置
    const position = this.getPosition();

    // 获取喷泉尺寸
    const size = this.getSize();
    const radius = size.width / 2 * 0.8;

    // 根据喷泉类型创建喷射点
    switch (this.fountainType) {
      case FountainType.STANDARD:
        // 单个喷射点
        this.jetPoints.push(new THREE.Vector3(position.x, position.y, position.z));
        this.jetDirections.push(new THREE.Vector3(0, 1, 0));
        this.jetStrengths.push(1.0);
        break;

      case FountainType.MULTI_JET:
        // 多个喷射点
        for (let i = 0; i < this.jetCount; i++) {
          const angle = (i / this.jetCount) * Math.PI * 2;
          const x = position.x + Math.cos(angle) * radius;
          const z = position.z + Math.sin(angle) * radius;
          this.jetPoints.push(new THREE.Vector3(x, position.y, z));

          // 根据喷射角度计算方向
          const jetAngleRad = this.jetAngle * Math.PI / 180;
          const dirX = Math.cos(angle) * Math.sin(jetAngleRad);
          const dirY = Math.cos(jetAngleRad);
          const dirZ = Math.sin(angle) * Math.sin(jetAngleRad);
          this.jetDirections.push(new THREE.Vector3(dirX, dirY, dirZ).normalize());

          this.jetStrengths.push(1.0);
        }
        break;

      case FountainType.DANCING:
        // 舞蹈喷泉（多个喷射点，随机强度）
        for (let i = 0; i < this.jetCount; i++) {
          const angle = (i / this.jetCount) * Math.PI * 2;
          const x = position.x + Math.cos(angle) * radius;
          const z = position.z + Math.sin(angle) * radius;
          this.jetPoints.push(new THREE.Vector3(x, position.y, z));
          this.jetDirections.push(new THREE.Vector3(0, 1, 0));
          this.jetStrengths.push(Math.random());
        }
        break;

      case FountainType.MUSICAL:
        // 音乐喷泉（多个喷射点，均匀分布）
        for (let i = 0; i < this.jetCount; i++) {
          const angle = (i / this.jetCount) * Math.PI * 2;
          const x = position.x + Math.cos(angle) * radius;
          const z = position.z + Math.sin(angle) * radius;
          this.jetPoints.push(new THREE.Vector3(x, position.y, z));
          this.jetDirections.push(new THREE.Vector3(0, 1, 0));
          this.jetStrengths.push(0.5);
        }
        break;

      case FountainType.SPIRAL:
        // 螺旋喷泉（螺旋形排列的喷射点）
        const spiralCount = Math.max(12, this.jetCount);
        const spiralTurns = 2; // 螺旋圈数

        for (let i = 0; i < spiralCount; i++) {
          // 计算螺旋参数
          const t = i / (spiralCount - 1); // 0到1的参数
          const spiralRadius = radius * (0.2 + t * 0.8); // 从内到外
          const angle = t * Math.PI * 2 * spiralTurns; // 螺旋角度

          // 计算位置
          const x = position.x + Math.cos(angle) * spiralRadius;
          const z = position.z + Math.sin(angle) * spiralRadius;
          this.jetPoints.push(new THREE.Vector3(x, position.y, z));

          // 计算方向（稍微向外倾斜）
          const dirX = Math.cos(angle) * 0.2;
          const dirY = 0.95;
          const dirZ = Math.sin(angle) * 0.2;
          this.jetDirections.push(new THREE.Vector3(dirX, dirY, dirZ).normalize());

          // 强度从内到外递减
          this.jetStrengths.push(1.0 - t * 0.5);
        }
        break;

      case FountainType.FLOWER:
        // 花朵喷泉（花瓣形状排列）
        const petalCount = Math.min(8, this.jetCount); // 花瓣数量
        const pointsPerPetal = Math.max(3, Math.floor(this.jetCount / petalCount)); // 每个花瓣的点数

        for (let petal = 0; petal < petalCount; petal++) {
          const petalAngle = (petal / petalCount) * Math.PI * 2;

          for (let i = 0; i < pointsPerPetal; i++) {
            // 计算花瓣参数
            const t = i / (pointsPerPetal - 1); // 0到1的参数
            const petalRadius = radius * (0.3 + 0.7 * Math.sin(t * Math.PI)); // 花瓣形状

            // 计算位置
            const angle = petalAngle + t * 0.5 - 0.25; // 花瓣弯曲
            const x = position.x + Math.cos(angle) * petalRadius;
            const z = position.z + Math.sin(angle) * petalRadius;
            this.jetPoints.push(new THREE.Vector3(x, position.y, z));

            // 计算方向（向中心倾斜）
            const dirX = -Math.cos(angle) * 0.3;
            const dirY = 0.9;
            const dirZ = -Math.sin(angle) * 0.3;
            this.jetDirections.push(new THREE.Vector3(dirX, dirY, dirZ).normalize());

            // 强度
            this.jetStrengths.push(0.7 + 0.3 * Math.sin(t * Math.PI));
          }
        }
        break;

      case FountainType.WATERFALL:
        // 瀑布喷泉（线性排列的喷射点）
        const lineCount = Math.max(10, this.jetCount);
        const lineWidth = size.width * 0.8;

        for (let i = 0; i < lineCount; i++) {
          // 计算位置
          const t = i / (lineCount - 1) - 0.5; // -0.5到0.5
          const x = position.x + t * lineWidth;
          const z = position.z;
          this.jetPoints.push(new THREE.Vector3(x, position.y, z));

          // 计算方向（向前倾斜）
          const dirX = 0;
          const dirY = 0.8;
          const dirZ = 0.6;
          this.jetDirections.push(new THREE.Vector3(dirX, dirY, dirZ).normalize());

          // 强度（中间高，两边低）
          const strength = 0.7 + 0.3 * (1 - Math.abs(t) * 2);
          this.jetStrengths.push(strength);
        }
        break;

      case FountainType.DOME:
        // 圆顶喷泉（半球形排列）
        const domeCount = Math.max(20, this.jetCount);
        const rings = 4; // 环数

        // 中心点
        this.jetPoints.push(new THREE.Vector3(position.x, position.y, position.z));
        this.jetDirections.push(new THREE.Vector3(0, 1, 0));
        this.jetStrengths.push(1.0);

        // 环形排列
        for (let ring = 1; ring <= rings; ring++) {
          const ringRadius = radius * (ring / rings);
          const ringPoints = Math.floor(domeCount * ring / rings);

          for (let i = 0; i < ringPoints; i++) {
            const angle = (i / ringPoints) * Math.PI * 2;
            const x = position.x + Math.cos(angle) * ringRadius;
            const z = position.z + Math.sin(angle) * ringRadius;
            this.jetPoints.push(new THREE.Vector3(x, position.y, z));

            // 方向（从中心向外倾斜）
            const tilt = (ring / rings) * 0.7; // 倾斜程度
            const dirX = Math.cos(angle) * tilt;
            const dirY = 1.0 - tilt;
            const dirZ = Math.sin(angle) * tilt;
            this.jetDirections.push(new THREE.Vector3(dirX, dirY, dirZ).normalize());

            // 强度（外环较低）
            this.jetStrengths.push(1.0 - (ring / rings) * 0.3);
          }
        }
        break;

      case FountainType.CROSSING:
        // 交叉喷泉（交叉的喷射线）
        const crossCount = Math.min(8, this.jetCount);
        const pointsPerLine = Math.max(3, Math.floor(this.jetCount / crossCount));

        for (let cross = 0; cross < crossCount; cross++) {
          const crossAngle = (cross / crossCount) * Math.PI;

          for (let i = 0; i < pointsPerLine; i++) {
            // 计算参数
            const t = (i / (pointsPerLine - 1)) * 2 - 1; // -1到1

            // 计算位置
            const dist = t * radius;
            const x = position.x + Math.cos(crossAngle) * dist;
            const z = position.z + Math.sin(crossAngle) * dist;
            this.jetPoints.push(new THREE.Vector3(x, position.y, z));

            // 计算方向（垂直于线方向）
            const dirX = Math.sin(crossAngle) * 0.3;
            const dirY = 0.9;
            const dirZ = -Math.cos(crossAngle) * 0.3;
            this.jetDirections.push(new THREE.Vector3(dirX, dirY, dirZ).normalize());

            // 强度
            this.jetStrengths.push(0.8 + 0.2 * Math.random());
          }
        }
        break;

      case FountainType.INTERACTIVE:
        // 交互式喷泉（单个喷射点）
        this.jetPoints.push(new THREE.Vector3(position.x, position.y, position.z));
        this.jetDirections.push(new THREE.Vector3(0, 1, 0));
        this.jetStrengths.push(1.0);
        break;

      default:
        // 默认单个喷射点
        this.jetPoints.push(new THREE.Vector3(position.x, position.y, position.z));
        this.jetDirections.push(new THREE.Vector3(0, 1, 0));
        this.jetStrengths.push(1.0);
        break;
    }

    Debug.log('FountainComponent', `初始化喷射点完成，共${this.jetPoints.length}个喷射点`);
  }

  /**
   * 创建水流网格
   */
  private createFlowMesh(): void {
    // 获取喷泉位置
    const position = this.getPosition();
    const size = this.getSize();

    // 创建水流几何体
    const geometry = new THREE.CylinderGeometry(
      size.width / 4, // 顶部半径
      size.width / 2, // 底部半径
      this.jetHeight,      // 高度
      16,                  // 径向分段
      10,                  // 高度分段
      true                 // 开放式圆柱体
    );

    // 将几何体移动到正确位置
    geometry.translate(0, this.jetHeight / 2, 0);

    // 创建水流材质
    const material = new THREE.MeshStandardMaterial({
      color: this.getColor(),
      transparent: true,
      opacity: this.getOpacity() * 0.7,
      side: THREE.DoubleSide,
      roughness: 0.1,
      metalness: 0.2,
      wireframe: false
    });

    // 创建网格
    this.flowMesh = new THREE.Mesh(geometry, material);
    this.flowMesh.position.copy(position);

    // 保存原始顶点位置（用于湍流效果）
    const positions = geometry.attributes.position.array;
    geometry.userData.originalPositions = new Float32Array(positions.length);
    for (let i = 0; i < positions.length; i++) {
      geometry.userData.originalPositions[i] = positions[i];
    }
    geometry.userData.time = 0;

    Debug.log('FountainComponent', '创建水流网格完成');
  }

  /**
   * 初始化音频
   */
  private initializeAudio(): void {
    // 如果未启用声音效果，则不初始化音频
    if (!this.enableSoundEffect) return;

    // 获取音频系统
    const audioSystem = this.entity.getWorld().getSystem(AudioSystem);
    if (!audioSystem) {
      Debug.warn('FountainComponent', '音频系统未找到');
      return;
    }

    // 创建音频源
    const audioSourceId = `fountain_${this.entity.id}`;
    this.audioSource = audioSystem.createSource(audioSourceId, AudioType.AMBIENT);
    if (!this.audioSource) {
      Debug.warn('FountainComponent', '创建音频源失败');
      return;
    }

    // 设置音频源属性
    this.audioSource.setVolume(this.soundEffectVolume);
    this.audioSource.setLoop(true);
    this.audioSource.setSpatial(true);
    const position = this.getPosition();
    this.audioSource.setPosition(position.x, position.y, position.z);
    this.audioSource.setRefDistance(10);
    this.audioSource.setMaxDistance(100);
    this.audioSource.setRolloffFactor(1);

    // 加载并播放喷泉声音
    // 注意：这里需要先加载音频文件，实际实现中可能需要音频加载器
    // 暂时跳过音频文件加载，因为需要音频加载系统

    Debug.log('FountainComponent', '音频系统初始化完成');
  }

  /**
   * 初始化粒子系统
   */
  private initializeParticleSystems(): void {
    // 如果未启用水雾效果、水花效果和水滴效果，则不初始化粒子系统
    if (!this.enableMistEffect && !this.enableSplashEffect && !this.enableDropletEffect) return;

    // 获取水下粒子系统
    const systems = this.entity.getWorld().getSystems();
    const underwaterParticleSystem = systems.find(system => system instanceof UnderwaterParticleSystem);
    if (!underwaterParticleSystem) {
      Debug.warn('FountainComponent', '水下粒子系统未找到');
      return;
    }

    // 获取喷泉位置
    const position = this.getPosition();
    const size = this.getSize();

    // 创建水雾效果
    if (this.enableMistEffect) {
      (underwaterParticleSystem as unknown as UnderwaterParticleSystem).addParticleGroup(this.entity.id, 'fountainMist', {
        type: UnderwaterParticleType.MIST,
        count: Math.floor(100 * this.mistEffectStrength),
        size: [0.5, 2.0],
        color: 0xffffff,
        opacity: 0.3 * this.mistEffectStrength,
        lifetime: [2, 5],
        speed: [0.1, 0.3],
        acceleration: new THREE.Vector3(0, 0.05, 0),
        blending: THREE.AdditiveBlending,
        emissionArea: {
          shape: 'cylinder',
          size: size.width / 2, // 使用 size 而不是 radius
          position: new THREE.Vector3(position.x, position.y + this.jetHeight / 2, position.z)
        }
      });
    }

    // 创建水花效果
    if (this.enableSplashEffect) {
      (underwaterParticleSystem as unknown as UnderwaterParticleSystem).addParticleGroup(this.entity.id, 'fountainSplash', {
        type: UnderwaterParticleType.SPLASH,
        count: Math.floor(100 * this.splashEffectStrength),
        size: [0.1, 0.3],
        color: 0xffffff,
        opacity: 0.7 * this.splashEffectStrength,
        lifetime: [0.5, 1.5],
        speed: [0.5, 2.0],
        acceleration: new THREE.Vector3(0, -9.8, 0),
        rotation: true,
        rotationSpeed: [1.0, 3.0],
        blending: THREE.AdditiveBlending,
        emissionArea: {
          shape: 'sphere', // 使用 sphere 而不是 circle
          size: size.width / 2,
          position: new THREE.Vector3(position.x, position.y + this.jetHeight, position.z)
        }
      });
    }

    // 创建水滴效果
    if (this.enableDropletEffect) {
      (underwaterParticleSystem as unknown as UnderwaterParticleSystem).addParticleGroup(this.entity.id, 'fountainDroplet', {
        type: UnderwaterParticleType.DROPLET,
        count: Math.floor(200 * this.dropletEffectStrength),
        size: [0.05, 0.15],
        color: this.getColor(),
        opacity: 0.8 * this.dropletEffectStrength,
        lifetime: [1, 2],
        speed: [1.0, 3.0],
        acceleration: new THREE.Vector3(0, -9.8, 0),
        rotation: true,
        rotationSpeed: [2.0, 5.0],
        blending: THREE.AdditiveBlending,
        emissionArea: {
          shape: 'sphere',
          size: size.width / 4, // 使用 size 而不是 radius
          position: new THREE.Vector3(position.x, position.y + this.jetHeight, position.z)
        }
      });
    }

    Debug.log('FountainComponent', '粒子系统初始化完成');
  }

  /**
   * 创建底部水体
   */
  private createBottomWaterBody(): void {
    // 创建底部水体实体
    const bottomWaterEntity = new Entity(`fountain_bottom_${this.entity.id}`);

    // 获取喷泉位置
    const position = this.getPosition();
    const size = this.getSize();

    // 创建底部水体组件
    this.bottomWaterBody = new WaterBodyComponent(bottomWaterEntity, {
      size: {
        width: size.width * 2,
        height: 0.5,
        depth: size.width * 2
      }
    });

    // 初始化底部水体
    this.bottomWaterBody.initialize();

    // 添加组件到实体
    bottomWaterEntity.addComponent(this.bottomWaterBody);

    // 添加到世界
    this.entity.getWorld().addEntity(bottomWaterEntity);

    Debug.log('FountainComponent', '创建底部水体完成');
  }

  /**
   * 更新喷泉组件
   * @param deltaTime 帧间隔时间（秒）
   */
  public update(deltaTime: number): void {
    // 调用父类更新
    super.update(deltaTime);

    // 如果未启用，则不更新
    if (!this.isEnabled()) return;

    // 更新喷泉喷射
    this.updateJets(deltaTime);

    // 更新水流动力学
    if (this.enableFluidDynamics) {
      this.updateFluidDynamics(deltaTime);
    }

    // 更新音频
    this.updateAudio(deltaTime);

    // 更新粒子效果
    this.updateParticleEffects(deltaTime);
  }

  /**
   * 更新喷泉喷射
   * @param deltaTime 帧间隔时间（秒）
   */
  private updateJets(deltaTime: number): void {
    // 更新模式计时器
    this.modeTimer += deltaTime;

    // 根据喷泉模式更新喷射状态
    switch (this.fountainMode) {
      case FountainMode.CONTINUOUS:
        // 连续模式，始终喷射
        this.jetActive = true;
        break;

      case FountainMode.PULSE:
        // 脉冲模式，定时喷射
        this.jetTimer += deltaTime;
        if (this.jetActive) {
          // 如果正在喷射，检查是否需要停止
          this.jetDurationTimer += deltaTime;
          if (this.jetDurationTimer >= this.jetDuration) {
            this.jetActive = false;
            this.jetDurationTimer = 0;
          }
        } else {
          // 如果未喷射，检查是否需要开始
          if (this.jetTimer >= this.jetInterval) {
            this.jetActive = true;
            this.jetTimer = 0;
          }
        }
        break;

      case FountainMode.ALTERNATING:
        // 交替模式，交替喷射不同的喷头
        this.jetTimer += deltaTime;
        if (this.jetTimer >= this.jetInterval) {
          this.currentJetIndex = (this.currentJetIndex + 1) % this.jetPoints.length;
          this.jetTimer = 0;
        }
        this.jetActive = true;
        break;

      case FountainMode.SEQUENCE:
        // 序列模式，按顺序喷射所有喷头
        this.jetTimer += deltaTime;
        if (this.jetTimer >= this.jetInterval) {
          this.currentJetIndex = (this.currentJetIndex + 1) % this.jetPoints.length;
          this.jetTimer = 0;
        }
        this.jetActive = true;
        break;

      case FountainMode.RANDOM:
        // 随机模式，随机喷射
        this.jetTimer += deltaTime;
        if (this.jetTimer >= this.jetInterval) {
          this.currentJetIndex = Math.floor(Math.random() * this.jetPoints.length);
          this.jetTimer = 0;
          this.jetActive = Math.random() > 0.3; // 70%的概率喷射
        }
        break;

      case FountainMode.WAVE:
        // 波浪模式，创建波浪效果
        this.jetActive = true;
        // 波浪效果在 emitJetParticles 中处理
        break;

      case FountainMode.CASCADE:
        // 级联模式，从一侧到另一侧逐渐开启
        this.jetActive = true;
        // 级联效果在 emitJetParticles 中处理
        break;

      case FountainMode.BURST:
        // 爆发模式，周期性爆发
        const burstFrequency = 0.2; // 每秒爆发次数
        const burstDuration = 1.0 / burstFrequency;
        const burstPhase = (this.modeTimer % burstDuration) / burstDuration;

        // 爆发阶段（快速上升和下降）
        const burstRiseTime = 0.1; // 上升时间占比
        const burstPeakTime = 0.2; // 峰值时间占比
        const burstFallTime = 0.3; // 下降时间占比

        if (burstPhase < burstRiseTime + burstPeakTime + burstFallTime) {
          this.jetActive = true;
        } else {
          this.jetActive = false;
        }
        break;

      case FountainMode.HARMONIC:
        // 和谐模式，使用多个正弦波创建和谐模式
        this.jetActive = true;
        // 和谐效果在 emitJetParticles 中处理
        break;

      case FountainMode.CHAOTIC:
        // 混沌模式，使用噪声函数创建不可预测的模式
        this.jetActive = true;
        // 混沌效果在 emitJetParticles 中处理
        break;

      default:
        this.jetActive = true;
        break;
    }

    // 更新喷射强度
    for (let i = 0; i < this.jetStrengths.length; i++) {
      // 根据喷泉类型更新喷射强度
      switch (this.fountainType) {
        case FountainType.DANCING:
          // 舞蹈喷泉，强度随时间变化
          this.jetStrengths[i] = 0.3 + 0.7 * Math.abs(Math.sin(Date.now() * 0.001 + i * 0.5));
          break;
        case FountainType.MUSICAL:
          // 音乐喷泉，强度随时间变化（模拟音乐节奏）
          this.jetStrengths[i] = 0.3 + 0.7 * Math.abs(Math.sin(Date.now() * 0.002 + i * 1.0));
          break;
        case FountainType.INTERACTIVE:
          // 交互式喷泉，强度保持不变
          break;
        default:
          // 其他类型，添加轻微随机变化
          if (Math.random() < 0.05) {
            this.jetStrengths[i] = 0.8 + Math.random() * 0.4;
          }
          break;
      }
    }

    // 如果喷泉处于活动状态，更新粒子发射
    if (this.jetActive) {
      this.emitJetParticles();
    }
  }

  /**
   * 发射喷射粒子
   */
  private emitJetParticles(): void {
    // 获取水下粒子系统
    const systems = this.entity.getWorld().getSystems();
    const underwaterParticleSystem = systems.find(system => system instanceof UnderwaterParticleSystem);
    if (!underwaterParticleSystem) return;

    // 根据喷泉模式确定要发射的喷射点
    let activeJets: number[] = [];

    // 初始化或更新喷射点活跃状态数组
    if (this.jetActiveArray.length !== this.jetPoints.length) {
      this.jetActiveArray = new Array(this.jetPoints.length).fill(true);
    }

    switch (this.fountainMode) {
      case FountainMode.CONTINUOUS:
        // 所有喷射点都活跃
        for (let i = 0; i < this.jetPoints.length; i++) {
          activeJets.push(i);
        }
        break;

      case FountainMode.ALTERNATING:
      case FountainMode.SEQUENCE:
      case FountainMode.RANDOM:
        // 只有当前喷射点活跃
        activeJets.push(this.currentJetIndex);
        break;

      case FountainMode.PULSE:
        // 所有喷射点都活跃，但只在脉冲期间
        if (this.jetActive) {
          for (let i = 0; i < this.jetPoints.length; i++) {
            activeJets.push(i);
          }
        }
        break;

      case FountainMode.WAVE:
        // 波浪模式，创建波浪效果
        const waveSpeed = 2.0; // 波浪速度

        for (let i = 0; i < this.jetPoints.length; i++) {
          // 计算波浪位置
          const normalizedIndex = i / this.jetPoints.length;
          const wavePhase = normalizedIndex * Math.PI * 2;
          const waveValue = Math.sin(wavePhase + this.modeTimer * waveSpeed);

          // 将波浪值映射到 0-1 范围
          const waveNormalized = (waveValue + 1) / 2;

          // 设置喷射点活跃状态
          this.jetActiveArray[i] = true;

          // 调整喷射强度
          this.jetStrengths[i] = 0.5 + waveNormalized * 0.5;

          // 添加到活跃喷射点
          activeJets.push(i);
        }
        break;

      case FountainMode.CASCADE:
        // 级联模式，从一侧到另一侧逐渐开启
        const cascadeFrequency = 0.3; // 每秒级联次数
        const cascadeDuration = 1.0 / cascadeFrequency;
        const cascadePhase = (this.modeTimer % cascadeDuration) / cascadeDuration;

        for (let i = 0; i < this.jetPoints.length; i++) {
          // 计算级联阈值
          const cascadeThreshold = i / this.jetPoints.length;

          // 设置喷射点活跃状态
          this.jetActiveArray[i] = cascadePhase >= cascadeThreshold;

          // 调整喷射强度（刚开启时强度较高）
          if (this.jetActiveArray[i]) {
            const timeSinceActivation = cascadePhase - cascadeThreshold;
            const strengthFactor = Math.max(0, 1 - timeSinceActivation * 2);
            this.jetStrengths[i] = 0.7 + strengthFactor * 0.3;

            // 添加到活跃喷射点
            activeJets.push(i);
          }
        }
        break;

      case FountainMode.BURST:
        // 爆发模式，周期性爆发
        const burstFrequency = 0.2; // 每秒爆发次数
        const burstDuration = 1.0 / burstFrequency;
        const burstPhase = (this.modeTimer % burstDuration) / burstDuration;

        // 爆发阶段（快速上升和下降）
        const burstRiseTime = 0.1; // 上升时间占比
        const burstPeakTime = 0.2; // 峰值时间占比
        const burstFallTime = 0.3; // 下降时间占比

        let burstStrength = 0;

        if (burstPhase < burstRiseTime) {
          // 上升阶段
          burstStrength = burstPhase / burstRiseTime;
        } else if (burstPhase < burstRiseTime + burstPeakTime) {
          // 峰值阶段
          burstStrength = 1.0;
        } else if (burstPhase < burstRiseTime + burstPeakTime + burstFallTime) {
          // 下降阶段
          const fallProgress = (burstPhase - burstRiseTime - burstPeakTime) / burstFallTime;
          burstStrength = 1.0 - fallProgress;
        }

        // 应用爆发强度
        if (burstStrength > 0) {
          for (let i = 0; i < this.jetPoints.length; i++) {
            this.jetActiveArray[i] = true;
            this.jetStrengths[i] = burstStrength;
            activeJets.push(i);
          }
        }
        break;

      case FountainMode.HARMONIC:
        // 和谐模式，使用多个正弦波创建和谐模式
        for (let i = 0; i < this.jetPoints.length; i++) {
          // 计算归一化索引
          const normalizedIndex = i / this.jetPoints.length;

          // 使用多个频率的正弦波
          const wave1 = Math.sin(normalizedIndex * Math.PI * 2 + this.modeTimer * 1.0);
          const wave2 = Math.sin(normalizedIndex * Math.PI * 4 + this.modeTimer * 1.5);
          const wave3 = Math.sin(normalizedIndex * Math.PI * 8 + this.modeTimer * 2.0);

          // 组合波形
          const combinedWave = (wave1 * 0.5 + wave2 * 0.3 + wave3 * 0.2);

          // 将波形映射到 0-1 范围
          const waveNormalized = (combinedWave + 1) / 2;

          // 设置喷射点活跃状态和强度
          this.jetActiveArray[i] = true;
          this.jetStrengths[i] = 0.3 + waveNormalized * 0.7;

          // 添加到活跃喷射点
          activeJets.push(i);
        }
        break;

      case FountainMode.CHAOTIC:
        // 混沌模式，使用噪声函数创建不可预测的模式
        for (let i = 0; i < this.jetPoints.length; i++) {
          // 计算归一化索引
          const normalizedIndex = i / this.jetPoints.length;

          // 使用噪声函数生成值
          const noiseValue = this.noiseGenerator.noise2D(
            normalizedIndex * 5,
            this.modeTimer * 0.5
          );

          // 将噪声值映射到 0-1 范围
          const noiseNormalized = (noiseValue + 1) / 2;

          // 设置喷射点活跃状态和强度
          this.jetActiveArray[i] = noiseNormalized > 0.3;
          this.jetStrengths[i] = noiseNormalized;

          // 添加到活跃喷射点
          if (this.jetActiveArray[i]) {
            activeJets.push(i);
          }
        }
        break;

      default:
        // 所有喷射点都活跃
        for (let i = 0; i < this.jetPoints.length; i++) {
          activeJets.push(i);
        }
        break;
    }

    // 为每个活跃的喷射点发射粒子
    for (const jetIndex of activeJets) {
      const jetPoint = this.jetPoints[jetIndex];
      const jetDirection = this.jetDirections[jetIndex];
      const jetStrength = this.jetStrengths[jetIndex];

      // 计算喷射高度（基于喷射强度）
      const actualJetHeight = this.jetHeight * jetStrength;

      // 计算喷射终点
      const jetEndPoint = new THREE.Vector3(
        jetPoint.x + jetDirection.x * actualJetHeight,
        jetPoint.y + jetDirection.y * actualJetHeight,
        jetPoint.z + jetDirection.z * actualJetHeight
      );

      // 实现粒子发射
      if (this.enableDropletEffect) {
        // 在喷射终点发射水滴粒子
        // 这里可以添加具体的粒子发射逻辑
        Debug.log('FountainComponent', `发射水滴粒子到位置: ${jetEndPoint.x}, ${jetEndPoint.y}, ${jetEndPoint.z}`);
      }

      if (this.enableSplashEffect) {
        // 在喷射终点发射水花粒子
        // 这里可以添加具体的粒子发射逻辑
        Debug.log('FountainComponent', `发射水花粒子到位置: ${jetEndPoint.x}, ${jetEndPoint.y}, ${jetEndPoint.z}`);
      }
    }
  }

  /**
   * 更新水流动力学
   * @param deltaTime 帧间隔时间（秒）
   */
  private updateFluidDynamics(deltaTime: number): void {
    // 如果没有水流网格，则不更新
    if (!this.flowMesh) return;

    // 获取水流材质
    const material = this.flowMesh.material as THREE.MeshStandardMaterial;
    if (!material) return;

    // 更新水流材质的位移贴图偏移，模拟水流动态
    if (!material.userData.time) {
      material.userData.time = 0;
    }
    material.userData.time += deltaTime * this.getFlowSpeed();

    // 如果材质有位移贴图，更新偏移
    if (material.displacementMap) {
      material.displacementMap.offset.y = -material.userData.time;
    }

    // 如果材质有法线贴图，更新偏移
    if (material.normalMap) {
      material.normalMap.offset.y = -material.userData.time;
    }

    // 应用湍流效果
    this.applyTurbulence(deltaTime);
  }

  /**
   * 应用湍流效果
   * @param deltaTime 帧间隔时间（秒）
   */
  private applyTurbulence(deltaTime: number): void {
    // 如果没有水流网格，则不更新
    if (!this.flowMesh) return;

    // 获取几何体
    const geometry = this.flowMesh.geometry as THREE.CylinderGeometry;
    if (!geometry || !geometry.attributes.position || !geometry.userData.originalPositions) return;

    // 获取顶点位置
    const positions = geometry.attributes.position.array as Float32Array;
    const count = positions.length / 3;

    // 更新时间
    geometry.userData.time += deltaTime * this.turbulenceSpeed;
    const time = geometry.userData.time;

    // 应用湍流扰动
    for (let i = 0; i < count; i++) {
      const index = i * 3;
      const originalX = geometry.userData.originalPositions[index];
      const originalY = geometry.userData.originalPositions[index + 1];
      const originalZ = geometry.userData.originalPositions[index + 2];

      // 使用噪声函数模拟湍流
      const noise = this.simplex3D(
        originalX * this.turbulenceFrequency,
        originalY * this.turbulenceFrequency,
        time
      );

      // 应用扰动
      positions[index] = originalX + noise * this.turbulenceStrength * 0.1;
      positions[index + 2] = originalZ + noise * this.turbulenceStrength * 0.1;
    }

    // 更新几何体
    geometry.attributes.position.needsUpdate = true;
  }

  /**
   * 更新音频
   * @param deltaTime 帧间隔时间（秒）
   */
  private updateAudio(_deltaTime: number): void {
    // 如果未启用声音效果或没有音频源，则不更新
    if (!this.enableSoundEffect || !this.audioSource) return;

    // 更新音频源位置
    const position = this.getPosition();
    this.audioSource.setPosition(position.x, position.y, position.z);

    // 根据喷泉活动状态调整音量
    let volumeFactor = 0.5;
    if (this.jetActive) {
      // 如果喷泉活跃，增加音量
      volumeFactor = 1.0;
    }

    // 设置音量
    this.audioSource.setVolume(this.soundEffectVolume * volumeFactor);
  }

  /**
   * 更新粒子效果
   * @param deltaTime 帧间隔时间（秒）
   */
  private updateParticleEffects(_deltaTime: number): void {
    // 如果未启用水雾效果、水花效果和水滴效果，则不更新
    if (!this.enableMistEffect && !this.enableSplashEffect && !this.enableDropletEffect) return;

    // 获取水下粒子系统
    const systems = this.entity.getWorld().getSystems();
    const underwaterParticleSystem = systems.find(system => system instanceof UnderwaterParticleSystem);
    if (!underwaterParticleSystem) {
      return;
    }

    // 粒子效果会在粒子系统的 update 方法中自动更新
    // 这里可以添加特殊的粒子效果逻辑，比如根据喷泉状态调整粒子强度等

    // 注意：UnderwaterParticleSystem 没有 updateParticleGroupPosition 方法
    // 粒子位置更新是在系统内部自动处理的
  }

  /**
   * 3D Simplex噪声函数
   * @param x X坐标
   * @param y Y坐标
   * @param z Z坐标
   * @returns 噪声值
   */
  private simplex3D(x: number, y: number, z: number): number {
    return this.noiseGenerator.noise3D(x, y, z);
  }

  /**
   * 设置喷泉类型
   * @param type 喷泉类型
   */
  public setFountainType(type: FountainType): void {
    this.fountainType = type;
    this.initializeJetPoints();
  }

  /**
   * 设置喷泉模式
   * @param mode 喷泉模式
   */
  public setFountainMode(mode: FountainMode): void {
    this.fountainMode = mode;
    this.jetTimer = 0;
    this.jetDurationTimer = 0;
    this.currentJetIndex = 0;
  }

  /**
   * 设置喷泉喷射高度
   * @param height 喷射高度
   */
  public setJetHeight(height: number): void {
    this.jetHeight = height;
    // 重新创建水流网格
    if (this.flowMesh) {
      // TODO: 实现移除对象的方法
      this.flowMesh = null;
    }
    this.createFlowMesh();
  }

  /**
   * 设置喷泉喷射角度
   * @param angle 喷射角度
   */
  public setJetAngle(angle: number): void {
    this.jetAngle = angle;
    this.initializeJetPoints();
  }

  /**
   * 设置喷泉喷射数量
   * @param count 喷射数量
   */
  public setJetCount(count: number): void {
    this.jetCount = count;
    this.initializeJetPoints();
  }

  /**
   * 设置喷泉喷射间隔
   * @param interval 喷射间隔
   */
  public setJetInterval(interval: number): void {
    this.jetInterval = interval;
  }

  /**
   * 设置喷泉喷射持续时间
   * @param duration 喷射持续时间
   */
  public setJetDuration(duration: number): void {
    this.jetDuration = duration;
  }

  /**
   * 设置喷泉喷射随机性
   * @param randomness 喷射随机性
   */
  public setJetRandomness(randomness: number): void {
    this.jetRandomness = randomness;
  }

  /**
   * 设置湍流强度
   * @param strength 湍流强度
   */
  public setTurbulenceStrength(strength: number): void {
    this.turbulenceStrength = strength;
  }

  /**
   * 设置湍流频率
   * @param frequency 湍流频率
   */
  public setTurbulenceFrequency(frequency: number): void {
    this.turbulenceFrequency = frequency;
  }

  /**
   * 设置湍流速度
   * @param speed 湍流速度
   */
  public setTurbulenceSpeed(speed: number): void {
    this.turbulenceSpeed = speed;
  }

  /**
   * 设置是否启用水雾效果
   * @param enable 是否启用
   */
  public setEnableMistEffect(enable: boolean): void {
    this.enableMistEffect = enable;
  }

  /**
   * 设置水雾效果强度
   * @param strength 效果强度
   */
  public setMistEffectStrength(strength: number): void {
    this.mistEffectStrength = strength;
  }

  /**
   * 设置是否启用水花效果
   * @param enable 是否启用
   */
  public setEnableSplashEffect(enable: boolean): void {
    this.enableSplashEffect = enable;
  }

  /**
   * 设置水花效果强度
   * @param strength 效果强度
   */
  public setSplashEffectStrength(strength: number): void {
    this.splashEffectStrength = strength;
  }

  /**
   * 设置是否启用水滴效果
   * @param enable 是否启用
   */
  public setEnableDropletEffect(enable: boolean): void {
    this.enableDropletEffect = enable;
  }

  /**
   * 设置水滴效果强度
   * @param strength 效果强度
   */
  public setDropletEffectStrength(strength: number): void {
    this.dropletEffectStrength = strength;
  }

  /**
   * 设置是否启用声音效果
   * @param enable 是否启用
   */
  public setEnableSoundEffect(enable: boolean): void {
    this.enableSoundEffect = enable;
    if (enable && !this.audioSource) {
      this.initializeAudio();
    } else if (!enable && this.audioSource) {
      // 获取音频系统
      const systems = this.entity.getWorld().getSystems();
      const audioSystem = systems.find(system => system instanceof AudioSystem);
      if (audioSystem) {
        (audioSystem as unknown as AudioSystem).stop(`fountain_${this.entity.id}`);
      }
      this.audioSource = null;
    }
  }

  /**
   * 设置声音效果音量
   * @param volume 音量
   */
  public setSoundEffectVolume(volume: number): void {
    this.soundEffectVolume = volume;
    if (this.audioSource) {
      this.audioSource.setVolume(volume);
    }
  }

  /**
   * 设置是否启用水流动力学
   * @param enable 是否启用
   */
  public setEnableFluidDynamics(enable: boolean): void {
    this.enableFluidDynamics = enable;
  }
}
