/**
 * 车轮约束
 * 模拟车轮的悬挂和转向
 */
import * as THREE from 'three';
import * as CANNON from 'cannon-es';
import type { Entity } from '../../core/Entity';
import type { PhysicsBody } from '../PhysicsBody';
import { PhysicsConstraint, ConstraintType } from './PhysicsConstraint';

/**
 * 车轮约束选项
 */
export interface WheelConstraintOptions {
  /** 悬挂轴向 */
  axisA?: THREE.Vector3;
  /** 转向轴向 */
  axisB?: THREE.Vector3;
  /** 悬挂刚度 */
  suspensionStiffness?: number;
  /** 悬挂阻尼 */
  suspensionDamping?: number;
  /** 悬挂长度 */
  suspensionLength?: number;
  /** 悬挂最大长度 */
  suspensionMaxLength?: number;
  /** 悬挂最小长度 */
  suspensionMinLength?: number;
  /** 转向角度 */
  steeringAngle?: number;
  /** 是否允许连接的物体之间碰撞 */
  collideConnected?: boolean;
  /** 最大力 */
  maxForce?: number;
}

/**
 * 车轮约束
 */
export class WheelConstraint extends PhysicsConstraint {
  /** 组件类型 */
  public static readonly type: string = 'WheelConstraint';

  /** 悬挂轴向 */
  private axisA: THREE.Vector3;

  /** 转向轴向 */
  private axisB: THREE.Vector3;

  /** 悬挂刚度 */
  private suspensionStiffness: number;

  /** 悬挂阻尼 */
  private suspensionDamping: number;

  /** 悬挂长度 */
  private suspensionLength: number;

  /** 悬挂最大长度 */
  private suspensionMaxLength: number;

  /** 悬挂最小长度 */
  private suspensionMinLength: number;

  /** 转向角度 */
  private steeringAngle: number;

  /** 最大力 */
  private maxForce: number;

  /** 悬挂约束 */
  private suspensionConstraint: CANNON.PointToPointConstraint | null = null;

  /** 转向约束 */
  private steeringConstraint: CANNON.HingeConstraint | null = null;

  /**
   * 创建车轮约束
   * @param targetEntity 目标实体
   * @param options 约束选项
   */
  constructor(targetEntity: Entity | null = null, options: WheelConstraintOptions = {}) {
    super(ConstraintType.WHEEL, targetEntity, options);

    // 设置轴向
    this.axisA = options.axisA ? options.axisA.clone() : new THREE.Vector3(0, 1, 0);
    this.axisB = options.axisB ? options.axisB.clone() : new THREE.Vector3(0, 0, 1);

    // 设置悬挂参数
    this.suspensionStiffness = options.suspensionStiffness !== undefined ? options.suspensionStiffness : 100;
    this.suspensionDamping = options.suspensionDamping !== undefined ? options.suspensionDamping : 10;
    this.suspensionLength = options.suspensionLength !== undefined ? options.suspensionLength : 0.2;
    this.suspensionMaxLength = options.suspensionMaxLength !== undefined ? options.suspensionMaxLength : 0.3;
    this.suspensionMinLength = options.suspensionMinLength !== undefined ? options.suspensionMinLength : 0.1;

    // 设置转向角度
    this.steeringAngle = options.steeringAngle !== undefined ? options.steeringAngle : 0;

    // 设置最大力
    this.maxForce = options.maxForce !== undefined ? options.maxForce : 1e6;
  }

  /**
   * 创建约束
   */
  protected createConstraint(): void {
    // 获取源物体和目标物体
    const bodyA = this.getSourceBody();
    const bodyB = this.getTargetBody();

    // 如果没有源物体或目标物体，则无法创建约束
    if (!bodyA || !bodyB) {
      console.warn('无法创建车轮约束：缺少源物体或目标物体');
      return;
    }

    // 创建悬挂约束
    this.suspensionConstraint = new CANNON.PointToPointConstraint(
      bodyA,
      new CANNON.Vec3(0, -this.suspensionLength, 0),
      bodyB,
      new CANNON.Vec3(0, 0, 0),
      this.maxForce
    );

    // 创建转向约束
    this.steeringConstraint = new CANNON.HingeConstraint(
      bodyA,
      bodyB,
      {
        pivotA: new CANNON.Vec3(0, -this.suspensionLength, 0),
        axisA: new CANNON.Vec3(this.axisA.x, this.axisA.y, this.axisA.z),
        pivotB: new CANNON.Vec3(0, 0, 0),
        axisB: new CANNON.Vec3(this.axisB.x, this.axisB.y, this.axisB.z)
      }
    );

    // 设置转向角度
    this.steeringConstraint.enableMotor();
    this.steeringConstraint.setMotorSpeed(0);
    this.steeringConstraint.setMotorMaxForce(this.maxForce);

    // 设置是否允许连接的物体之间碰撞
    this.suspensionConstraint.collideConnected = this.collideConnected;
    this.steeringConstraint.collideConnected = this.collideConnected;

    // 保存约束
    this.constraint = this.suspensionConstraint;

    // 添加转向约束到物理世界
    if (this.world) {
      this.world.addConstraint(this.steeringConstraint);
    }
  }

  /**
   * 更新约束
   */
  public update(): void {
    if (!this.suspensionConstraint || !this.steeringConstraint) {
      return;
    }

    // 更新转向角度
    // 注意：cannon-es 的 HingeConstraint 没有 setMotorTarget 方法
    // 我们使用 setMotorSpeed 来控制转向速度，实现转向角度控制
    const targetSpeed = this.steeringAngle * 10; // 简单的比例控制
    this.steeringConstraint.setMotorSpeed(targetSpeed);
  }

  /**
   * 设置悬挂轴向
   * @param axis 轴向
   */
  public setAxisA(axis: THREE.Vector3): void {
    this.axisA.copy(axis);

    // 如果约束已创建，需要重新创建
    this.recreateConstraint();
  }

  /**
   * 获取悬挂轴向
   * @returns 轴向
   */
  public getAxisA(): THREE.Vector3 {
    return this.axisA.clone();
  }

  /**
   * 设置转向轴向
   * @param axis 轴向
   */
  public setAxisB(axis: THREE.Vector3): void {
    this.axisB.copy(axis);

    // 如果约束已创建，需要重新创建
    this.recreateConstraint();
  }

  /**
   * 获取转向轴向
   * @returns 轴向
   */
  public getAxisB(): THREE.Vector3 {
    return this.axisB.clone();
  }

  /**
   * 设置悬挂刚度
   * @param stiffness 刚度
   */
  public setSuspensionStiffness(stiffness: number): void {
    this.suspensionStiffness = stiffness;

    // 如果约束已创建，需要重新创建
    this.recreateConstraint();
  }

  /**
   * 获取悬挂刚度
   * @returns 刚度
   */
  public getSuspensionStiffness(): number {
    return this.suspensionStiffness;
  }

  /**
   * 设置悬挂阻尼
   * @param damping 阻尼
   */
  public setSuspensionDamping(damping: number): void {
    this.suspensionDamping = damping;

    // 如果约束已创建，需要重新创建
    this.recreateConstraint();
  }

  /**
   * 获取悬挂阻尼
   * @returns 阻尼
   */
  public getSuspensionDamping(): number {
    return this.suspensionDamping;
  }

  /**
   * 设置悬挂长度
   * @param length 长度
   */
  public setSuspensionLength(length: number): void {
    this.suspensionLength = length;

    // 如果约束已创建，需要重新创建
    this.recreateConstraint();
  }

  /**
   * 获取悬挂长度
   * @returns 长度
   */
  public getSuspensionLength(): number {
    return this.suspensionLength;
  }

  /**
   * 重新创建约束
   */
  private recreateConstraint(): void {
    if (this.initialized && this.constraint && this.world) {
      // 移除旧约束
      this.world.removeConstraint(this.constraint);
      if (this.steeringConstraint) {
        this.world.removeConstraint(this.steeringConstraint);
      }

      // 重置状态
      this.constraint = null;
      this.suspensionConstraint = null;
      this.steeringConstraint = null;
      this.initialized = false;

      // 重新初始化
      this.initialize(this.world);
    }
  }

  /**
   * 设置转向角度
   * @param angle 角度
   */
  public setSteeringAngle(angle: number): void {
    this.steeringAngle = angle;

    // 更新约束
    this.update();
  }

  /**
   * 获取转向角度
   * @returns 角度
   */
  public getSteeringAngle(): number {
    return this.steeringAngle;
  }

  /**
   * 销毁约束
   */
  public dispose(): void {
    // 移除转向约束
    if (this.steeringConstraint && this.world) {
      this.world.removeConstraint(this.steeringConstraint);
      this.steeringConstraint = null;
    }

    // 调用父类的销毁方法
    (super as any).dispose();
  }
}
