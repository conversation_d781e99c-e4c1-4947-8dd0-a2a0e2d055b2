/**
 * 地形实例化渲染系统
 * 用于优化地形上大量相同对象的渲染
 */
import * as THREE from 'three';
import { System } from '../core/System';
import type { Entity } from '../core/Entity';
import { type type Camera   } from '../rendering/Camera';
import { Scene } from '../scene/Scene';
import { TerrainComponent } from './components/TerrainComponent';
import { Debug } from '../utils/Debug';
import { EventEmitter } from '../utils/EventEmitter';
import { Octree } from '../utils/Octree';

/**
 * 地形实例数据接口
 */
export interface TerrainInstanceData {
  /** 位置 */
  position: THREE.Vector3;
  /** 旋转 */
  rotation: THREE.Euler;
  /** 缩放 */
  scale: THREE.Vector3;
  /** 颜色 */
  color: THREE.Color;
  /** 可见性 */
  visible: boolean;
  /** 用户数据 */
  userData: any;
}

/**
 * 地形实例组接口
 */
export interface TerrainInstanceGroup {
  /** 组ID */
  id: string;
  /** 几何体 */
  geometry: THREE.BufferGeometry;
  /** 材质 */
  material: THREE.Material;
  /** 实例化网格 */
  instancedMesh: THREE.InstancedMesh;
  /** 实例数据 */
  instances: TerrainInstanceData[];
  /** 可用索引 */
  availableIndices: number[];
  /** 实例ID到索引的映射 */
  instanceIdToIndex: Map<string, number>;
  /** 是否可见 */
  visible: boolean;
  /** 是否需要更新 */
  needsUpdate: boolean;
  /** 用户数据 */
  userData: any;
}

/**
 * 地形实例化渲染系统事件类型
 */
export enum TerrainInstancedRenderingSystemEventType {
  /** 实例组创建 */
  GROUP_CREATED = 'group_created',
  /** 实例组销毁 */
  GROUP_DESTROYED = 'group_destroyed',
  /** 实例添加 */
  INSTANCE_ADDED = 'instance_added',
  /** 实例移除 */
  INSTANCE_REMOVED = 'instance_removed',
  /** 实例更新 */
  INSTANCE_UPDATED = 'instance_updated'
}

/**
 * 地形实例化渲染系统配置接口
 */
export interface TerrainInstancedRenderingSystemOptions {
  /** 是否启用 */
  enabled?: boolean;
  /** 是否自动更新 */
  autoUpdate?: boolean;
  /** 更新频率 */
  updateFrequency?: number;
  /** 最大批处理大小 */
  maxBatchSize?: number;
  /** 是否使用视锥体剔除 */
  useFrustumCulling?: boolean;
  /** 是否使用八叉树 */
  useOctree?: boolean;
  /** 是否使用LOD */
  useInstanceLOD?: boolean;
  /** 是否使用阴影 */
  useInstanceShadow?: boolean;
  /** 是否使用调试可视化 */
  useDebugVisualization?: boolean;
}

/**
 * 地形实例化渲染系统类
 */
export class TerrainInstancedRenderingSystem extends System {
  /** 系统类型 */
  public static readonly TYPE: string = 'TerrainInstancedRenderingSystem';

  /** 是否启用 */
  private enabled: boolean;

  /** 是否自动更新 */
  private autoUpdate: boolean;

  /** 更新频率 */
  private updateFrequency: number;

  /** 最大批处理大小 */
  private maxBatchSize: number;

  /** 是否使用视锥体剔除 */
  private useFrustumCulling: boolean;

  /** 是否使用八叉树 */
  private useOctree: boolean;

  /** 是否使用LOD */
  private useInstanceLOD: boolean;

  /** 是否使用阴影 */
  private useInstanceShadow: boolean;

  /** 是否使用调试可视化 */
  private useDebugVisualization: boolean;

  /** 地形实体映射 */
  private terrainEntities: Map<Entity, TerrainComponent>;

  /** 实例组映射 */
  private instanceGroups: Map<string, TerrainInstanceGroup>;

  /** 几何体到实例组的映射 */
  private geometryToGroup: Map<string, TerrainInstanceGroup>;

  /** 八叉树 */
  private octree: Octree | null;

  /** 视锥体 */
  private frustum: THREE.Frustum;

  /** 帧计数器 */
  private frameCount: number;

  /** 事件发射器 */
  private eventEmitter: EventEmitter;

  /** 调试网格 */
  private debugMeshes: THREE.Mesh[];

  /** 实例组计数器 */
  private groupCounter: number;

  /** 实例计数器 */
  private instanceCounter: number;

  /**
   * 创建地形实例化渲染系统
   * @param options 配置选项
   */
  constructor(options: TerrainInstancedRenderingSystemOptions = {}) {
    super();

    this.enabled = options.enabled !== undefined ? options.enabled : true;
    this.autoUpdate = options.autoUpdate !== undefined ? options.autoUpdate : true;
    this.updateFrequency = options.updateFrequency || 1;
    this.maxBatchSize = options.maxBatchSize || 1000;
    this.useFrustumCulling = options.useFrustumCulling !== undefined ? options.useFrustumCulling : true;
    this.useOctree = options.useOctree !== undefined ? options.useOctree : true;
    this.useInstanceLOD = options.useInstanceLOD !== undefined ? options.useInstanceLOD : true;
    this.useInstanceShadow = options.useInstanceShadow !== undefined ? options.useInstanceShadow : true;
    this.useDebugVisualization = options.useDebugVisualization !== undefined ? options.useDebugVisualization : false;

    this.terrainEntities = new Map();
    this.instanceGroups = new Map();
    this.geometryToGroup = new Map();
    this.octree = null;
    this.frustum = new THREE.Frustum();
    this.frameCount = 0;
    this.eventEmitter = new EventEmitter();
    this.debugMeshes = [];
    this.groupCounter = 0;
    this.instanceCounter = 0;
  }

  /**
   * 获取系统类型
   * @returns 系统类型
   */
  public getType(): string {
    return TerrainInstancedRenderingSystem.TYPE;
  }

  /**
   * 初始化系统
   */
  public initialize(): void {
    // 初始化八叉树
    if (this.useOctree) {
      this.initializeOctree();
    }
  }

  /**
   * 初始化八叉树
   */
  private initializeOctree(): void {
    // 创建八叉树
    this.octree = new Octree({
      size: 10000, // 足够大以包含所有地形
      maxDepth: 8,
      maxObjects: 100
    });
  }

  /**
   * 更新系统
   * @param deltaTime 帧间隔时间（秒）
   */
  public update(deltaTime: number): void {
    if (!this.enabled || !this.autoUpdate) {
      return;
    }

    // 按照更新频率更新
    this.frameCount++;
    if (this.frameCount % this.updateFrequency !== 0) {
      return;
    }

    // 获取相机
    const camera = this.getCamera();
    if (!camera) {
      return;
    }

    // 更新视锥体
    if (this.useFrustumCulling) {
      this.updateFrustum(camera);
    }

    // 更新所有实例组
    this.updateInstanceGroups(camera);

    // 更新调试可视化
    if (this.useDebugVisualization) {
      this.updateDebugVisualization();
    }
  }

  /**
   * 获取相机
   * @returns 相机
   */
  private getCamera(): Camera | null {
    // 获取相机组件
    const cameras = this.entityManager.getComponentsOfType<Camera>('Camera');
    if (cameras.length === 0) {
      return null;
    }

    // 返回第一个相机
    return cameras[0];
  }

  /**
   * 获取场景
   * @returns 场景
   */
  private getScene(): Scene | null {
    // 获取场景组件
    const scenes = this.entityManager.getComponentsOfType<Scene>('Scene');
    if (scenes.length === 0) {
      return null;
    }

    // 返回第一个场景
    return scenes[0];
  }

  /**
   * 更新视锥体
   * @param camera 相机
   */
  private updateFrustum(camera: Camera): void {
    // 获取相机视锥体
    const projScreenMatrix = new THREE.Matrix4();
    projScreenMatrix.multiplyMatrices(
      camera.getThreeCamera().projectionMatrix,
      camera.getThreeCamera().matrixWorldInverse
    );
    this.frustum.setFromProjectionMatrix(projScreenMatrix);
  }

  /**
   * 更新所有实例组
   * @param camera 相机
   */
  private updateInstanceGroups(camera: Camera): void {
    // 获取相机位置
    const cameraPosition = camera.getThreeCamera().position;

    // 如果使用八叉树，使用八叉树更新实例
    if (this.useOctree && this.octree) {
      this.updateInstancesWithOctree(camera);
    } else {
      // 否则，遍历所有实例组
      this.updateInstancesWithBruteForce(camera);
    }
  }

  /**
   * 使用八叉树更新实例
   * @param camera 相机
   */
  private updateInstancesWithOctree(camera: Camera): void {
    if (!this.octree) {
      return;
    }

    // 获取相机位置
    const cameraPosition = camera.getThreeCamera().position;

    // 获取视锥体内的节点
    const visibleNodes = this.octree.getFrustumIntersectedNodes(this.frustum);

    // 创建可见实例集合
    const visibleInstances = new Set<string>();

    // 遍历可见节点
    for (const node of visibleNodes) {
      // 获取节点中的实例
      const instancesInNode = node.objects as string[];

      // 遍历实例
      for (const instanceId of instancesInNode) {
        // 添加到可见实例集合
        visibleInstances.add(instanceId);

        // 查找实例所在的组
        for (const group of this.instanceGroups.values()) {
          const index = group.instanceIdToIndex.get(instanceId);
          if (index !== undefined) {
            // 获取实例数据
            const instanceData = group.instances[index];

            // 计算相机到实例的距离
            const distance = cameraPosition.distanceTo(instanceData.position);

            // 更新实例可见性
            instanceData.visible = true;

            // 更新实例矩阵
            this.updateInstanceMatrix(group, index, instanceData);

            break;
          }
        }
      }
    }

    // 隐藏不在视锥体内的实例
    for (const group of this.instanceGroups.values()) {
      for (let i = 0; i < group.instances.length; i++) {
        const instanceData = group.instances[i];
        const instanceId = this.getInstanceIdByIndex(group, i);

        if (instanceId && !visibleInstances.has(instanceId) && instanceData.visible) {
          // 隐藏实例
          instanceData.visible = false;

          // 更新实例矩阵
          this.updateInstanceMatrix(group, i, instanceData);
        }
      }
    }
  }

  /**
   * 使用暴力方法更新实例
   * @param camera 相机
   */
  private updateInstancesWithBruteForce(camera: Camera): void {
    // 获取相机位置
    const cameraPosition = camera.getThreeCamera().position;

    // 遍历所有实例组
    for (const group of this.instanceGroups.values()) {
      // 遍历所有实例
      for (let i = 0; i < group.instances.length; i++) {
        const instanceData = group.instances[i];

        // 计算相机到实例的距离
        const distance = cameraPosition.distanceTo(instanceData.position);

        // 创建包围球
        const boundingSphere = new THREE.Sphere(
          instanceData.position,
          Math.max(instanceData.scale.x, instanceData.scale.y, instanceData.scale.z)
        );

        // 检查是否在视锥体内
        const isVisible = !this.useFrustumCulling || this.frustum.intersectsSphere(boundingSphere);

        // 更新实例可见性
        if (instanceData.visible !== isVisible) {
          instanceData.visible = isVisible;

          // 更新实例矩阵
          this.updateInstanceMatrix(group, i, instanceData);
        }
      }
    }
  }

  /**
   * 根据索引获取实例ID
   * @param group 实例组
   * @param index 索引
   * @returns 实例ID
   */
  private getInstanceIdByIndex(group: TerrainInstanceGroup, index: number): string | null {
    for (const [instanceId, idx] of group.instanceIdToIndex.entries()) {
      if (idx === index) {
        return instanceId;
      }
    }
    return null;
  }

  /**
   * 更新调试可视化
   */
  private updateDebugVisualization(): void {
    // 清除现有的调试网格
    for (const mesh of this.debugMeshes) {
      if (mesh.parent) {
        mesh.parent.remove(mesh);
      }
    }
    this.debugMeshes = [];

    // 获取场景
    const scene = this.getScene();
    if (!scene) {
      return;
    }

    // 创建调试材质
    const visibleMaterial = new THREE.MeshBasicMaterial({
      color: 0x00ff00,
      wireframe: true,
      transparent: true,
      opacity: 0.3
    });
    const hiddenMaterial = new THREE.MeshBasicMaterial({
      color: 0xff0000,
      wireframe: true,
      transparent: true,
      opacity: 0.3
    });

    // 遍历所有实例组
    for (const group of this.instanceGroups.values()) {
      // 遍历所有实例
      for (let i = 0; i < group.instances.length; i++) {
        const instanceData = group.instances[i];

        // 创建包围球网格
        const radius = Math.max(instanceData.scale.x, instanceData.scale.y, instanceData.scale.z);
        const geometry = new THREE.SphereGeometry(radius, 8, 8);
        const material = instanceData.visible ? visibleMaterial : hiddenMaterial;
        const mesh = new THREE.Mesh(geometry, material);

        // 设置位置
        mesh.position.copy(instanceData.position);

        // 添加到场景
        scene.getThreeScene().add(mesh);
        this.debugMeshes.push(mesh);
      }
    }
  }

  /**
   * 添加地形实体
   * @param entity 实体
   * @param component 地形组件
   */
  public addTerrainEntity(entity: Entity, component: TerrainComponent): void {
    this.terrainEntities.set(entity, component);
  }

  /**
   * 移除地形实体
   * @param entity 实体
   */
  public removeTerrainEntity(entity: Entity): void {
    this.terrainEntities.delete(entity);
  }

  /**
   * 创建实例组
   * @param geometry 几何体
   * @param material 材质
   * @param maxInstances 最大实例数
   * @returns 实例组
   */
  public createInstanceGroup(geometry: THREE.BufferGeometry, material: THREE.Material, maxInstances: number = this.maxBatchSize): TerrainInstanceGroup {
    // 生成组ID
    const groupId = `group_${this.groupCounter++}`;

    // 创建实例化网格
    const instancedMesh = new THREE.InstancedMesh(geometry, material, maxInstances);
    instancedMesh.name = `instanced_mesh_${groupId}`;
    instancedMesh.frustumCulled = false; // 禁用自动视锥体剔除，由系统自己处理
    instancedMesh.castShadow = this.useInstanceShadow;
    instancedMesh.receiveShadow = this.useInstanceShadow;

    // 创建可用索引数组
    const availableIndices = Array.from({ length: maxInstances }, (_, i) => i);

    // 创建实例组
    const group: TerrainInstanceGroup = {
      id: groupId,
      geometry,
      material,
      instancedMesh,
      instances: [],
      availableIndices,
      instanceIdToIndex: new Map(),
      visible: true,
      needsUpdate: false,
      userData: {}
    };

    // 添加到实例组映射
    this.instanceGroups.set(groupId, group);

    // 添加到几何体映射
    this.geometryToGroup.set(`${geometry.uuid}_${material.uuid}`, group);

    // 发出实例组创建事件
    this.eventEmitter.emit(TerrainInstancedRenderingSystemEventType.GROUP_CREATED, group);

    return group;
  }

  /**
   * 销毁实例组
   * @param groupId 组ID
   */
  public destroyInstanceGroup(groupId: string): void {
    const group = this.instanceGroups.get(groupId);
    if (!group) {
      return;
    }

    // 从场景中移除实例化网格
    if (group.instancedMesh.parent) {
      group.instancedMesh.parent.remove(group.instancedMesh);
    }

    // 释放资源
    (group.instancedMesh as any).dispose();

    // 从实例组映射中移除
    this.instanceGroups.delete(groupId);

    // 从几何体映射中移除
    this.geometryToGroup.delete(`${group.geometry.uuid}_${group.material.uuid}`);

    // 发出实例组销毁事件
    this.eventEmitter.emit(TerrainInstancedRenderingSystemEventType.GROUP_DESTROYED, group);
  }

  /**
   * 获取或创建实例组
   * @param geometry 几何体
   * @param material 材质
   * @returns 实例组
   */
  public getOrCreateInstanceGroup(geometry: THREE.BufferGeometry, material: THREE.Material): TerrainInstanceGroup {
    // 查找现有的实例组
    const key = `${geometry.uuid}_${material.uuid}`;
    const existingGroup = this.geometryToGroup.get(key);

    if (existingGroup) {
      return existingGroup;
    }

    // 创建新的实例组
    return this.createInstanceGroup(geometry, material);
  }

  /**
   * 添加实例
   * @param geometry 几何体
   * @param material 材质
   * @param instanceData 实例数据
   * @returns 实例ID
   */
  public addInstance(geometry: THREE.BufferGeometry, material: THREE.Material, instanceData: TerrainInstanceData): string {
    // 生成实例ID
    const instanceId = `instance_${this.instanceCounter++}`;

    // 获取或创建实例组
    const group = this.getOrCreateInstanceGroup(geometry, material);

    // 如果实例组已满，则创建新的实例组
    if (group.availableIndices.length === 0) {
      const newGroup = this.createInstanceGroup(geometry, material);
      return this.addInstance(geometry, material, instanceData);
    }

    // 获取可用索引
    const index = group.availableIndices.pop()!;

    // 添加实例数据
    group.instances.push(instanceData);
    group.instanceIdToIndex.set(instanceId, index);

    // 更新实例化网格
    this.updateInstanceMatrix(group, index, instanceData);

    // 如果使用八叉树，添加到八叉树
    if (this.useOctree && this.octree) {
      this.octree.insert(instanceId, instanceData.position, Math.max(instanceData.scale.x, instanceData.scale.y, instanceData.scale.z));
    }

    // 发出实例添加事件
    this.eventEmitter.emit(TerrainInstancedRenderingSystemEventType.INSTANCE_ADDED, group, instanceId, instanceData);

    return instanceId;
  }

  /**
   * 移除实例
   * @param groupId 组ID
   * @param instanceId 实例ID
   */
  public removeInstance(groupId: string, instanceId: string): void {
    const group = this.instanceGroups.get(groupId);
    if (!group) {
      return;
    }

    // 获取实例索引
    const index = group.instanceIdToIndex.get(instanceId);
    if (index === undefined) {
      return;
    }

    // 获取实例数据
    const instanceData = group.instances[index];

    // 从实例组中移除
    group.instances.splice(index, 1);
    group.instanceIdToIndex.delete(instanceId);
    group.availableIndices.push(index);

    // 隐藏实例
    const matrix = new THREE.Matrix4();
    matrix.makeScale(0, 0, 0);
    group.instancedMesh.setMatrixAt(index, matrix);
    group.instancedMesh.instanceMatrix.needsUpdate = true;

    // 如果使用八叉树，从八叉树中移除
    if (this.useOctree && this.octree) {
      this.octree.remove(instanceId);
    }

    // 发出实例移除事件
    this.eventEmitter.emit(TerrainInstancedRenderingSystemEventType.INSTANCE_REMOVED, group, instanceId, instanceData);
  }

  /**
   * 更新实例
   * @param groupId 组ID
   * @param instanceId 实例ID
   * @param instanceData 实例数据
   */
  public updateInstance(groupId: string, instanceId: string, instanceData: Partial<TerrainInstanceData>): void {
    const group = this.instanceGroups.get(groupId);
    if (!group) {
      return;
    }

    // 获取实例索引
    const index = group.instanceIdToIndex.get(instanceId);
    if (index === undefined) {
      return;
    }

    // 获取实例数据
    const currentData = group.instances[index];

    // 更新实例数据
    if (instanceData.position) {
      currentData.position.copy(instanceData.position);
    }

    if (instanceData.rotation) {
      currentData.rotation.copy(instanceData.rotation);
    }

    if (instanceData.scale) {
      currentData.scale.copy(instanceData.scale);
    }

    if (instanceData.color) {
      currentData.color.copy(instanceData.color);
    }

    if (instanceData.visible !== undefined) {
      currentData.visible = instanceData.visible;
    }

    if (instanceData.userData) {
      currentData.userData = { ...currentData.userData, ...instanceData.userData };
    }

    // 更新实例化网格
    this.updateInstanceMatrix(group, index, currentData);

    // 如果使用八叉树，更新八叉树
    if (this.useOctree && this.octree && instanceData.position) {
      this.octree.update(instanceId, instanceData.position, Math.max(currentData.scale.x, currentData.scale.y, currentData.scale.z));
    }

    // 发出实例更新事件
    this.eventEmitter.emit(TerrainInstancedRenderingSystemEventType.INSTANCE_UPDATED, group, instanceId, currentData);
  }

  /**
   * 更新实例矩阵
   * @param group 实例组
   * @param index 索引
   * @param instanceData 实例数据
   */
  private updateInstanceMatrix(group: TerrainInstanceGroup, index: number, instanceData: TerrainInstanceData): void {
    // 创建矩阵
    const matrix = new THREE.Matrix4();

    // 如果实例不可见，则使用零缩放
    if (!instanceData.visible) {
      matrix.makeScale(0, 0, 0);
    } else {
      // 设置位置、旋转和缩放
      matrix.compose(
        instanceData.position,
        new THREE.Quaternion().setFromEuler(instanceData.rotation),
        instanceData.scale
      );
    }

    // 更新实例化网格
    group.instancedMesh.setMatrixAt(index, matrix);

    // 更新颜色
    group.instancedMesh.setColorAt(index, instanceData.color);

    // 标记需要更新
    group.instancedMesh.instanceMatrix.needsUpdate = true;
    if (group.instancedMesh.instanceColor) {
      group.instancedMesh.instanceColor.needsUpdate = true;
    }
  }
}
