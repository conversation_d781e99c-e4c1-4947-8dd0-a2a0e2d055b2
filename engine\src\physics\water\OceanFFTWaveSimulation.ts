/**
 * 基于FFT的海洋波浪模拟
 * 使用快速傅里叶变换算法模拟真实的海洋波浪
 */
import * as THREE from 'three';
import { System } from '../../core/System';
import type { World } from '../../core/World';
import { Debug } from '../../utils/Debug';
import { PerformanceMonitor } from '../../utils/PerformanceMonitor';
import { EventEmitter } from '../../utils/EventEmitter';

/**
 * 海洋波浪模拟配置
 */
export interface OceanFFTWaveSimulationConfig {
  /** 是否启用 */
  enabled?: boolean;
  /** 是否自动更新 */
  autoUpdate?: boolean;
  /** 更新频率 */
  updateFrequency?: number;
  /** 分辨率 */
  resolution?: number;
  /** 尺寸 */
  size?: number;
  /** 风速 */
  windSpeed?: number;
  /** 风向 */
  windDirection?: number;
  /** 波浪尺度 */
  waveScale?: number;
  /** 波浪高度 */
  waveHeight?: number;
  /** 波浪速度 */
  waveSpeed?: number;
  /** 是否使用GPU计算 */
  useGPUCompute?: boolean;
  /** 是否使用调试可视化 */
  useDebugVisualization?: boolean;
}

/**
 * 海洋波浪模拟事件类型
 */
export enum OceanFFTWaveSimulationEventType {
  /** 波浪更新 */
  WAVE_UPDATED = 'waveUpdated',
  /** 波浪参数变化 */
  WAVE_PARAMS_CHANGED = 'waveParamsChanged'
}

/**
 * 海洋波浪模拟系统
 */
export class OceanFFTWaveSimulation extends System {
  /** 系统类型 */
  public static readonly TYPE = 'OceanFFTWaveSimulation';

  /** 配置 */
  private config: Required<OceanFFTWaveSimulationConfig>;
  /** 帧计数器 */
  private frameCount: number = 0;
  /** 事件发射器 */
  private eventEmitter: EventEmitter = new EventEmitter();
  /** 性能监视器 */
  private performanceMonitor: PerformanceMonitor = PerformanceMonitor.getInstance();
  /** 调试对象 */
  private debugObjects: THREE.Object3D[] = [];
  /** 时间 */
  private time: number = 0;
  /** 波浪高度场 */
  private heightField: Float32Array;
  /** 波浪法线场 */
  private normalField: Float32Array;
  /** 波浪位移场 */
  private displacementField: Float32Array;
  /** 频谱 */
  private spectrum: Float32Array;
  /** 频谱共轭 */
  private spectrumConjugate: Float32Array;
  /** 波浪高度纹理 */
  private heightTexture: THREE.DataTexture;
  /** 波浪法线纹理 */
  private normalTexture: THREE.DataTexture;
  /** 波浪位移纹理 */
  private displacementTexture: THREE.DataTexture;
  /** GPU计算渲染器 */
  private gpuCompute: THREE.WebGLRenderer | null = null;
  /** 波浪高度计算着色器 */
  private heightComputeShader: THREE.ShaderMaterial | null = null;
  /** 波浪法线计算着色器 */
  private normalComputeShader: THREE.ShaderMaterial | null = null;
  /** 波浪位移计算着色器 */
  private displacementComputeShader: THREE.ShaderMaterial | null = null;
  /** 波浪高度计算目标 */
  private heightRenderTarget: THREE.WebGLRenderTarget | null = null;
  /** 波浪法线计算目标 */
  private normalRenderTarget: THREE.WebGLRenderTarget | null = null;
  /** 波浪位移计算目标 */
  private displacementRenderTarget: THREE.WebGLRenderTarget | null = null;

  /**
   * 构造函数
   * @param world 世界
   * @param config 配置
   */
  constructor(world: World, config: OceanFFTWaveSimulationConfig = {}) {
    super();
    this.world = world;

    // 设置默认配置
    this.config = {
      enabled: config.enabled !== undefined ? config.enabled : true,
      autoUpdate: config.autoUpdate !== undefined ? config.autoUpdate : true,
      updateFrequency: config.updateFrequency || 1,
      resolution: config.resolution || 256,
      size: config.size || 1000,
      windSpeed: config.windSpeed || 10,
      windDirection: config.windDirection || 0,
      waveScale: config.waveScale || 1,
      waveHeight: config.waveHeight || 1,
      waveSpeed: config.waveSpeed || 1,
      useGPUCompute: config.useGPUCompute !== undefined ? config.useGPUCompute : true,
      useDebugVisualization: config.useDebugVisualization !== undefined ? config.useDebugVisualization : false
    };

    // 初始化波浪场
    this.heightField = new Float32Array(this.config.resolution * this.config.resolution);
    this.normalField = new Float32Array(this.config.resolution * this.config.resolution * 3);
    this.displacementField = new Float32Array(this.config.resolution * this.config.resolution * 3);
    this.spectrum = new Float32Array(this.config.resolution * this.config.resolution * 2);
    this.spectrumConjugate = new Float32Array(this.config.resolution * this.config.resolution * 2);

    // 初始化纹理
    this.heightTexture = this.createDataTexture(this.heightField, 1);
    this.normalTexture = this.createDataTexture(this.normalField, 3);
    this.displacementTexture = this.createDataTexture(this.displacementField, 3);

    // 初始化GPU计算
    if (this.config.useGPUCompute) {
      this.initializeGPUCompute();
    }

    // 初始化频谱
    this.initializeSpectrum();

    // 初始化调试可视化
    if (this.config.useDebugVisualization) {
      this.initializeDebugVisualization();
    }
  }

  /**
   * 初始化系统
   */
  public initialize(): void {
    super.initialize();
    Debug.log('OceanFFTWaveSimulation', '海洋波浪模拟系统初始化');
  }

  /**
   * 创建数据纹理
   * @param data 数据
   * @param itemSize 每个元素的大小
   * @returns 数据纹理
   */
  private createDataTexture(data: Float32Array, itemSize: number): THREE.DataTexture {
    const texture = new THREE.DataTexture(
      data,
      this.config.resolution,
      this.config.resolution,
      itemSize === 1 ? THREE.RedFormat : THREE.RGBAFormat,
      THREE.FloatType
    );
    texture.needsUpdate = true;
    return texture;
  }

  /**
   * 初始化GPU计算
   */
  private initializeGPUCompute(): void {
    // 创建GPU计算渲染器
    this.gpuCompute = new THREE.WebGLRenderer();
    this.gpuCompute.setSize(this.config.resolution, this.config.resolution);

    // 创建计算目标
    this.heightRenderTarget = new THREE.WebGLRenderTarget(
      this.config.resolution,
      this.config.resolution,
      {
        format: THREE.RedFormat,
        type: THREE.FloatType
      }
    );

    this.normalRenderTarget = new THREE.WebGLRenderTarget(
      this.config.resolution,
      this.config.resolution,
      {
        format: THREE.RGBAFormat,
        type: THREE.FloatType
      }
    );

    this.displacementRenderTarget = new THREE.WebGLRenderTarget(
      this.config.resolution,
      this.config.resolution,
      {
        format: THREE.RGBAFormat,
        type: THREE.FloatType
      }
    );

    // 创建计算着色器
    this.heightComputeShader = new THREE.ShaderMaterial({
      uniforms: {
        time: { value: 0 },
        resolution: { value: this.config.resolution },
        size: { value: this.config.size },
        windSpeed: { value: this.config.windSpeed },
        windDirection: { value: this.config.windDirection },
        waveScale: { value: this.config.waveScale },
        waveHeight: { value: this.config.waveHeight },
        waveSpeed: { value: this.config.waveSpeed },
        spectrum: { value: this.createDataTexture(this.spectrum, 2) },
        spectrumConjugate: { value: this.createDataTexture(this.spectrumConjugate, 2) }
      },
      vertexShader: this.getHeightComputeVertexShader(),
      fragmentShader: this.getHeightComputeFragmentShader()
    });

    this.normalComputeShader = new THREE.ShaderMaterial({
      uniforms: {
        heightMap: { value: this.heightTexture },
        resolution: { value: this.config.resolution },
        size: { value: this.config.size }
      },
      vertexShader: this.getNormalComputeVertexShader(),
      fragmentShader: this.getNormalComputeFragmentShader()
    });

    this.displacementComputeShader = new THREE.ShaderMaterial({
      uniforms: {
        heightMap: { value: this.heightTexture },
        normalMap: { value: this.normalTexture },
        resolution: { value: this.config.resolution },
        size: { value: this.config.size },
        waveHeight: { value: this.config.waveHeight }
      },
      vertexShader: this.getDisplacementComputeVertexShader(),
      fragmentShader: this.getDisplacementComputeFragmentShader()
    });
  }

  /**
   * 初始化频谱
   */
  private initializeSpectrum(): void {
    // 计算频谱
    this.computePhillipsSpectrum();
  }

  /**
   * 计算Phillips频谱
   */
  private computePhillipsSpectrum(): void {
    const N = this.config.resolution;
    const L = this.config.size;
    const windSpeed = this.config.windSpeed;
    const windDirection = this.config.windDirection;
    const waveScale = this.config.waveScale;
    const g = 9.81; // 重力加速度

    // 风向向量
    const windX = Math.cos(windDirection);
    const windZ = Math.sin(windDirection);

    // 计算Phillips频谱
    for (let i = 0; i < N; i++) {
      for (let j = 0; j < N; j++) {
        // 计算波数向量
        const kx = (2 * Math.PI * (i - N / 2)) / L;
        const kz = (2 * Math.PI * (j - N / 2)) / L;
        const k = Math.sqrt(kx * kx + kz * kz);

        // 避免除以零
        if (k < 0.000001) {
          this.spectrum[(i * N + j) * 2] = 0;
          this.spectrum[(i * N + j) * 2 + 1] = 0;
          this.spectrumConjugate[(i * N + j) * 2] = 0;
          this.spectrumConjugate[(i * N + j) * 2 + 1] = 0;
          continue;
        }

        // 计算风向投影
        const kDotW = (kx * windX + kz * windZ) / k;
        const kDotW2 = kDotW * kDotW;

        // 计算Phillips频谱
        const L_ = windSpeed * windSpeed / g;
        const L2 = L_ * L_;
        const k2 = k * k;
        const k4 = k2 * k2;
        const phillips = waveScale * Math.exp(-1 / (k2 * L2)) / k4 * kDotW2;

        // 生成随机相位
        const phase = Math.random() * 2 * Math.PI;
        const cosPhase = Math.cos(phase);
        const sinPhase = Math.sin(phase);

        // 计算频谱
        this.spectrum[(i * N + j) * 2] = phillips * cosPhase;
        this.spectrum[(i * N + j) * 2 + 1] = phillips * sinPhase;

        // 计算共轭频谱
        this.spectrumConjugate[(i * N + j) * 2] = phillips * cosPhase;
        this.spectrumConjugate[(i * N + j) * 2 + 1] = -phillips * sinPhase;
      }
    }
  }

  /**
   * 初始化调试可视化
   */
  private initializeDebugVisualization(): void {
    // 创建调试容器
    const debugContainer = new THREE.Object3D();
    debugContainer.name = 'OceanFFTWaveSimulationDebug';
    this.world.getActiveScene()?.getThreeScene().add(debugContainer);

    // 创建调试平面
    const geometry = new THREE.PlaneGeometry(100, 100, 1, 1);
    const material = new THREE.MeshBasicMaterial({
      map: this.heightTexture,
      side: THREE.DoubleSide
    });
    const plane = new THREE.Mesh(geometry, material);
    plane.rotation.x = -Math.PI / 2;
    (plane as any).getPosition().y = 50;
    debugContainer.add(plane);
    this.debugObjects.push(plane);
  }

  /**
   * 更新系统
   * @param deltaTime 帧间隔时间（秒）
   */
  public update(deltaTime: number): void {
    if (!this.config.enabled || !this.config.autoUpdate) {
      return;
    }

    // 按照更新频率更新
    this.frameCount++;
    if (this.frameCount % this.config.updateFrequency !== 0) {
      return;
    }

    // 如果启用了性能监控，开始计时
    if (this.config.useDebugVisualization) {
      this.performanceMonitor.beginMeasure('oceanFFTWaveUpdate');
    }

    // 更新时间
    this.time += deltaTime * this.config.waveSpeed;

    // 更新波浪
    if (this.config.useGPUCompute) {
      this.updateWavesGPU();
    } else {
      this.updateWavesCPU();
    }

    // 更新调试可视化
    if (this.config.useDebugVisualization) {
      this.updateDebugVisualization();
      this.performanceMonitor.endMeasure('oceanFFTWaveUpdate');
    }
  }

  /**
   * 使用GPU更新波浪
   */
  private updateWavesGPU(): void {
    if (!this.gpuCompute || !this.heightComputeShader || !this.normalComputeShader || !this.displacementComputeShader) {
      return;
    }

    // 更新时间
    this.heightComputeShader.uniforms.time.value = this.time;

    // 渲染高度场
    this.gpuCompute.setRenderTarget(this.heightRenderTarget!);
    this.gpuCompute.render(new THREE.Scene().add(new THREE.Mesh(new THREE.PlaneGeometry(2, 2), this.heightComputeShader)), new THREE.OrthographicCamera(-1, 1, 1, -1, 0, 1));

    // 更新法线计算着色器的高度图
    this.normalComputeShader.uniforms.heightMap.value = this.heightRenderTarget!.texture;

    // 渲染法线场
    this.gpuCompute.setRenderTarget(this.normalRenderTarget!);
    this.gpuCompute.render(new THREE.Scene().add(new THREE.Mesh(new THREE.PlaneGeometry(2, 2), this.normalComputeShader)), new THREE.OrthographicCamera(-1, 1, 1, -1, 0, 1));

    // 更新位移计算着色器的高度图和法线图
    this.displacementComputeShader.uniforms.heightMap.value = this.heightRenderTarget!.texture;
    this.displacementComputeShader.uniforms.normalMap.value = this.normalRenderTarget!.texture;

    // 渲染位移场
    this.gpuCompute.setRenderTarget(this.displacementRenderTarget!);
    this.gpuCompute.render(new THREE.Scene().add(new THREE.Mesh(new THREE.PlaneGeometry(2, 2), this.displacementComputeShader)), new THREE.OrthographicCamera(-1, 1, 1, -1, 0, 1));

    // 更新纹理
    this.heightTexture = this.heightRenderTarget!.texture as THREE.DataTexture;
    this.normalTexture = this.normalRenderTarget!.texture as THREE.DataTexture;
    this.displacementTexture = this.displacementRenderTarget!.texture as THREE.DataTexture;

    // 发出事件
    this.eventEmitter.emit(OceanFFTWaveSimulationEventType.WAVE_UPDATED, {
      heightTexture: this.heightTexture,
      normalTexture: this.normalTexture,
      displacementTexture: this.displacementTexture
    });
  }

  /**
   * 使用CPU更新波浪
   */
  private updateWavesCPU(): void {
    // 计算波浪高度场
    this.computeWaveHeightField();

    // 计算波浪法线场
    this.computeWaveNormalField();

    // 计算波浪位移场
    this.computeWaveDisplacementField();

    // 更新纹理
    this.heightTexture.needsUpdate = true;
    this.normalTexture.needsUpdate = true;
    this.displacementTexture.needsUpdate = true;

    // 发出事件
    this.eventEmitter.emit(OceanFFTWaveSimulationEventType.WAVE_UPDATED, {
      heightTexture: this.heightTexture,
      normalTexture: this.normalTexture,
      displacementTexture: this.displacementTexture
    });
  }

  /**
   * 计算波浪高度场
   */
  private computeWaveHeightField(): void {
    const N = this.config.resolution;
    const L = this.config.size;
    const waveHeight = this.config.waveHeight;

    // 计算波浪高度场
    for (let i = 0; i < N; i++) {
      for (let j = 0; j < N; j++) {
        // 计算波数向量
        const kx = (2 * Math.PI * (i - N / 2)) / L;
        const kz = (2 * Math.PI * (j - N / 2)) / L;
        const k = Math.sqrt(kx * kx + kz * kz);

        // 计算频率
        const omega = Math.sqrt(9.81 * k);

        // 计算相位
        const phase = omega * this.time;

        // 获取频谱
        const re = this.spectrum[(i * N + j) * 2];
        const im = this.spectrum[(i * N + j) * 2 + 1];

        // 计算复指数
        const cosPhase = Math.cos(phase);
        const sinPhase = Math.sin(phase);

        // 计算时间演化的频谱
        const reT = re * cosPhase - im * sinPhase;
        // const imT = re * sinPhase + im * cosPhase; // 暂时不使用虚部

        // 获取共轭频谱
        const reC = this.spectrumConjugate[(i * N + j) * 2];
        const imC = this.spectrumConjugate[(i * N + j) * 2 + 1];

        // 计算时间演化的共轭频谱
        const reCT = reC * cosPhase - imC * sinPhase;
        // const imCT = reC * sinPhase + imC * cosPhase; // 暂时不使用虚部

        // 计算高度
        const height = (reT + reCT) * waveHeight;

        // 存储高度
        this.heightField[i * N + j] = height;
      }
    }
  }

  /**
   * 计算波浪法线场
   */
  private computeWaveNormalField(): void {
    const N = this.config.resolution;
    const L = this.config.size;
    const cellSize = L / N;

    // 计算波浪法线场
    for (let i = 0; i < N; i++) {
      for (let j = 0; j < N; j++) {
        // 获取相邻高度
        // const h = this.heightField[i * N + j]; // 当前高度暂时不使用
        const hL = this.heightField[((i - 1 + N) % N) * N + j];
        const hR = this.heightField[((i + 1) % N) * N + j];
        const hT = this.heightField[i * N + ((j - 1 + N) % N)];
        const hB = this.heightField[i * N + ((j + 1) % N)];

        // 计算梯度
        const gradX = (hR - hL) / (2 * cellSize);
        const gradZ = (hB - hT) / (2 * cellSize);

        // 计算法线
        const nx = -gradX;
        const ny = 1;
        const nz = -gradZ;
        const len = Math.sqrt(nx * nx + ny * ny + nz * nz);

        // 存储法线
        this.normalField[(i * N + j) * 3] = nx / len;
        this.normalField[(i * N + j) * 3 + 1] = ny / len;
        this.normalField[(i * N + j) * 3 + 2] = nz / len;
      }
    }
  }

  /**
   * 计算波浪位移场
   */
  private computeWaveDisplacementField(): void {
    const N = this.config.resolution;
    // const L = this.config.size; // 暂时不使用
    const waveHeight = this.config.waveHeight;

    // 计算波浪位移场
    for (let i = 0; i < N; i++) {
      for (let j = 0; j < N; j++) {
        // 获取高度和法线
        const h = this.heightField[i * N + j];
        const nx = this.normalField[(i * N + j) * 3];
        // const ny = this.normalField[(i * N + j) * 3 + 1]; // Y分量暂时不使用
        const nz = this.normalField[(i * N + j) * 3 + 2];

        // 计算位移
        const dx = nx * waveHeight * 0.5;
        const dy = h;
        const dz = nz * waveHeight * 0.5;

        // 存储位移
        this.displacementField[(i * N + j) * 3] = dx;
        this.displacementField[(i * N + j) * 3 + 1] = dy;
        this.displacementField[(i * N + j) * 3 + 2] = dz;
      }
    }
  }

  /**
   * 更新调试可视化
   */
  private updateDebugVisualization(): void {
    // 更新调试平面的纹理
    if (this.debugObjects.length > 0) {
      const plane = this.debugObjects[0] as THREE.Mesh;
      const material = plane.material as THREE.MeshBasicMaterial;
      material.map = this.heightTexture;
      material.needsUpdate = true;
    }
  }

  /**
   * 获取高度计算顶点着色器
   * @returns 顶点着色器代码
   */
  private getHeightComputeVertexShader(): string {
    return `
      varying vec2 vUv;

      void main() {
        vUv = uv;
        gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
      }
    `;
  }

  /**
   * 获取高度计算片段着色器
   * @returns 片段着色器代码
   */
  private getHeightComputeFragmentShader(): string {
    return `
      uniform float time;
      uniform float resolution;
      uniform float size;
      uniform float windSpeed;
      uniform float windDirection;
      uniform float waveScale;
      uniform float waveHeight;
      uniform float waveSpeed;
      uniform sampler2D spectrum;
      uniform sampler2D spectrumConjugate;

      varying vec2 vUv;

      const float PI = 3.14159265359;
      const float g = 9.81;

      void main() {
        // 计算波数向量
        vec2 k = 2.0 * PI * (vUv - 0.5) * resolution / size;
        float kLen = length(k);

        // 避免除以零
        if (kLen < 0.000001) {
          gl_FragColor = vec4(0.0, 0.0, 0.0, 1.0);
          return;
        }

        // 计算频率
        float omega = sqrt(g * kLen);

        // 计算相位
        float phase = omega * time * waveSpeed;

        // 获取频谱
        vec2 spectrumValue = texture2D(spectrum, vUv).rg;
        vec2 spectrumConjugateValue = texture2D(spectrumConjugate, vUv).rg;

        // 计算复指数
        float cosPhase = cos(phase);
        float sinPhase = sin(phase);

        // 计算时间演化的频谱
        vec2 spectrumT = vec2(
          spectrumValue.x * cosPhase - spectrumValue.y * sinPhase,
          spectrumValue.x * sinPhase + spectrumValue.y * cosPhase
        );

        // 计算时间演化的共轭频谱
        vec2 spectrumConjugateT = vec2(
          spectrumConjugateValue.x * cosPhase - spectrumConjugateValue.y * sinPhase,
          spectrumConjugateValue.x * sinPhase + spectrumConjugateValue.y * cosPhase
        );

        // 计算高度
        float height = (spectrumT.x + spectrumConjugateT.x) * waveHeight;

        gl_FragColor = vec4(height, 0.0, 0.0, 1.0);
      }
    `;
  }

  /**
   * 获取法线计算顶点着色器
   * @returns 顶点着色器代码
   */
  private getNormalComputeVertexShader(): string {
    return `
      varying vec2 vUv;

      void main() {
        vUv = uv;
        gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
      }
    `;
  }

  /**
   * 获取法线计算片段着色器
   * @returns 片段着色器代码
   */
  private getNormalComputeFragmentShader(): string {
    return `
      uniform sampler2D heightMap;
      uniform float resolution;
      uniform float size;

      varying vec2 vUv;

      void main() {
        // 计算像素大小
        float pixelSize = 1.0 / resolution;
        float cellSize = size / resolution;

        // 获取相邻高度
        float h = texture2D(heightMap, vUv).r;
        float hL = texture2D(heightMap, vUv + vec2(-pixelSize, 0.0)).r;
        float hR = texture2D(heightMap, vUv + vec2(pixelSize, 0.0)).r;
        float hT = texture2D(heightMap, vUv + vec2(0.0, -pixelSize)).r;
        float hB = texture2D(heightMap, vUv + vec2(0.0, pixelSize)).r;

        // 计算梯度
        float gradX = (hR - hL) / (2.0 * cellSize);
        float gradZ = (hB - hT) / (2.0 * cellSize);

        // 计算法线
        vec3 normal = normalize(vec3(-gradX, 1.0, -gradZ));

        // 输出法线
        gl_FragColor = vec4(normal, 1.0);
      }
    `;
  }

  /**
   * 获取位移计算顶点着色器
   * @returns 顶点着色器代码
   */
  private getDisplacementComputeVertexShader(): string {
    return `
      varying vec2 vUv;

      void main() {
        vUv = uv;
        gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
      }
    `;
  }

  /**
   * 获取位移计算片段着色器
   * @returns 片段着色器代码
   */
  private getDisplacementComputeFragmentShader(): string {
    return `
      uniform sampler2D heightMap;
      uniform sampler2D normalMap;
      uniform float resolution;
      uniform float size;
      uniform float waveHeight;

      varying vec2 vUv;

      void main() {
        // 获取高度和法线
        float h = texture2D(heightMap, vUv).r;
        vec3 normal = texture2D(normalMap, vUv).rgb;

        // 计算位移
        vec3 displacement = vec3(
          normal.x * waveHeight * 0.5,
          h,
          normal.z * waveHeight * 0.5
        );

        // 输出位移
        gl_FragColor = vec4(displacement, 1.0);
      }
    `;
  }

  /**
   * 设置风速
   * @param windSpeed 风速
   */
  public setWindSpeed(windSpeed: number): void {
    this.config.windSpeed = windSpeed;
    this.initializeSpectrum();
    this.eventEmitter.emit(OceanFFTWaveSimulationEventType.WAVE_PARAMS_CHANGED, this.config);
  }

  /**
   * 设置风向
   * @param windDirection 风向
   */
  public setWindDirection(windDirection: number): void {
    this.config.windDirection = windDirection;
    this.initializeSpectrum();
    this.eventEmitter.emit(OceanFFTWaveSimulationEventType.WAVE_PARAMS_CHANGED, this.config);
  }

  /**
   * 设置波浪尺度
   * @param waveScale 波浪尺度
   */
  public setWaveScale(waveScale: number): void {
    this.config.waveScale = waveScale;
    this.initializeSpectrum();
    this.eventEmitter.emit(OceanFFTWaveSimulationEventType.WAVE_PARAMS_CHANGED, this.config);
  }

  /**
   * 设置波浪高度
   * @param waveHeight 波浪高度
   */
  public setWaveHeight(waveHeight: number): void {
    this.config.waveHeight = waveHeight;
    if (this.heightComputeShader) {
      this.heightComputeShader.uniforms.waveHeight.value = waveHeight;
    }
    if (this.displacementComputeShader) {
      this.displacementComputeShader.uniforms.waveHeight.value = waveHeight;
    }
    this.eventEmitter.emit(OceanFFTWaveSimulationEventType.WAVE_PARAMS_CHANGED, this.config);
  }

  /**
   * 设置波浪速度
   * @param waveSpeed 波浪速度
   */
  public setWaveSpeed(waveSpeed: number): void {
    this.config.waveSpeed = waveSpeed;
    if (this.heightComputeShader) {
      this.heightComputeShader.uniforms.waveSpeed.value = waveSpeed;
    }
    this.eventEmitter.emit(OceanFFTWaveSimulationEventType.WAVE_PARAMS_CHANGED, this.config);
  }

  /**
   * 获取高度纹理
   * @returns 高度纹理
   */
  public getHeightTexture(): THREE.DataTexture {
    return this.heightTexture;
  }

  /**
   * 获取法线纹理
   * @returns 法线纹理
   */
  public getNormalTexture(): THREE.DataTexture {
    return this.normalTexture;
  }

  /**
   * 获取位移纹理
   * @returns 位移纹理
   */
  public getDisplacementTexture(): THREE.DataTexture {
    return this.displacementTexture;
  }
}
