/**
 * 动画重定向系统
 * 用于将一个骨骼结构的动画应用到另一个骨骼结构上
 */
import * as THREE from 'three';
import type { Entity } from '../core/Entity';
import type { AnimationClip } from './AnimationClip';
import { RigComponent } from './RigComponent';

/**
 * 骨骼映射接口
 */
export interface BoneMapping {
  /** 源骨骼名称 */
  source: string;
  /** 目标骨骼名称 */
  target: string;
}

/**
 * 骨骼重定向配置
 */
export interface RetargetingConfig {
  /** 骨骼映射 */
  boneMapping: BoneMapping[];
  /** 是否保留位置轨道 */
  preservePositionTracks?: boolean;
  /** 是否保留缩放轨道 */
  preserveScaleTracks?: boolean;
  /** 是否规范化旋转 */
  normalizeRotations?: boolean;
  /** 是否调整根骨骼高度 */
  adjustRootHeight?: boolean;
  /** 是否调整骨骼长度 */
  adjustBoneLength?: boolean;
}

/**
 * 骨骼信息
 */
interface BoneInfo {
  /** 骨骼对象 */
  bone: THREE.Bone;
  /** 初始本地旋转 */
  initialLocalRotation: THREE.Quaternion;
  /** 初始世界旋转 */
  initialWorldRotation: THREE.Quaternion;
  /** 初始世界旋转逆 */
  initialWorldRotationInverse: THREE.Quaternion;
  /** 父骨骼世界旋转 */
  parentWorldRotation: THREE.Quaternion;
  /** 父骨骼世界旋转逆 */
  parentWorldRotationInverse: THREE.Quaternion;
}

/**
 * 动画重定向系统
 */
export class AnimationRetargeting {
  /**
   * 重定向动画片段
   * @param sourceClip 源动画片段
   * @param sourceSkeleton 源骨骼
   * @param targetSkeleton 目标骨骼
   * @param config 重定向配置
   * @returns 重定向后的动画片段
   */
  public static retargetClip(
    sourceClip: THREE.AnimationClip,
    sourceSkeleton: THREE.SkinnedMesh | THREE.Bone[],
    targetSkeleton: THREE.SkinnedMesh | THREE.Bone[],
    config: RetargetingConfig
  ): THREE.AnimationClip {
    // 提取骨骼数组
    const sourceBones = Array.isArray(sourceSkeleton)
      ? sourceSkeleton
      : sourceSkeleton.skeleton.bones;

    const targetBones = Array.isArray(targetSkeleton)
      ? targetSkeleton
      : targetSkeleton.skeleton.bones;

    // 创建骨骼映射
    const boneMap = new Map<string, string>();
    for (const mapping of config.boneMapping) {
      boneMap.set(mapping.source, mapping.target);
    }

    // 创建新的轨道
    const newTracks: THREE.KeyframeTrack[] = [];

    // 处理每个轨道
    for (const track of sourceClip.tracks) {
      // 解析轨道名称，格式为 "boneName.property"
      const trackSplit = track.name.split('.');
      const boneName = trackSplit[0];
      const property = trackSplit[1];

      // 检查是否有映射
      const targetBoneName = boneMap.get(boneName);
      if (!targetBoneName) continue;

      // 查找目标骨骼
      const targetBone = targetBones.find(bone => bone.name === targetBoneName);
      if (!targetBone) continue;

      // 创建新轨道
      let newTrack: THREE.KeyframeTrack;

      if (property === 'quaternion') {
        // 处理旋转轨道
        newTrack = this.retargetRotationTrack(
          track as THREE.QuaternionKeyframeTrack,
          boneName,
          targetBoneName,
          sourceBones,
          targetBones,
          config
        );
      } else if (property === 'position' && config.preservePositionTracks) {
        // 处理位置轨道
        newTrack = this.retargetPositionTrack(
          track as THREE.VectorKeyframeTrack,
          boneName,
          targetBoneName,
          sourceBones,
          targetBones,
          config
        );
      } else if (property === 'scale' && config.preserveScaleTracks) {
        // 处理缩放轨道
        newTrack = this.retargetScaleTrack(
          track as THREE.VectorKeyframeTrack,
          boneName,
          targetBoneName
        );
      } else {
        continue;
      }

      if (newTrack) {
        newTracks.push(newTrack);
      }
    }

    // 创建新的动画片段
    return new THREE.AnimationClip(
      sourceClip.name,
      sourceClip.duration,
      newTracks,
      sourceClip.blendMode
    );
  }

  /**
   * 重定向旋转轨道
   * @param track 源轨道
   * @param sourceBoneName 源骨骼名称
   * @param targetBoneName 目标骨骼名称
   * @param sourceBones 源骨骼数组
   * @param targetBones 目标骨骼数组
   * @param config 重定向配置
   * @returns 重定向后的轨道
   */
  private static retargetRotationTrack(
    track: THREE.QuaternionKeyframeTrack,
    sourceBoneName: string,
    targetBoneName: string,
    sourceBones: THREE.Bone[],
    targetBones: THREE.Bone[],
    config: RetargetingConfig
  ): THREE.QuaternionKeyframeTrack {
    // 查找源骨骼和目标骨骼
    const sourceBone = sourceBones.find(bone => bone.name === sourceBoneName);
    const targetBone = targetBones.find(bone => bone.name === targetBoneName);

    if (!sourceBone || !targetBone) {
      return null;
    }

    // 获取骨骼信息
    const sourceBoneInfo = this.getBoneInfo(sourceBone);
    const targetBoneInfo = this.getBoneInfo(targetBone);

    // 创建新的值数组
    const times = track.times;
    const values = new Float32Array(track.values.length);

    // 处理每一帧
    for (let i = 0; i < track.times.length; i++) {
      const quaternion = new THREE.Quaternion();
      quaternion.fromArray(track.values, i * 4);

      // 转换旋转
      const retargetedQuaternion = this.retargetRotation(
        quaternion,
        sourceBoneInfo,
        targetBoneInfo,
        config.normalizeRotations
      );

      // 存储结果
      retargetedQuaternion.toArray(values, i * 4);
    }

    // 创建新轨道
    return new THREE.QuaternionKeyframeTrack(
      `${targetBoneName}.quaternion`,
      times,
      values
    );
  }

  /**
   * 重定向位置轨道
   * @param track 源轨道
   * @param sourceBoneName 源骨骼名称
   * @param targetBoneName 目标骨骼名称
   * @param sourceBones 源骨骼数组
   * @param targetBones 目标骨骼数组
   * @param config 重定向配置
   * @returns 重定向后的轨道
   */
  private static retargetPositionTrack(
    track: THREE.VectorKeyframeTrack,
    sourceBoneName: string,
    targetBoneName: string,
    sourceBones: THREE.Bone[],
    targetBones: THREE.Bone[],
    config: RetargetingConfig
  ): THREE.VectorKeyframeTrack {
    // 查找源骨骼和目标骨骼
    const sourceBone = sourceBones.find(bone => bone.name === sourceBoneName);
    const targetBone = targetBones.find(bone => bone.name === targetBoneName);

    if (!sourceBone || !targetBone) {
      return null;
    }

    // 创建新的值数组
    const times = track.times;
    const values = new Float32Array(track.values.length);

    // 计算骨骼长度比例
    let scale = 1.0;
    if (config.adjustBoneLength) {
      const sourceLength = this.getBoneLength(sourceBone);
      const targetLength = this.getBoneLength(targetBone);
      if (sourceLength > 0 && targetLength > 0) {
        scale = targetLength / sourceLength;
      }
    }

    // 处理每一帧
    for (let i = 0; i < track.times.length; i++) {
      const position = new THREE.Vector3();
      position.fromArray(track.values, i * 3);

      // 缩放位置
      position.multiplyScalar(scale);

      // 如果是根骨骼且需要调整高度
      if (sourceBoneName === 'Hips' && config.adjustRootHeight) {
        // 调整Y轴高度
        const sourceHeight = (sourceBone as any).getPosition().y;
        const targetHeight = (targetBone as any).getPosition().y;
        position.y = position.y - sourceHeight + targetHeight;
      }

      // 存储结果
      position.toArray(values, i * 3);
    }

    // 创建新轨道
    return new THREE.VectorKeyframeTrack(
      `${targetBoneName}.position`,
      times,
      values
    );
  }

  /**
   * 重定向缩放轨道
   * @param track 源轨道
   * @param sourceBoneName 源骨骼名称
   * @param targetBoneName 目标骨骼名称
   * @returns 重定向后的轨道
   */
  private static retargetScaleTrack(
    track: THREE.VectorKeyframeTrack,
    sourceBoneName: string,
    targetBoneName: string
  ): THREE.VectorKeyframeTrack {
    // 直接复制轨道，只修改名称
    return new THREE.VectorKeyframeTrack(
      `${targetBoneName}.scale`,
      track.times,
      track.values.slice()
    );
  }

  /**
   * 获取骨骼信息
   * @param bone 骨骼
   * @returns 骨骼信息
   */
  private static getBoneInfo(bone: THREE.Bone): BoneInfo {
    // 计算世界变换
    bone.updateWorldMatrix(true, false);

    // 获取初始本地旋转
    const initialLocalRotation = bone.quaternion.clone();

    // 获取初始世界旋转
    const initialWorldRotation = new THREE.Quaternion();
    bone.getWorldQuaternion(initialWorldRotation);

    // 计算初始世界旋转逆
    const initialWorldRotationInverse = initialWorldRotation.clone().invert();

    // 获取父骨骼世界旋转
    const parentWorldRotation = new THREE.Quaternion();
    if (bone.parent && bone.parent instanceof THREE.Bone) {
      bone.parent.getWorldQuaternion(parentWorldRotation);
    }

    // 计算父骨骼世界旋转逆
    const parentWorldRotationInverse = parentWorldRotation.clone().invert();

    return {
      bone,
      initialLocalRotation,
      initialWorldRotation,
      initialWorldRotationInverse,
      parentWorldRotation,
      parentWorldRotationInverse
    };
  }

  /**
   * 重定向旋转
   * @param rotation 源旋转
   * @param sourceBoneInfo 源骨骼信息
   * @param targetBoneInfo 目标骨骼信息
   * @param normalize 是否规范化
   * @returns 重定向后的旋转
   */
  private static retargetRotation(
    rotation: THREE.Quaternion,
    sourceBoneInfo: BoneInfo,
    targetBoneInfo: BoneInfo,
    normalize: boolean = true
  ): THREE.Quaternion {
    // 创建临时四元数
    const q1 = new THREE.Quaternion();
    const q2 = new THREE.Quaternion();
    const q3 = new THREE.Quaternion();

    // 如果需要规范化
    if (normalize) {
      // 将本地旋转转换为世界旋转
      q1.copy(sourceBoneInfo.parentWorldRotation);
      q1.multiply(rotation);

      // 应用源骨骼的初始世界旋转逆
      q2.copy(sourceBoneInfo.initialWorldRotationInverse);
      q1.multiply(q2);

      // 应用目标骨骼的初始世界旋转
      q1.multiply(targetBoneInfo.initialWorldRotation);

      // 将世界旋转转换回本地旋转
      q3.copy(targetBoneInfo.parentWorldRotationInverse);
      q3.multiply(q1);

      return q3;
    } else {
      // 直接返回原始旋转
      return rotation.clone();
    }
  }

  /**
   * 获取骨骼长度
   * @param bone 骨骼
   * @returns 骨骼长度
   */
  private static getBoneLength(bone: THREE.Bone): number {
    // 如果没有子骨骼，返回0
    if (!bone.children.length) return 0;

    // 找到第一个子骨骼
    const childBone = bone.children.find(child => child instanceof THREE.Bone) as THREE.Bone;
    if (!childBone) return 0;

    // 返回子骨骼位置的长度
    return childBone.position.length();
  }

  /**
   * 从实体中获取骨骼映射
   * @param entity 实体
   * @returns 骨骼映射
   */
  public static getBoneMappingFromEntity(entity: Entity): Map<string, string> {
    // 这里需要根据实际的实体结构来实现
    // 例如，可以从实体的组件中获取骨骼映射信息
    const boneMapping = new Map<string, string>();

    // 示例：从RigComponent中获取骨骼映射
    const rigComponent = entity.getComponent('Rig') as any as any as RigComponent;
    if (rigComponent) {
      // 从RigComponent中获取entitiesToBones映射
      for (const [entityId, boneName] of Object.entries(rigComponent.entitiesToBones)) {
        // 这里需要根据实际情况进行映射
        // 例如，可以将Mixamo骨骼名称映射到VRM骨骼名称
        boneMapping.set(entityId, boneName as string);
      }
    }

    return boneMapping;
  }
}
