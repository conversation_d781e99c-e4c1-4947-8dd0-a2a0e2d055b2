/**
 * 实体同步管理器
 * 负责管理网络实体的同步
 */
import { EventEmitter } from '../utils/EventEmitter';
import { Debug } from '../utils/Debug';
import type { Entity } from '../core/Entity';
import { NetworkEntityComponent   } from './components/NetworkEntityComponent';
import { BandwidthController } from './BandwidthController';
import { DataCompressor, CompressionAlgorithm, CompressionLevel } from './DataCompressor';
import { SyncPriorityManager } from './SyncPriorityManager';

/**
 * 同步区域类型
 */
export enum SyncAreaType {
  /** 全局 */
  GLOBAL = 'global',
  /** 区域 */
  AREA = 'area',
  /** 距离 */
  DISTANCE = 'distance',
  /** 兴趣点 */
  INTEREST = 'interest',
}

/**
 * 同步区域配置
 */
export interface SyncAreaConfig {
  /** 区域类型 */
  type: SyncAreaType;
  /** 区域ID */
  id: string;
  /** 区域中心位置 */
  position?: { x: number; y: number; z: number };
  /** 区域半径 */
  radius?: number;
  /** 区域边界 */
  bounds?: {
    min: { x: number; y: number; z: number };
    max: { x: number; y: number; z: number };
  };
  /** 同步优先级 */
  priority?: number;
  /** 同步间隔（毫秒） */
  syncInterval?: number;
  /** 是否启用 */
  enabled?: boolean;
}

/**
 * 实体同步配置
 */
export interface EntitySyncConfig {
  /** 默认同步间隔（毫秒） */
  defaultSyncInterval?: number;
  /** 最小同步间隔（毫秒） */
  minSyncInterval?: number;
  /** 最大同步间隔（毫秒） */
  maxSyncInterval?: number;
  /** 同步距离 */
  syncDistance?: number;
  /** 是否使用空间分区 */
  useSpatialPartitioning?: boolean;
  /** 空间分区单元格大小 */
  spatialCellSize?: number;
  /** 是否使用插值 */
  useInterpolation?: boolean;
  /** 是否使用外推 */
  useExtrapolation?: boolean;
  /** 外推时间（毫秒） */
  extrapolationTime?: number;
  /** 是否使用压缩 */
  useCompression?: boolean;
  /** 是否使用增量同步 */
  useDeltaSync?: boolean;
  /** 是否使用优先级同步 */
  usePrioritySync?: boolean;
  /** 是否使用自适应同步 */
  useAdaptiveSync?: boolean;
}

/**
 * 实体同步状态
 */
export interface EntitySyncState {
  /** 实体ID */
  entityId: string;
  /** 实体所有者ID */
  ownerId: string;
  /** 上次同步时间 */
  lastSyncTime: number;
  /** 同步间隔（毫秒） */
  syncInterval: number;
  /** 同步优先级 */
  syncPriority: number;
  /** 同步区域ID */
  syncAreaId: string | null;
  /** 是否需要同步 */
  needsSync: boolean;
  /** 同步数据大小（字节） */
  syncDataSize: number;
  /** 同步次数 */
  syncCount: number;
  /** 同步失败次数 */
  syncFailCount: number;
}

/**
 * 实体同步管理器
 * 负责管理网络实体的同步
 */
export class EntitySyncManager extends EventEmitter {
  /** 配置 */
  private config: Required<EntitySyncConfig>;

  /** 实体映射表 */
  private entities: Map<string, Entity> = new Map();

  /** 实体同步状态映射表 */
  private entitySyncStates: Map<string, EntitySyncState> = new Map();

  /** 同步区域映射表 */
  private syncAreas: Map<string, SyncAreaConfig> = new Map();

  /** 空间分区网格 */
  private spatialGrid: Map<string, Set<string>> = new Map();

  /** 上次同步的实体数据缓存 */
  private lastSyncDataCache: Map<string, any> = new Map();

  /** 数据压缩器 */
  private dataCompressor: any = null;

  /** 同步优先级管理器 */
  private priorityManager: SyncPriorityManager = null;

  /** 本地用户ID */
  private localUserId: string | null = null;

  /** 带宽控制器 */
  private bandwidthController: BandwidthController | null = null;

  /** 同步定时器ID */
  private syncTimerId: number | null = null;

  /** 是否正在同步 */
  private isSyncing: boolean = false;

  /** 待同步实体队列 */
  private syncQueue: string[] = [];

  /**
   * 创建实体同步管理器
   * @param config 配置
   */
  constructor(config: EntitySyncConfig = {}) {
    super();

    // 默认配置
    this.config = {
      defaultSyncInterval: 100,
      minSyncInterval: 50,
      maxSyncInterval: 1000,
      syncDistance: 100,
      useSpatialPartitioning: true,
      spatialCellSize: 10,
      useInterpolation: true,
      useExtrapolation: true,
      extrapolationTime: 100,
      useCompression: true,
      useDeltaSync: true,
      usePrioritySync: true,
      useAdaptiveSync: true,
      ...config,
    };

    // 创建数据压缩器
    if (this.config.useCompression) {
      this.dataCompressor = new DataCompressor({
        algorithm: CompressionAlgorithm.LZ_STRING,
        level: CompressionLevel.MEDIUM,
        adaptive: true,
        incremental: {
          enabled: this.config.useDeltaSync,
          maxDepth: 10,
          includePathInfo: true,
          compressIncrementalData: true
        }
      });
    }

    // 创建同步优先级管理器
    if (this.config.usePrioritySync) {
      this.priorityManager = new SyncPriorityManager({
        useDistancePriority: true,
        distancePriorityWeight: 0.5,
        maxDistance: this.config.syncDistance,

        useVisibilityPriority: true,
        visibilityPriorityWeight: 0.3,

        useActivityPriority: true,
        activityPriorityWeight: 0.4,

        useImportancePriority: true,
        importancePriorityWeight: 0.2,

        priorityUpdateInterval: this.config.defaultSyncInterval
      });
    }
  }

  /**
   * 初始化管理器
   * @param localUserId 本地用户ID
   * @param bandwidthController 带宽控制器
   */
  public initialize(localUserId: string, bandwidthController?: BandwidthController): void {
    this.localUserId = localUserId;
    this.bandwidthController = bandwidthController || null;

    // 创建全局同步区域
    this.addSyncArea({
      type: SyncAreaType.GLOBAL,
      id: 'global',
      priority: 0,
      syncInterval: this.config.defaultSyncInterval,
      enabled: true,
    });

    // 如果启用了增量同步，但没有数据压缩器，则创建一个
    if (this.config.useDeltaSync && !this.dataCompressor) {
      this.dataCompressor = new DataCompressor({
        algorithm: CompressionAlgorithm.INCREMENTAL,
        incremental: {
          enabled: true,
          maxDepth: 10,
          includePathInfo: true,
          compressIncrementalData: false
        }
      });
    }

    // 初始化同步优先级管理器
    if (this.priorityManager) {
      (this.priorityManager as any).initialize?.(localUserId);
    }
  }

  /**
   * 启动同步
   * @param interval 同步间隔（毫秒）
   */
  public startSync(interval: number = 100): void {
    if (this.syncTimerId !== null) {
      return;
    }

    this.syncTimerId = window.setInterval(() => {
      this.syncEntities();
    }, interval);
  }

  /**
   * 停止同步
   */
  public stopSync(): void {
    if (this.syncTimerId !== null) {
      clearInterval(this.syncTimerId);
      this.syncTimerId = null;
    }
  }

  /**
   * 添加实体
   * @param entityId 实体ID
   * @param entity 实体
   */
  public addEntity(entityId: string, entity: Entity): void {
    // 检查实体是否已存在
    if (this.entities.has(entityId)) {
      Debug.warn('EntitySyncManager', `Entity with ID ${entityId} already exists`);
      return;
    }

    // 检查实体是否有网络实体组件
    const networkEntity = entity.getComponent('NetworkEntity') as any as NetworkEntityComponent;
    if (!networkEntity) {
      Debug.warn('EntitySyncManager', `Entity with ID ${entityId} does not have a NetworkEntityComponent`);
      return;
    }

    // 添加到实体映射表
    this.entities.set(entityId, entity);

    // 创建同步状态
    const syncState: EntitySyncState = {
      entityId,
      ownerId: (networkEntity as any).ownerId || 'unknown',
      lastSyncTime: Date.now(),
      syncInterval: (networkEntity as any).syncInterval || this.config.defaultSyncInterval,
      syncPriority: (networkEntity as any).syncPriority || 0,
      syncAreaId: 'global',
      needsSync: false,
      syncDataSize: 0,
      syncCount: 0,
      syncFailCount: 0,
    };

    // 添加到同步状态映射表
    this.entitySyncStates.set(entityId, syncState);

    // 如果使用空间分区，则添加到空间网格
    if (this.config.useSpatialPartitioning) {
      this.updateEntitySpatialCell(entityId, entity);
    }

    // 如果使用优先级同步，则注册到优先级管理器
    if (this.config.usePrioritySync && this.priorityManager) {
      (this.priorityManager as any).registerEntity?.(entityId, entity);
    }

    // 触发实体添加事件
    this.emit('entityAdded', entityId, entity);
  }

  /**
   * 移除实体
   * @param entityId 实体ID
   */
  public removeEntity(entityId: string): void {
    // 检查实体是否存在
    if (!this.entities.has(entityId)) {
      return;
    }

    const entity = this.entities.get(entityId)!;

    // 从实体映射表中移除
    this.entities.delete(entityId);

    // 从同步状态映射表中移除
    this.entitySyncStates.delete(entityId);

    // 从增量同步缓存中移除
    this.lastSyncDataCache.delete(entityId);

    // 如果使用空间分区，则从空间网格中移除
    if (this.config.useSpatialPartitioning) {
      this.removeEntityFromSpatialGrid(entityId);
    }

    // 如果使用优先级同步，则从优先级管理器中注销
    if (this.config.usePrioritySync && this.priorityManager) {
      (this.priorityManager as any).unregisterEntity?.(entityId);
    }

    // 触发实体移除事件
    this.emit('entityRemoved', entityId, entity);
  }

  /**
   * 更新实体
   * @param entityId 实体ID
   * @param entity 实体
   */
  public updateEntity(entityId: string, entity: Entity): void {
    // 检查实体是否存在
    if (!this.entities.has(entityId)) {
      this.addEntity(entityId, entity);
      return;
    }

    // 更新实体
    this.entities.set(entityId, entity);

    // 如果使用空间分区，则更新空间网格
    if (this.config.useSpatialPartitioning) {
      this.updateEntitySpatialCell(entityId, entity);
    }

    // 如果使用优先级同步，则更新优先级
    if (this.config.usePrioritySync && this.priorityManager) {
      (this.priorityManager as any).updateEntityPriority?.(entityId, entity);
    }

    // 标记实体需要同步
    const syncState = this.entitySyncStates.get(entityId);
    if (syncState) {
      syncState.needsSync = true;
    }
  }

  /**
   * 添加同步区域
   * @param area 同步区域配置
   */
  public addSyncArea(area: SyncAreaConfig): void {
    // 检查区域是否已存在
    if (this.syncAreas.has(area.id)) {
      Debug.warn('EntitySyncManager', `Sync area with ID ${area.id} already exists`);
      return;
    }

    // 添加到同步区域映射表
    this.syncAreas.set(area.id, {
      ...area,
      enabled: area.enabled !== undefined ? area.enabled : true,
    });

    // 触发同步区域添加事件
    this.emit('syncAreaAdded', area.id, area);
  }

  /**
   * 移除同步区域
   * @param areaId 区域ID
   */
  public removeSyncArea(areaId: string): void {
    // 检查区域是否存在
    if (!this.syncAreas.has(areaId) || areaId === 'global') {
      return;
    }

    const area = this.syncAreas.get(areaId)!;

    // 从同步区域映射表中移除
    this.syncAreas.delete(areaId);

    // 将该区域中的实体移动到全局区域
    for (const [, syncState] of this.entitySyncStates.entries()) {
      if (syncState.syncAreaId === areaId) {
        syncState.syncAreaId = 'global';
      }
    }

    // 触发同步区域移除事件
    this.emit('syncAreaRemoved', areaId, area);
  }

  /**
   * 更新
   * @param deltaTime 时间增量（毫秒）
   */
  public update(deltaTime: number): void {
    // 如果使用优先级同步，则更新优先级管理器
    if (this.config.usePrioritySync && this.priorityManager) {
      (this.priorityManager as any).update?.(deltaTime, this.entities);
    }
  }

  /**
   * 同步实体
   */
  private syncEntities(): void {
    if (this.isSyncing || !this.localUserId) {
      return;
    }

    this.isSyncing = true;

    try {
      const now = Date.now();

      // 更新同步队列
      this.updateSyncQueue(now);

      // 处理同步队列
      this.processSyncQueue(now);
    } finally {
      this.isSyncing = false;
    }
  }

  /**
   * 更新同步队列
   * @param now 当前时间
   */
  private updateSyncQueue(now: number): void {
    this.syncQueue = [];

    // 遍历所有实体
    for (const [entityId, syncState] of this.entitySyncStates.entries()) {
      const entity = this.entities.get(entityId);
      if (!entity) continue;

      const networkEntity = entity.getComponent('NetworkEntity') as any as NetworkEntityComponent;
      if (!networkEntity) continue;

      // 只同步本地拥有的实体
      if ((networkEntity as any).ownerId !== this.localUserId) continue;

      // 检查是否需要同步
      const timeSinceLastSync = now - syncState.lastSyncTime;
      const shouldSync = syncState.needsSync && timeSinceLastSync >= syncState.syncInterval;

      if (shouldSync) {
        this.syncQueue.push(entityId);
      }
    }

    // 如果使用优先级同步，则按优先级排序
    if (this.config.usePrioritySync) {
      if (this.priorityManager) {
        // 使用优先级管理器获取优先级
        this.syncQueue.sort((a, b) => {
          const priorityA = (this.priorityManager as any).getEntityPriority?.(a) || 0;
          const priorityB = (this.priorityManager as any).getEntityPriority?.(b) || 0;

          // 优先级越高越优先（1最高，0最低）
          return priorityB - priorityA;
        });
      } else {
        // 使用实体自身的优先级
        this.syncQueue.sort((a, b) => {
          const stateA = this.entitySyncStates.get(a);
          const stateB = this.entitySyncStates.get(b);

          if (!stateA || !stateB) return 0;

          // 优先级越小越优先
          return stateA.syncPriority - stateB.syncPriority;
        });
      }
    }
  }

  /**
   * 处理同步队列
   * @param now 当前时间
   */
  private processSyncQueue(now: number): void {
    // 获取可用带宽
    let availableBandwidth = Number.MAX_SAFE_INTEGER;
    if (this.bandwidthController) {
      const usage = this.bandwidthController.getBandwidthUsage();
      availableBandwidth = usage.uploadLimit - usage.upload;
    }

    // 处理队列中的实体
    let usedBandwidth = 0;

    for (const entityId of this.syncQueue) {
      const entity = this.entities.get(entityId);
      const syncState = this.entitySyncStates.get(entityId);

      if (!entity || !syncState) continue;

      // 检查是否超出带宽限制
      if (usedBandwidth >= availableBandwidth) {
        break;
      }

      // 同步实体
      const syncResult = this.syncEntity(entityId, entity, syncState);

      if (syncResult.success) {
        // 更新同步状态
        syncState.lastSyncTime = now;
        syncState.needsSync = false;
        syncState.syncCount++;
        syncState.syncDataSize = syncResult.dataSize;

        // 记录带宽使用
        usedBandwidth += syncResult.dataSize;
        if (this.bandwidthController) {
          this.bandwidthController.recordUpload(syncResult.dataSize);
        }
      } else {
        syncState.syncFailCount++;
      }
    }
  }

  /**
   * 同步单个实体
   * @param entityId 实体ID
   * @param entity 实体
   * @param syncState 同步状态
   * @returns 同步结果
   */
  private syncEntity(entityId: string, entity: Entity, syncState: EntitySyncState): { success: boolean; dataSize: number } {
    try {
      const networkEntity = entity.getComponent('NetworkEntity') as any as NetworkEntityComponent;
      if (!networkEntity) {
        return { success: false, dataSize: 0 };
      }

      // 获取同步数据
      const syncData = (networkEntity as any).getSyncData?.() || {};

      // 如果使用增量同步，则计算差异
      let finalData = syncData;
      let originalSize = 0;
      let compressedSize = 0;

      if (this.config.useDeltaSync && this.dataCompressor) {
        // 获取上次同步的数据
        const lastSyncData = this.lastSyncDataCache.get(entityId);

        // 创建增量数据
        if ((this.dataCompressor as any).algorithm === CompressionAlgorithm.INCREMENTAL) {
          // 直接使用增量压缩算法
          finalData = (this.dataCompressor as any).createIncrementalData?.(syncData, lastSyncData) || this.createIncrementalData(syncData, lastSyncData);
        } else {
          // 手动计算增量
          finalData = this.createIncrementalData(syncData, lastSyncData);
        }

        // 更新缓存
        this.lastSyncDataCache.set(entityId, syncData);

        // 估算原始大小和压缩后大小
        originalSize = JSON.stringify(syncData).length;
        compressedSize = JSON.stringify(finalData).length;
      } else if (this.config.useCompression && this.dataCompressor) {
        // 使用压缩算法压缩数据
        try {
          const compressionResult = (this.dataCompressor as any).compress?.(syncData);
          if (compressionResult && typeof compressionResult === 'object') {
            finalData = compressionResult.data || compressionResult;
            originalSize = compressionResult.originalSize || JSON.stringify(syncData).length;
            compressedSize = compressionResult.compressedSize || JSON.stringify(finalData).length;
          } else {
            finalData = compressionResult || syncData;
            originalSize = JSON.stringify(syncData).length;
            compressedSize = JSON.stringify(finalData).length;
          }
        } catch (error) {
          Debug.warn('EntitySyncManager', `压缩失败，使用原始数据: ${error}`);
          finalData = syncData;
          originalSize = JSON.stringify(syncData).length;
          compressedSize = originalSize;
        }

        // 更新缓存
        this.lastSyncDataCache.set(entityId, syncData);
      } else {
        // 不使用压缩或增量同步
        originalSize = JSON.stringify(syncData).length;
        compressedSize = originalSize;
      }

      // 使用压缩后的大小
      const dataSize = compressedSize;

      // 触发实体同步事件
      this.emit('entitySync', entityId, finalData, dataSize);

      // 如果启用了自适应同步，则根据数据大小调整同步间隔
      if (this.config.useAdaptiveSync) {
        this.adjustSyncInterval(entityId, dataSize);
      }

      // 记录同步状态信息用于调试
      Debug.log('EntitySyncManager', `同步实体 ${entityId}, 数据大小: ${dataSize}, 同步次数: ${syncState.syncCount + 1}`);

      return { success: true, dataSize };
    } catch (error) {
      Debug.error('EntitySyncManager', `Failed to sync entity ${entityId}:`, error);
      return { success: false, dataSize: 0 };
    }
  }

  /**
   * 手动创建增量数据
   * @param newData 新数据
   * @param oldData 旧数据
   * @returns 增量数据
   */
  private createIncrementalData(newData: any, oldData: any): any {
    // 如果没有旧数据，则返回完整数据
    if (!oldData) {
      return {
        __incremental: true,
        __complete: true,
        __data: newData
      };
    }

    // 创建增量对象
    const incremental: any = {
      __incremental: true,
      __version: 1,
      __timestamp: Date.now()
    };

    // 计算差异
    const changes = this.calculateDifferences(newData, oldData);

    // 如果没有变化，则返回空增量
    if (Object.keys(changes).length === 0) {
      incremental.__empty = true;
      return incremental;
    }

    // 合并差异到增量对象
    Object.assign(incremental, changes);

    // 计算增量大小和完整数据大小
    const incrementalSize = JSON.stringify(incremental).length;
    const fullSize = JSON.stringify(newData).length;

    // 如果增量数据比完整数据大，则使用完整数据
    if (incrementalSize >= fullSize) {
      return {
        __incremental: true,
        __complete: true,
        __data: newData
      };
    }

    return incremental;
  }

  /**
   * 计算两个对象之间的差异
   * @param newData 新数据
   * @param oldData 旧数据
   * @returns 差异对象
   */
  private calculateDifferences(newData: any, oldData: any): any {
    const differences: any = {};

    // 如果类型不同，则返回新数据
    if (typeof newData !== typeof oldData) {
      return newData;
    }

    // 如果不是对象，则直接比较
    if (typeof newData !== 'object' || newData === null) {
      return newData !== oldData ? newData : {};
    }

    // 如果是数组，则比较数组
    if (Array.isArray(newData)) {
      // 如果长度不同，则返回新数组
      if (!Array.isArray(oldData) || newData.length !== oldData.length) {
        return newData;
      }

      // 比较数组元素
      let hasChanges = false;
      const arrayChanges: any[] = [];

      for (let i = 0; i < newData.length; i++) {
        const elementChanges = this.calculateDifferences(newData[i], oldData[i]);

        if (Object.keys(elementChanges).length > 0) {
          // 如果有变化，则记录索引和变化
          arrayChanges.push({
            index: i,
            changes: elementChanges
          });
          hasChanges = true;
        }
      }

      // 如果有变化，则返回数组变化
      if (hasChanges) {
        return { __array: arrayChanges };
      }

      return {};
    }

    // 比较对象属性
    for (const key in newData) {
      // 如果属性不在旧数据中，则添加
      if (!(key in oldData)) {
        differences[key] = newData[key];
        continue;
      }

      // 递归比较属性值
      const propertyChanges = this.calculateDifferences(newData[key], oldData[key]);

      // 如果有变化，则添加
      if (Object.keys(propertyChanges).length > 0) {
        differences[key] = propertyChanges;
      }
    }

    // 检查删除的属性
    for (const key in oldData) {
      if (!(key in newData)) {
        differences[key] = { __deleted: true };
      }
    }

    return differences;
  }

  /**
   * 应用增量数据
   * @param incrementalData 增量数据
   * @param currentData 当前数据
   * @returns 更新后的数据
   */
  public applyIncrementalData(incrementalData: any, currentData: any): any {
    // 如果不是增量数据，则直接返回
    if (!incrementalData || !incrementalData.__incremental) {
      return incrementalData;
    }

    // 如果是完整数据，则直接使用
    if (incrementalData.__complete) {
      return incrementalData.__data || incrementalData;
    }

    // 如果是空增量，则保持当前状态
    if (incrementalData.__empty) {
      return currentData;
    }

    // 检查增量版本
    const version = incrementalData.__version || 1;

    // 根据版本应用增量
    if (version === 1) {
      // 版本1：简单属性覆盖
      const newData = { ...currentData };

      for (const key in incrementalData) {
        if (!key.startsWith('__')) {
          // 如果是删除标记，则删除属性
          if (incrementalData[key] && typeof incrementalData[key] === 'object' && incrementalData[key].__deleted) {
            delete newData[key];
          } else if (incrementalData[key] && typeof incrementalData[key] === 'object' && incrementalData[key].__array) {
            // 如果是数组变化，则应用数组变化
            if (!Array.isArray(newData[key])) {
              newData[key] = [];
            }

            // 应用数组变化
            for (const change of incrementalData[key].__array) {
              const { index, changes } = change;

              // 确保数组长度足够
              while (newData[key].length <= index) {
                newData[key].push(undefined);
              }

              // 应用变化
              if (typeof changes === 'object' && Object.keys(changes).length > 0) {
                newData[key][index] = this.applyIncrementalData(changes, newData[key][index]);
              } else {
                newData[key][index] = changes;
              }
            }
          } else if (incrementalData[key] && typeof incrementalData[key] === 'object' && Object.keys(incrementalData[key]).length > 0) {
            // 如果是对象变化，则递归应用
            newData[key] = this.applyIncrementalData(incrementalData[key], newData[key] || {});
          } else {
            // 否则直接替换
            newData[key] = incrementalData[key];
          }
        }
      }

      return newData;
    } else {
      // 如果是未知版本，则使用数据压缩器
      if (this.dataCompressor) {
        return (this.dataCompressor as any).applyIncrementalData?.(incrementalData, currentData) || currentData;
      }

      // 如果没有数据压缩器，则返回原始数据
      return currentData;
    }
  }

  /**
   * 调整实体同步间隔
   * @param entityId 实体ID
   * @param dataSize 数据大小
   */
  private adjustSyncInterval(entityId: string, dataSize: number): void {
    const syncState = this.entitySyncStates.get(entityId);
    if (!syncState) {
      return;
    }

    // 如果使用优先级同步，则使用优先级管理器的推荐同步间隔
    if (this.config.usePrioritySync && this.priorityManager) {
      const recommendedInterval = (this.priorityManager as any).getEntityRecommendedSyncInterval?.(entityId) || this.config.defaultSyncInterval;

      // 根据数据大小进行微调
      const sizeRatio = Math.min(1, dataSize / 10000); // 10KB作为基准
      const sizeAdjustment = Math.round(sizeRatio * 100); // 最多增加100ms

      // 计算最终间隔
      const finalInterval = recommendedInterval + sizeAdjustment;

      // 限制在配置范围内
      syncState.syncInterval = Math.max(this.config.minSyncInterval, Math.min(finalInterval, this.config.maxSyncInterval));
    } else {
      // 使用基于数据大小的简单自适应策略
      const sizeRatio = Math.min(1, dataSize / 10000); // 10KB作为基准
      const intervalRange = this.config.maxSyncInterval - this.config.minSyncInterval;
      const newInterval = Math.round(this.config.minSyncInterval + sizeRatio * intervalRange);

      // 限制在配置范围内
      syncState.syncInterval = Math.max(this.config.minSyncInterval, Math.min(newInterval, this.config.maxSyncInterval));
    }
  }

  /**
   * 更新实体空间单元格
   * @param entityId 实体ID
   * @param entity 实体
   */
  private updateEntitySpatialCell(entityId: string, entity: Entity): void {
    if (!this.config.useSpatialPartitioning) {
      return;
    }

    const transform = entity.getComponent('Transform') as any as any;
    if (!transform) {
      return;
    }

    // 移除实体从旧的单元格
    this.removeEntityFromSpatialGrid(entityId);

    // 计算新的单元格坐标
    const cellX = Math.floor((transform as any).getPosition().x / this.config.spatialCellSize);
    const cellY = Math.floor((transform as any).getPosition().y / this.config.spatialCellSize);
    const cellZ = Math.floor((transform as any).getPosition().z / this.config.spatialCellSize);

    // 生成单元格ID
    const cellId = `${cellX},${cellY},${cellZ}`;

    // 添加实体到新的单元格
    if (!this.spatialGrid.has(cellId)) {
      this.spatialGrid.set(cellId, new Set());
    }

    this.spatialGrid.get(cellId)!.add(entityId);
  }

  /**
   * 从空间网格中移除实体
   * @param entityId 实体ID
   */
  private removeEntityFromSpatialGrid(entityId: string): void {
    for (const [cellId, entities] of this.spatialGrid.entries()) {
      if (entities.has(entityId)) {
        entities.delete(entityId);

        // 如果单元格为空，则移除
        if (entities.size === 0) {
          this.spatialGrid.delete(cellId);
        }
      }
    }
  }

  /**
   * 获取实体
   * @param entityId 实体ID
   * @returns 实体
   */
  public getEntity(entityId: string): Entity | undefined {
    return this.entities.get(entityId);
  }

  /**
   * 获取实体同步状态
   * @param entityId 实体ID
   * @returns 同步状态
   */
  public getEntitySyncState(entityId: string): EntitySyncState | undefined {
    return this.entitySyncStates.get(entityId);
  }

  /**
   * 获取同步区域
   * @param areaId 区域ID
   * @returns 同步区域
   */
  public getSyncArea(areaId: string): SyncAreaConfig | undefined {
    return this.syncAreas.get(areaId);
  }

  /**
   * 设置实体同步优先级
   * @param entityId 实体ID
   * @param priority 优先级
   */
  public setEntitySyncPriority(entityId: string, priority: number): void {
    const syncState = this.entitySyncStates.get(entityId);
    if (syncState) {
      syncState.syncPriority = priority;
    }
  }

  /**
   * 设置实体同步间隔
   * @param entityId 实体ID
   * @param interval 同步间隔（毫秒）
   */
  public setEntitySyncInterval(entityId: string, interval: number): void {
    const syncState = this.entitySyncStates.get(entityId);
    if (syncState) {
      syncState.syncInterval = Math.max(this.config.minSyncInterval, Math.min(interval, this.config.maxSyncInterval));
    }
  }

  /**
   * 标记实体需要同步
   * @param entityId 实体ID
   */
  public markEntityForSync(entityId: string): void {
    const syncState = this.entitySyncStates.get(entityId);
    if (syncState) {
      syncState.needsSync = true;
    }
  }

  /**
   * 销毁管理器
   */
  public dispose(): void {
    this.stopSync();
    this.entities.clear();
    this.entitySyncStates.clear();
    this.syncAreas.clear();
    this.spatialGrid.clear();
    this.lastSyncDataCache.clear();
    this.syncQueue = [];
    this.removeAllListeners();

    // 清理数据压缩器
    if (this.dataCompressor) {
      this.dataCompressor = null;
    }

    // 清理优先级管理器
    if (this.priorityManager) {
      (this.priorityManager as any).dispose();
      this.priorityManager = null;
    }
  }
}
