/**
 * 实例化渲染系统
 * 用于高效渲染大量相同物体
 */
import * as THREE from 'three';
import { System } from '../../core/System';
import type { Entity } from '../../core/Entity';
import { type type Camera   } from '../Camera';
import { Scene } from '../../scene/Scene';
import type { Transform } from '../../scene/Transform';
import { EventEmitter } from '../../utils/EventEmitter';
import { InstancedComponent, InstanceData } from './InstancedComponent';

/**
 * 实例化渲染系统配置接口
 */
export interface InstancedRenderingSystemOptions {
  /** 是否启用 */
  enabled?: boolean;
  /** 是否自动更新 */
  autoUpdate?: boolean;
  /** 更新频率（帧） */
  updateFrequency?: number;
  /** 是否使用视锥体剔除 */
  useFrustumCulling?: boolean;
  /** 是否使用遮挡剔除 */
  useOcclusionCulling?: boolean;
  /** 是否使用距离剔除 */
  useDistanceCulling?: boolean;
  /** 最大剔除距离 */
  maxCullingDistance?: number;
  /** 是否使用动态批处理 */
  useDynamicBatching?: boolean;
  /** 是否使用GPU实例化 */
  useGPUInstancing?: boolean;
}

/**
 * 实例化渲染系统事件类型
 */
export enum InstancedRenderingSystemEventType {
  /** 组件添加 */
  COMPONENT_ADDED = 'component_added',
  /** 组件移除 */
  COMPONENT_REMOVED = 'component_removed',
  /** 实例添加 */
  INSTANCE_ADDED = 'instance_added',
  /** 实例移除 */
  INSTANCE_REMOVED = 'instance_removed',
  /** 实例更新 */
  INSTANCE_UPDATED = 'instance_updated'
}

/**
 * 实例化渲染系统类
 */
export class InstancedRenderingSystem extends System {
  /** 系统类型 */
  private static readonly TYPE: string = 'InstancedRenderingSystem';

  /** 是否自动更新 */
  private autoUpdate: boolean;

  /** 更新频率（帧） */
  private updateFrequency: number;

  /** 当前帧计数 */
  private frameCount: number = 0;

  /** 是否使用视锥体剔除 */
  private useFrustumCulling: boolean;

  /** 是否使用遮挡剔除 */
  private useOcclusionCulling: boolean;

  /** 是否使用距离剔除 */
  private useDistanceCulling: boolean;

  /** 最大剔除距离 */
  private maxCullingDistance: number;

  /** 是否使用动态批处理 */
  private useDynamicBatching: boolean;

  /** 是否使用GPU实例化 */
  private useGPUInstancing: boolean;

  /** 活跃相机 */
  private activeCamera: Camera | null = null;

  /** 活跃场景 */
  private activeScene: Scene | null = null;

  /** 实例化组件列表 */
  private instancedComponents: Map<Entity, InstancedComponent> = new Map();

  /** 视锥体 */
  private frustum: THREE.Frustum = new THREE.Frustum();

  /** 事件发射器 */
  private eventEmitter: EventEmitter = new EventEmitter();

  /** 临时向量 */
  private tempVector: THREE.Vector3 = new THREE.Vector3();

  /** 临时矩阵 */
  private tempMatrix: THREE.Matrix4 = new THREE.Matrix4();

  /** 临时四元数 */
  private tempQuaternion: THREE.Quaternion = new THREE.Quaternion();

  /** 临时欧拉角 */
  private tempEuler: THREE.Euler = new THREE.Euler();

  /** 临时颜色 */
  private tempColor: THREE.Color = new THREE.Color();

  /**
   * 创建实例化渲染系统
   * @param options 实例化渲染系统配置
   */
  constructor(options: InstancedRenderingSystemOptions = {}) {
    super();

    this.setEnabled(options.enabled !== undefined ? options.enabled : true);
    this.autoUpdate = options.autoUpdate !== undefined ? options.autoUpdate : true;
    this.updateFrequency = options.updateFrequency !== undefined ? options.updateFrequency : 1;
    this.useFrustumCulling = options.useFrustumCulling !== undefined ? options.useFrustumCulling : true;
    this.useOcclusionCulling = options.useOcclusionCulling !== undefined ? options.useOcclusionCulling : false;
    this.useDistanceCulling = options.useDistanceCulling !== undefined ? options.useDistanceCulling : true;
    this.maxCullingDistance = options.maxCullingDistance !== undefined ? options.maxCullingDistance : 1000;
    this.useDynamicBatching = options.useDynamicBatching !== undefined ? options.useDynamicBatching : true;
    this.useGPUInstancing = options.useGPUInstancing !== undefined ? options.useGPUInstancing : true;

    // 设置优先级
    this.setPriority(30); // 实例化渲染系统优先级较高，在视锥体剔除系统之后执行
  }

  /**
   * 获取系统类型
   * @returns 系统类型
   */
  public getType(): string {
    return InstancedRenderingSystem.TYPE;
  }

  /**
   * 设置活跃相机
   * @param camera 相机
   */
  public setActiveCamera(camera: Camera): void {
    this.activeCamera = camera;
  }

  /**
   * 获取活跃相机
   * @returns 活跃相机
   */
  public getActiveCamera(): Camera | null {
    return this.activeCamera;
  }

  /**
   * 设置活跃场景
   * @param scene 场景
   */
  public setActiveScene(scene: Scene): void {
    this.activeScene = scene;
  }

  /**
   * 获取活跃场景
   * @returns 活跃场景
   */
  public getActiveScene(): Scene | null {
    return this.activeScene;
  }

  /**
   * 注册实例化组件
   * @param entity 实体
   * @param component 实例化组件
   */
  public registerInstancedComponent(entity: Entity, component: InstancedComponent): void {
    this.instancedComponents.set(entity, component);
    this.eventEmitter.emit(InstancedRenderingSystemEventType.COMPONENT_ADDED, entity, component);

    // 创建实例化网格
    this.createInstancedMesh(entity, component);
  }

  /**
   * 注销实例化组件
   * @param entity 实体
   */
  public unregisterInstancedComponent(entity: Entity): void {
    const component = this.instancedComponents.get(entity);
    if (component) {
      this.instancedComponents.delete(entity);
      this.eventEmitter.emit(InstancedRenderingSystemEventType.COMPONENT_REMOVED, entity, component);

      // 移除实例化网格
      this.removeInstancedMesh(entity, component);
    }
  }

  /**
   * 创建实例化网格
   * @param entity 实体
   * @param component 实例化组件
   */
  private createInstancedMesh(entity: Entity, component: InstancedComponent): void {
    // 如果已经有实例化网格，则先移除
    if (component.getInstancedMesh()) {
      this.removeInstancedMesh(entity, component);
    }

    // 获取原始网格
    const originalMesh = component.getOriginalMesh();
    if (!originalMesh) {
      return;
    }

    // 获取实例数量
    const instanceCount = component.getMaxInstanceCount();
    if (instanceCount <= 0) {
      return;
    }

    // 创建实例化网格
    const instancedMesh = new THREE.InstancedMesh(
      originalMesh.geometry,
      originalMesh.material,
      instanceCount
    );
    instancedMesh.name = `${originalMesh.name || 'Mesh'}_Instanced`;
    instancedMesh.castShadow = originalMesh.castShadow;
    instancedMesh.receiveShadow = originalMesh.receiveShadow;
    instancedMesh.frustumCulled = this.useFrustumCulling;

    // 设置实例化网格
    component.setInstancedMesh(instancedMesh);

    // 更新实例
    this.updateInstances(component);

    // 将实例化网格添加到场景
    if (this.activeScene) {
      this.activeScene.getThreeScene().add(instancedMesh);
    }
  }

  /**
   * 移除实例化网格
   * @param entity 实体
   * @param component 实例化组件
   */
  private removeInstancedMesh(entity: Entity, component: InstancedComponent): void {
    // 获取实例化网格
    const instancedMesh = component.getInstancedMesh();
    if (!instancedMesh) {
      return;
    }

    // 从场景中移除实例化网格
    if (this.activeScene) {
      this.activeScene.getThreeScene().remove(instancedMesh);
    }

    // 清除实例化网格
    component.setInstancedMesh(null);
  }

  /**
   * 更新实例
   * @param component 实例化组件
   */
  private updateInstances(component: InstancedComponent): void {
    // 获取实例化网格
    const instancedMesh = component.getInstancedMesh();
    if (!instancedMesh) {
      return;
    }

    // 获取实例数据
    const instances = component.getInstances();
    const instanceCount = instances.length;

    // 更新实例化网格的实例数量
    instancedMesh.count = instanceCount;

    // 更新实例
    for (let i = 0; i < instanceCount; i++) {
      const instance = instances[i];

      // 更新矩阵
      this.tempMatrix.compose(
        instance.position,
        instance.quaternion,
        instance.scale
      );
      instancedMesh.setMatrixAt(i, this.tempMatrix);

      // 更新颜色
      if (instance.color) {
        instancedMesh.setColorAt(i, instance.color);
      }
    }

    // 更新实例化网格
    if (instancedMesh.instanceMatrix) {
      instancedMesh.instanceMatrix.needsUpdate = true;
    }

    if (instancedMesh.instanceColor) {
      instancedMesh.instanceColor.needsUpdate = true;
    }
  }

  /**
   * 更新系统
   * @param deltaTime 帧间隔时间（秒）
   */
  public update(deltaTime: number): void {
    if (!this.isEnabled() || !this.autoUpdate) {
      return;
    }

    // 如果没有活跃相机或场景，则不更新
    if (!this.activeCamera || !this.activeScene) {
      return;
    }

    // 按照更新频率更新
    this.frameCount++;
    if (this.frameCount % this.updateFrequency !== 0) {
      return;
    }

    // 更新视锥体
    if (this.useFrustumCulling) {
      this.updateFrustum();
    }

    // 更新所有实例化组件
    for (const [entity, component] of Array.from(this.instancedComponents.entries())) {
      // 如果组件已更新，则更新实例
      if (component.isUpdated()) {
        this.updateInstances(component);
        component.setUpdated(false);
      }
    }
  }

  /**
   * 更新视锥体
   */
  private updateFrustum(): void {
    if (!this.activeCamera) {
      return;
    }

    const camera = this.activeCamera.getThreeCamera();
    this.tempMatrix.multiplyMatrices(camera.projectionMatrix, camera.matrixWorldInverse);
    this.frustum.setFromProjectionMatrix(this.tempMatrix);
  }

  /**
   * 添加实例
   * @param entity 实体
   * @param instance 实例数据
   * @returns 实例ID
   */
  public addInstance(entity: Entity, instance: InstanceData): number {
    // 获取实例化组件
    const component = this.instancedComponents.get(entity);
    if (!component) {
      return -1;
    }

    // 添加实例
    const instanceId = component.addInstance(instance);

    // 发出实例添加事件
    this.eventEmitter.emit(InstancedRenderingSystemEventType.INSTANCE_ADDED, entity, component, instanceId, instance);

    return instanceId;
  }

  /**
   * 移除实例
   * @param entity 实体
   * @param instanceId 实例ID
   * @returns 是否成功
   */
  public removeInstance(entity: Entity, instanceId: number): boolean {
    // 获取实例化组件
    const component = this.instancedComponents.get(entity);
    if (!component) {
      return false;
    }

    // 移除实例
    const success = component.removeInstance(instanceId);

    // 发出实例移除事件
    if (success) {
      this.eventEmitter.emit(InstancedRenderingSystemEventType.INSTANCE_REMOVED, entity, component, instanceId);
    }

    return success;
  }

  /**
   * 更新实例
   * @param entity 实体
   * @param instanceId 实例ID
   * @param instance 实例数据
   * @returns 是否成功
   */
  public updateInstance(entity: Entity, instanceId: number, instance: Partial<InstanceData>): boolean {
    // 获取实例化组件
    const component = this.instancedComponents.get(entity);
    if (!component) {
      return false;
    }

    // 更新实例
    const success = component.updateInstance(instanceId, instance);

    // 发出实例更新事件
    if (success) {
      this.eventEmitter.emit(InstancedRenderingSystemEventType.INSTANCE_UPDATED, entity, component, instanceId, instance);
    }

    return success;
  }

  /**
   * 获取实例
   * @param entity 实体
   * @param instanceId 实例ID
   * @returns 实例数据
   */
  public getInstance(entity: Entity, instanceId: number): InstanceData | null {
    // 获取实例化组件
    const component = this.instancedComponents.get(entity);
    if (!component) {
      return null;
    }

    // 获取实例
    return component.getInstance(instanceId);
  }

  /**
   * 清空实例
   * @param entity 实体
   * @returns 是否成功
   */
  public clearInstances(entity: Entity): boolean {
    // 获取实例化组件
    const component = this.instancedComponents.get(entity);
    if (!component) {
      return false;
    }

    // 清空实例
    component.clearInstances();

    return true;
  }

  /**
   * 手动更新
   */
  public manualUpdate(): void {
    if (!this.enabled) {
      return;
    }

    // 更新所有实例化组件
    for (const [entity, component] of Array.from(this.instancedComponents.entries())) {
      this.updateInstances(component);
      component.setUpdated(false);
    }
  }

  /**
   * 设置是否启用
   * @param enabled 是否启用
   */
  public setEnabled(enabled: boolean): void {
    this.enabled = enabled;
  }

  /**
   * 获取是否启用
   * @returns 是否启用
   */
  public isEnabled(): boolean {
    return this.enabled;
  }

  /**
   * 设置是否自动更新
   * @param autoUpdate 是否自动更新
   */
  public setAutoUpdate(autoUpdate: boolean): void {
    this.autoUpdate = autoUpdate;
  }

  /**
   * 获取是否自动更新
   * @returns 是否自动更新
   */
  public isAutoUpdate(): boolean {
    return this.autoUpdate;
  }

  /**
   * 添加事件监听器
   * @param type 事件类型
   * @param listener 监听器
   */
  public addEventListener(type: InstancedRenderingSystemEventType, listener: (...args: any[]) => void): void {
    this.eventEmitter.on(type, listener);
  }

  /**
   * 移除事件监听器
   * @param type 事件类型
   * @param listener 监听器
   */
  public removeEventListener(type: InstancedRenderingSystemEventType, listener: (...args: any[]) => void): void {
    this.eventEmitter.off(type, listener);
  }

  /**
   * 销毁系统
   */
  public dispose(): void {
    // 移除所有实例化网格
    for (const [entity, component] of Array.from(this.instancedComponents.entries())) {
      this.removeInstancedMesh(entity, component);
    }

    this.instancedComponents.clear();
    this.eventEmitter.removeAllListeners();
  }
}
