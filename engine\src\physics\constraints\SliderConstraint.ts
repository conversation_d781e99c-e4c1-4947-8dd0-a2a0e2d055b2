/**
 * 滑动约束
 * 限制两个物体沿着一个轴线滑动
 */
import * as THREE from 'three';
import * as CANNON from 'cannon-es';
import type { Entity } from '../../core/Entity';
import type { PhysicsBody } from '../PhysicsBody';
import { PhysicsConstraint, ConstraintType } from './PhysicsConstraint';

/**
 * 滑动约束选项
 */
export interface SliderConstraintOptions {
  /** 轴向A */
  axisA?: THREE.Vector3;
  /** 轴向B */
  axisB?: THREE.Vector3;
  /** 最小位移 */
  lowerLimit?: number;
  /** 最大位移 */
  upperLimit?: number;
  /** 是否允许连接的物体之间碰撞 */
  collideConnected?: boolean;
  /** 最大力 */
  maxForce?: number;
}

/**
 * 滑动约束
 */
export class SliderConstraint extends PhysicsConstraint {
  /** 组件类型 */
  public static readonly type: string = 'SliderConstraint';

  /** 轴向A */
  private axisA: THREE.Vector3;

  /** 轴向B */
  private axisB: THREE.Vector3;

  /** 最小位移 */
  private lowerLimit: number;

  /** 最大位移 */
  private upperLimit: number;

  /** 最大力 */
  private maxForce: number;

  /**
   * 创建滑动约束
   * @param targetEntity 目标实体
   * @param options 约束选项
   */
  constructor(targetEntity: Entity | null = null, options: SliderConstraintOptions = {}) {
    super(ConstraintType.SLIDER, targetEntity, options);

    // 设置轴向
    this.axisA = options.axisA ? options.axisA.clone() : new THREE.Vector3(1, 0, 0);
    this.axisB = options.axisB ? options.axisB.clone() : new THREE.Vector3(1, 0, 0);

    // 设置限制
    this.lowerLimit = options.lowerLimit !== undefined ? options.lowerLimit : -1;
    this.upperLimit = options.upperLimit !== undefined ? options.upperLimit : 1;

    // 设置最大力
    this.maxForce = options.maxForce !== undefined ? options.maxForce : 1e6;
  }

  /**
   * 创建约束
   */
  protected createConstraint(): void {
    // 获取源物体和目标物体
    const bodyA = this.getSourceBody();
    const bodyB = this.getTargetBody();

    // 如果没有源物体或目标物体，则无法创建约束
    if (!bodyA || !bodyB) {
      console.warn('无法创建滑动约束：缺少源物体或目标物体');
      return;
    }

    // 创建滑动约束
    // 注意：CANNON.js没有内置的滑动约束，我们使用点对点约束和铰链约束组合实现
    // 这是一个简化的实现，实际应用中可能需要更复杂的算法

    // 创建点对点约束
    const pointConstraint = new CANNON.PointToPointConstraint(
      bodyA,
      new CANNON.Vec3(0, 0, 0),
      bodyB,
      new CANNON.Vec3(0, 0, 0),
      this.maxForce
    );

    // 创建铰链约束
    const hingeConstraint = new CANNON.HingeConstraint(
      bodyA,
      bodyB,
      {
        pivotA: new CANNON.Vec3(0, 0, 0),
        axisA: new CANNON.Vec3(this.axisA.x, this.axisA.y, this.axisA.z),
        pivotB: new CANNON.Vec3(0, 0, 0),
        axisB: new CANNON.Vec3(this.axisB.x, this.axisB.y, this.axisB.z)
      }
    );

    // 设置是否允许连接的物体之间碰撞
    pointConstraint.collideConnected = this.collideConnected;
    hingeConstraint.collideConnected = this.collideConnected;

    // 保存约束
    this.constraint = pointConstraint;

    // 保存辅助约束
    (this as any).auxiliaryConstraint = hingeConstraint;

    // 添加辅助约束到物理世界
    if (this.world) {
      this.world.addConstraint(hingeConstraint);
    }
  }

  /**
   * 设置轴向A
   * @param axis 轴向
   */
  public setAxisA(axis: THREE.Vector3): void {
    this.axisA.copy(axis);

    // 如果约束已创建，需要重新创建
    this.recreateConstraint();
  }

  /**
   * 获取轴向A
   * @returns 轴向
   */
  public getAxisA(): THREE.Vector3 {
    return this.axisA.clone();
  }

  /**
   * 设置轴向B
   * @param axis 轴向
   */
  public setAxisB(axis: THREE.Vector3): void {
    this.axisB.copy(axis);

    // 如果约束已创建，需要重新创建
    this.recreateConstraint();
  }

  /**
   * 获取轴向B
   * @returns 轴向
   */
  public getAxisB(): THREE.Vector3 {
    return this.axisB.clone();
  }

  /**
   * 设置最小位移
   * @param limit 最小位移
   */
  public setLowerLimit(limit: number): void {
    this.lowerLimit = limit;

    // 如果约束已创建，需要重新创建
    this.recreateConstraint();
  }

  /**
   * 获取最小位移
   * @returns 最小位移
   */
  public getLowerLimit(): number {
    return this.lowerLimit;
  }

  /**
   * 设置最大位移
   * @param limit 最大位移
   */
  public setUpperLimit(limit: number): void {
    this.upperLimit = limit;

    // 如果约束已创建，需要重新创建
    this.recreateConstraint();
  }

  /**
   * 获取最大位移
   * @returns 最大位移
   */
  public getUpperLimit(): number {
    return this.upperLimit;
  }

  /**
   * 设置最大力
   * @param maxForce 最大力
   */
  public setMaxForce(maxForce: number): void {
    this.maxForce = maxForce;

    // 如果约束已创建，需要重新创建
    this.recreateConstraint();
  }

  /**
   * 获取最大力
   * @returns 最大力
   */
  public getMaxForce(): number {
    return this.maxForce;
  }

  /**
   * 重新创建约束
   */
  private recreateConstraint(): void {
    if (this.initialized && this.constraint && this.world) {
      this.world.removeConstraint(this.constraint);
      this.constraint = null;
      this.initialized = false;
      this.initialize(this.world);
    }
  }

  /**
   * 销毁约束
   */
  public dispose(): void {
    // 移除辅助约束
    if ((this as any).auxiliaryConstraint && this.world) {
      this.world.removeConstraint((this as any).auxiliaryConstraint);
      (this as any).auxiliaryConstraint = null;
    }

    // 调用父类的销毁方法
    (super as any).dispose();
  }
}
