/**
 * InteractionSystem.ts
 *
 * 交互系统，用于处理3D场景中的对象交互
 */

import { System } from '../core/System';
import type { World } from '../core/World';
import type { Entity } from '../core/Entity';
import { Vector3, Raycaster, type type Camera, Object3D   } from 'three';
import { InputSystem } from '../input/InputSystem';
import { InteractableComponent } from './components/InteractableComponent';
import { InteractionEventComponent } from './components/InteractionEventComponent';
import { Debug } from '../utils/Debug';

/**
 * 交互系统配置
 */
export interface InteractionSystemConfig {
  /** 是否启用调试模式 */
  debug?: boolean;
  /** 最大交互距离 */
  maxInteractionDistance?: number;
  /** 是否启用视锥体检测 */
  enableFrustumCheck?: boolean;
  /** 是否启用高亮效果 */
  enableHighlight?: boolean;
  /** 是否启用交互提示 */
  enablePrompt?: boolean;
  /** 是否启用交互声音 */
  enableSound?: boolean;
}

/**
 * 交互系统
 * 用于处理3D场景中的对象交互
 */
export class InteractionSystem extends System {
  /** 系统名称 */
  public static readonly NAME: string = 'InteractionSystem';



  /** 输入系统引用 */
  private inputSystem?: InputSystem;

  /** 可交互组件列表 */
  private interactableComponents: Map<Entity, InteractableComponent> = new Map();

  /** 交互事件组件列表 */
  private interactionEventComponents: Map<Entity, InteractionEventComponent> = new Map();

  /** 当前可用的交互对象列表（按距离排序） */
  private availableInteractables: Entity[] = [];

  /** 当前最近的交互对象 */
  private closestInteractable?: Entity;

  /** 当前高亮的交互对象 */
  private highlightedInteractable?: Entity;

  /** 射线投射器 */
  private raycaster: Raycaster = new Raycaster();

  /** 配置 */
  private config: InteractionSystemConfig;

  /** 交互检测计时器 */
  private interactionCheckTimer: number = 0;

  /** 交互检测间隔（秒） */
  private readonly interactionCheckInterval: number = 0.1;

  /**
   * 构造函数
   * @param world 世界实例
   * @param config 交互系统配置
   */
  constructor(world: World, config: InteractionSystemConfig = {}) {
    // 调用基类构造函数，传入优先级
    super(0);

    // 设置世界引用
    this.setWorld(world);

    // 初始化配置
    this.config = {
      debug: config.debug || false,
      maxInteractionDistance: config.maxInteractionDistance || 5,
      enableFrustumCheck: config.enableFrustumCheck !== undefined ? config.enableFrustumCheck : true,
      enableHighlight: config.enableHighlight !== undefined ? config.enableHighlight : true,
      enablePrompt: config.enablePrompt !== undefined ? config.enablePrompt : true,
      enableSound: config.enableSound !== undefined ? config.enableSound : true
    };

    // 获取输入系统引用
    this.inputSystem = world.getSystem(InputSystem);
    if (!this.inputSystem) {
      Debug.warn('InteractionSystem', 'InputSystem not found, some features may not work properly');
    }
  }

  /**
   * 注册可交互组件
   * @param entity 实体
   * @param component 可交互组件
   */
  registerInteractableComponent(entity: Entity, component: InteractableComponent): void {
    this.interactableComponents.set(entity, component);

    if (this.config.debug) {
      Debug.log('InteractionSystem', `Registered interactable component for entity ${entity.id}`);
    }
  }

  /**
   * 注销可交互组件
   * @param entity 实体
   */
  unregisterInteractableComponent(entity: Entity): void {
    // 从高亮和最近的交互对象中移除
    if (this.highlightedInteractable === entity) {
      this.highlightedInteractable = undefined;
    }

    if (this.closestInteractable === entity) {
      this.closestInteractable = undefined;
    }

    // 从可用交互对象列表中移除
    const index = this.availableInteractables.indexOf(entity);
    if (index !== -1) {
      this.availableInteractables.splice(index, 1);
    }

    // 从组件列表中移除
    this.interactableComponents.delete(entity);

    if (this.config.debug) {
      Debug.log('InteractionSystem', `Unregistered interactable component for entity ${entity.id}`);
    }
  }

  /**
   * 注册交互事件组件
   * @param entity 实体
   * @param component 交互事件组件
   */
  registerInteractionEventComponent(entity: Entity, component: InteractionEventComponent): void {
    this.interactionEventComponents.set(entity, component);

    if (this.config.debug) {
      Debug.log('InteractionSystem', `Registered interaction event component for entity ${entity.id}`);
    }
  }

  /**
   * 注销交互事件组件
   * @param entity 实体
   */
  unregisterInteractionEventComponent(entity: Entity): void {
    this.interactionEventComponents.delete(entity);

    if (this.config.debug) {
      Debug.log('InteractionSystem', `Unregistered interaction event component for entity ${entity.id}`);
    }
  }

  /**
   * 更新系统
   * @param deltaTime 时间增量（秒）
   */
  update(deltaTime: number): void {
    // 更新交互检测计时器
    this.interactionCheckTimer += deltaTime;

    // 每隔一段时间检测一次可交互对象
    if (this.interactionCheckTimer >= this.interactionCheckInterval) {
      this.interactionCheckTimer = 0;
      this.gatherAvailableInteractables();
    }

    // 处理输入
    this.handleInput();

    // 更新高亮效果
    this.updateHighlight();
  }

  /**
   * 收集可用的交互对象
   */
  private gatherAvailableInteractables(): void {
    // 清空可用交互对象列表
    this.availableInteractables = [];

    // 获取主相机（从当前场景中查找）
    const scene = this.world.getActiveScene();
    if (!scene) return;

    const camera = this.findMainCamera(scene);
    if (!camera) return;

    // 获取相机位置
    const cameraPosition = camera.position.clone();

    // 遍历所有可交互组件
    for (const [entity, component] of this.interactableComponents) {
      // 检查是否可见和可交互
      if (!component.visible || !component.interactive) continue;

      // 获取对象位置
      const position = component.getWorldPosition();

      // 计算距离
      const distance = position.distanceTo(cameraPosition);

      // 检查是否在最大交互距离内
      if (distance > this.config.maxInteractionDistance!) continue;

      // 如果启用视锥体检测，检查是否在视锥体内
      if (this.config.enableFrustumCheck && !this.isInFrustum(entity, camera)) continue;

      // 添加到可用交互对象列表
      this.availableInteractables.push(entity);
    }

    // 按距离排序
    this.sortInteractablesByDistance(cameraPosition);

    // 更新最近的交互对象
    this.closestInteractable = this.availableInteractables.length > 0 ? this.availableInteractables[0] : undefined;
  }

  /**
   * 检查对象是否在视锥体内
   * @param entity 实体
   * @param camera 相机
   * @returns 是否在视锥体内
   */
  private isInFrustum(entity: Entity, camera: Camera): boolean {
    // 获取对象的3D表示
    const object = this.getObject3D(entity);
    if (!object) return false;

    // 更新相机的视锥体
    camera.updateMatrixWorld();

    // 检查对象是否在视锥体内
    // const frustum = camera.projectionMatrix.clone().multiply(camera.matrixWorldInverse);
    // 简化实现：直接返回对象的可见性
    return object.visible;
  }

  /**
   * 按距离排序交互对象
   * @param cameraPosition 相机位置
   */
  private sortInteractablesByDistance(cameraPosition: Vector3): void {
    this.availableInteractables.sort((a, b) => {
      const componentA = this.interactableComponents.get(a);
      const componentB = this.interactableComponents.get(b);

      if (!componentA || !componentB) return 0;

      const positionA = componentA.getWorldPosition();
      const positionB = componentB.getWorldPosition();

      const distanceA = positionA.distanceTo(cameraPosition);
      const distanceB = positionB.distanceTo(cameraPosition);

      return distanceA - distanceB;
    });
  }

  /**
   * 处理输入
   */
  private handleInput(): void {
    // 如果没有输入系统，则返回
    if (!this.inputSystem) return;

    // 检查交互键是否按下
    if (this.inputSystem.isKeyDown('e') || this.inputSystem.isKeyDown('E')) {
      this.interactWithClosestInteractable();
    }
  }

  /**
   * 与最近的交互对象交互
   */
  private interactWithClosestInteractable(): void {
    if (!this.closestInteractable) return;

    const component = this.interactableComponents.get(this.closestInteractable);
    if (!component) return;

    // 调用交互回调
    component.interact();

    // 如果启用声音，播放交互声音
    if (this.config.enableSound && component.interactionSound) {
      // 尝试获取音频系统并播放声音
      // 注意：这里需要实际的音频系统类，暂时注释掉
      // const audioSystem = this.world.getSystem(AudioSystem);
      // if (audioSystem && typeof audioSystem.playSound === 'function') {
      //   audioSystem.playSound(component.interactionSound);
      // } else if (this.config.debug) {
      //   Debug.warn('InteractionSystem', 'AudioSystem not found or playSound method not available');
      // }
      if (this.config.debug) {
        Debug.log('InteractionSystem', `Playing interaction sound: ${component.interactionSound}`);
      }
    }
  }

  /**
   * 更新高亮效果
   */
  private updateHighlight(): void {
    // 如果未启用高亮，则返回
    if (!this.config.enableHighlight) return;

    // 如果最近的交互对象改变，更新高亮
    if (this.closestInteractable !== this.highlightedInteractable) {
      // 移除旧的高亮
      if (this.highlightedInteractable) {
        const oldComponent = this.interactableComponents.get(this.highlightedInteractable);
        if (oldComponent) {
          oldComponent.setHighlighted(false);
        }
      }

      // 添加新的高亮
      if (this.closestInteractable) {
        const newComponent = this.interactableComponents.get(this.closestInteractable);
        if (newComponent) {
          newComponent.setHighlighted(true);
        }
      }

      // 更新高亮对象
      this.highlightedInteractable = this.closestInteractable;
    }
  }

  /**
   * 获取对象的3D表示
   * @param entity 实体
   * @returns 3D对象
   */
  private getObject3D(entity: Entity): Object3D | null {
    // 从实体中获取Transform组件
    const transform = entity.getComponent('Transform') as any as any as any;
    if (transform) {
      // 使用Transform组件的getObject3D方法获取3D对象
      return transform.getObject3D();
    }

    // 如果没有Transform组件，尝试从实体中获取mesh属性（用于示例代码兼容）
    if ((entity as any).mesh instanceof Object3D) {
      return (entity as any).mesh;
    }

    return null;
  }

  /**
   * 查找主相机
   * @param scene 场景
   * @returns 相机对象
   */
  private findMainCamera(scene: any): any | null {
    // 简化实现：返回场景中的第一个相机
    // 实际实现需要根据具体的场景结构来查找相机
    const entities = scene.getEntities ? scene.getEntities() : [];
    for (const entity of entities) {
      const camera = entity.getComponent('Camera') as any;
      if (camera) {
        return camera;
      }
    }
    return null;
  }

  /**
   * 销毁系统
   */
  dispose(): void {
    // 清空所有列表
    this.interactableComponents.clear();
    this.interactionEventComponents.clear();
    this.availableInteractables = [];
    this.closestInteractable = undefined;
    this.highlightedInteractable = undefined;

    if (this.config.debug) {
      Debug.log('InteractionSystem', 'Disposed');
    }
  }
}
