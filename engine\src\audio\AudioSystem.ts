/**
 * 音频系统
 * 用于处理3D音频效果和音频播放
 */
import * as THREE from 'three';
import { EventEmitter, EventCallback } from '../utils/EventEmitter';
import { System } from '../core/System';
import { AudioListener } from './AudioListener';
import { AudioSource } from './AudioSource';

/**
 * 音频类型
 */
export enum AudioType {
  /** 音效 */
  SOUND = 'sound',
  /** 音乐 */
  MUSIC = 'music',
  /** 语音 */
  VOICE = 'voice',
  /** 环境音 */
  AMBIENT = 'ambient',
  /** 界面音效 */
  UI = 'ui'
}

/**
 * 音频事件类型
 */
export enum AudioEventType {
  /** 加载完成 */
  LOAD = 'load',
  /** 加载错误 */
  ERROR = 'error',
  /** 播放 */
  PLAY = 'play',
  /** 暂停 */
  PAUSE = 'pause',
  /** 停止 */
  STOP = 'stop',
  /** 结束 */
  END = 'end',
  /** 循环 */
  LOOP = 'loop',
  /** 音量变化 */
  VOLUME_CHANGE = 'volumeChange',
  /** 静音变化 */
  MUTE_CHANGE = 'muteChange',
  /** 音频添加 */
  AUDIO_ADDED = 'audioAdded',
  /** 音频移除 */
  AUDIO_REMOVED = 'audioRemoved'
}

/**
 * 音频系统选项
 */
export interface AudioSystemOptions {
  /** 是否启用 */
  enabled?: boolean;
  /** 全局音量 */
  volume?: number;
  /** 是否静音 */
  muted?: boolean;
  /** 音频类型音量 */
  typeVolumes?: { [key in AudioType]?: number };
  /** 音频类型静音 */
  typeMuted?: { [key in AudioType]?: boolean };
  /** 是否启用空间音频 */
  spatialAudio?: boolean;
  /** 是否自动加载 */
  autoLoad?: boolean;
  /** 最大同时播放数量 */
  maxSimultaneous?: number;
  /** 音频上下文选项 */
  contextOptions?: AudioContextOptions;
}

/**
 * 音频系统
 */
export class AudioSystem extends System {
  /** 系统名称 */
  public static readonly NAME: string = 'AudioSystem';

  /** 是否启用音频系统 */
  private audioEnabled: boolean;

  /** 全局音量 */
  private volume: number;

  /** 是否静音 */
  private muted: boolean;

  /** 音频类型音量 */
  private typeVolumes: Map<AudioType, number> = new Map();

  /** 音频类型静音 */
  private typeMuted: Map<AudioType, boolean> = new Map();

  /** 是否启用空间音频 */
  private spatialAudio: boolean;

  /** 是否自动加载 */
  private autoLoad: boolean;

  /** 最大同时播放数量 */
  private maxSimultaneous: number;

  /** 音频上下文 */
  private context: AudioContext | null = null;

  /** 主音量节点 */
  private masterGain: GainNode | null = null;

  /** 类型音量节点 */
  private typeGains: Map<AudioType, GainNode> = new Map();

  /** 音频监听器 */
  private listener: AudioListener | null = null;

  /** 音频源映射 */
  private sources: Map<string, AudioSource> = new Map();

  /** 当前播放的音频源 */
  private playingSources: Set<AudioSource> = new Set();

  /** 音频缓存 */
  private bufferCache: Map<string, AudioBuffer> = new Map();

  /** 事件发射器 */
  private eventEmitter: EventEmitter = new EventEmitter();

  /** 是否已初始化 */
  private initialized: boolean = false;

  /** 是否已销毁 */
  private destroyed: boolean = false;

  /** 音频加载器 */
  private audioLoader: THREE.AudioLoader = new THREE.AudioLoader();

  /** 是否支持Web Audio API */
  private supported: boolean = typeof AudioContext !== 'undefined';

  /**
   * 创建音频系统
   * @param options 音频系统选项
   */
  constructor(options: AudioSystemOptions = {}) {
    // 将系统名称转换为数字优先级，这里使用0作为默认优先级
    super(0);

    this.audioEnabled = options.enabled !== undefined ? options.enabled : true;
    // 设置基类的启用状态
    this.setEnabled(this.audioEnabled);
    this.volume = options.volume !== undefined ? options.volume : 1.0;
    this.muted = options.muted !== undefined ? options.muted : false;
    this.spatialAudio = options.spatialAudio !== undefined ? options.spatialAudio : true;
    this.autoLoad = options.autoLoad !== undefined ? options.autoLoad : true;
    this.maxSimultaneous = options.maxSimultaneous !== undefined ? options.maxSimultaneous : 32;

    // 设置音频类型音量
    if (options.typeVolumes) {
      for (const [type, volume] of Object.entries(options.typeVolumes)) {
        this.typeVolumes.set(type as AudioType, volume);
      }
    }

    // 设置音频类型静音
    if (options.typeMuted) {
      for (const [type, muted] of Object.entries(options.typeMuted)) {
        this.typeMuted.set(type as AudioType, muted);
      }
    }

    // 设置默认音频类型音量
    for (const type of Object.values(AudioType)) {
      if (!this.typeVolumes.has(type)) {
        this.typeVolumes.set(type, 1.0);
      }
      if (!this.typeMuted.has(type)) {
        this.typeMuted.set(type, false);
      }
    }

    // 如果支持Web Audio API，则创建音频上下文
    if (this.supported) {
      try {
        this.context = new AudioContext(options.contextOptions);

        // 创建主音量节点
        this.masterGain = this.context.createGain();
        this.masterGain.gain.value = this.muted ? 0 : this.volume;
        this.masterGain.connect(this.context.destination);

        // 创建类型音量节点
        for (const type of Object.values(AudioType)) {
          const gain = this.context.createGain();
          const typeVolume = this.typeVolumes.get(type) || 1.0;
          const typeMuted = this.typeMuted.get(type) || false;
          gain.gain.value = typeMuted ? 0 : typeVolume;
          gain.connect(this.masterGain);
          this.typeGains.set(type, gain);
        }

        // 创建音频监听器
        this.listener = new AudioListener(this.context);
      } catch (error) {
        console.error('创建音频上下文失败:', error);
        this.supported = false;
      }
    }
  }

  /**
   * 初始化系统
   */
  public initialize(): void {
    if (this.initialized || !this.supported) return;

    // 恢复音频上下文
    this.resumeContext();

    this.initialized = true;
  }

  /**
   * 恢复音频上下文
   */
  private resumeContext(): void {
    if (!this.context) return;

    // 如果音频上下文被挂起，则恢复
    if (this.context.state === 'suspended') {
      this.context.resume().catch(error => {
        console.error('恢复音频上下文失败:', error);
      });
    }
  }

  /**
   * 更新系统
   * @param deltaTime 帧间隔时间（秒）
   */
  public update(deltaTime: number): void {
    if (!this.initialized || !this.isEnabled() || !this.supported) return;

    // 更新音频监听器
    if (this.listener) {
      this.listener.update();
    }

    // 更新所有音频源
    for (const source of Array.from(this.playingSources)) {
      source.update(deltaTime);
    }
  }

  /**
   * 创建音频源
   * @param id 音频源ID
   * @param type 音频类型
   * @returns 音频源
   */
  public createSource(id: string, type: AudioType = AudioType.SOUND): AudioSource | null {
    if (!this.initialized || !this.supported || !this.context || !this.listener) return null;

    // 如果已存在相同ID的音频源，则返回
    if (this.sources.has(id)) {
      return this.sources.get(id) || null;
    }

    // 获取类型音量节点
    const typeGain = this.typeGains.get(type);
    if (!typeGain) return null;

    // 创建音频源
    const source = new AudioSource({
      id,
      type,
      context: this.context,
      listener: this.listener,
      destination: typeGain
    });

    // 添加到音频源映射
    this.sources.set(id, source);

    // 触发音频添加事件
    this.eventEmitter.emit(AudioEventType.AUDIO_ADDED, { id, type });

    return source;
  }

  /**
   * 移除音频源
   * @param id 音频源ID
   * @returns 是否成功移除
   */
  public removeSource(id: string): boolean {
    const source = this.sources.get(id);
    if (!source) return false;

    // 停止音频源
    source.stop();

    // 从播放列表中移除
    this.playingSources.delete(source);

    // 销毁音频源
    (source as any).dispose();

    // 从音频源映射中移除
    this.sources.delete(id);

    // 触发音频移除事件
    this.eventEmitter.emit(AudioEventType.AUDIO_REMOVED, { id, type: source.getType() });

    return true;
  }

  /**
   * 获取音频源
   * @param id 音频源ID
   * @returns 音频源
   */
  public getSource(id: string): AudioSource | null {
    return this.sources.get(id) || null;
  }

  /**
   * 获取所有音频源
   * @returns 音频源数组
   */
  public getSources(): AudioSource[] {
    return Array.from(this.sources.values());
  }

  /**
   * 获取指定类型的音频源
   * @param type 音频类型
   * @returns 音频源数组
   */
  public getSourcesByType(type: AudioType): AudioSource[] {
    return Array.from(this.sources.values()).filter(source => source.getType() === type);
  }

  /**
   * 获取当前播放的音频源
   * @returns 音频源数组
   */
  public getPlayingSources(): AudioSource[] {
    return Array.from(this.playingSources);
  }

  /**
   * 加载音频
   * @param url 音频URL
   * @param onLoad 加载完成回调
   * @param onError 加载错误回调
   */
  public loadAudio(url: string, onLoad?: (buffer: AudioBuffer) => void, onError?: (error: Error) => void): void {
    if (!this.initialized || !this.supported || !this.context) {
      if (onError) onError(new Error('音频系统未初始化或不支持'));
      return;
    }

    // 如果已缓存，则直接返回
    if (this.bufferCache.has(url)) {
      const buffer = this.bufferCache.get(url);
      if (buffer && onLoad) {
        onLoad(buffer);
      }
      return;
    }

    // 加载音频
    this.audioLoader.load(
      url,
      buffer => {
        // 缓存音频
        this.bufferCache.set(url, buffer);

        // 触发加载完成事件
        this.eventEmitter.emit(AudioEventType.LOAD, { url, buffer });

        // 调用回调
        if (onLoad) onLoad(buffer);
      },
      undefined,
      error => {
        console.error('加载音频失败:', error);

        // 创建标准错误对象
        const errorObj = new Error(error instanceof Error ? error.message : String(error));

        // 触发加载错误事件
        this.eventEmitter.emit(AudioEventType.ERROR, { url, error: errorObj });

        // 调用回调
        if (onError) onError(errorObj);
      }
    );
  }

  /**
   * 播放音频
   * @param id 音频源ID
   * @param url 音频URL
   * @param options 播放选项
   * @returns 是否成功开始播放
   */
  public play(id: string, url: string, options: any = {}): boolean {
    if (!this.initialized || !this.supported || !this.context) return false;

    // 恢复音频上下文
    this.resumeContext();

    // 如果达到最大同时播放数量，则停止最早的音频
    if (this.playingSources.size >= this.maxSimultaneous) {
      const oldestSource = this.playingSources.values().next().value;
      if (oldestSource) {
        oldestSource.stop();
        this.playingSources.delete(oldestSource);
      }
    }

    // 获取或创建音频源
    let source = this.getSource(id);
    if (!source) {
      source = this.createSource(id, options.type || AudioType.SOUND);
      if (!source) return false;
    }

    // 设置音频源选项
    if (options.loop !== undefined) source.setLoop(options.loop);
    if (options.volume !== undefined) source.setVolume(options.volume);
    if (options.playbackRate !== undefined) source.setPlaybackRate(options.playbackRate);
    if (options.position) source.setPosition((options as any).getPosition().x, (options as any).getPosition().y, (options as any).getPosition().z);
    if (options.velocity) source.setVelocity(options.velocity.x, options.velocity.y, options.velocity.z);
    if (options.refDistance !== undefined) source.setRefDistance(options.refDistance);
    if (options.maxDistance !== undefined) source.setMaxDistance(options.maxDistance);
    if (options.rolloffFactor !== undefined) source.setRolloffFactor(options.rolloffFactor);
    if (options.coneInnerAngle !== undefined) source.setConeInnerAngle(options.coneInnerAngle);
    if (options.coneOuterAngle !== undefined) source.setConeOuterAngle(options.coneOuterAngle);
    if (options.coneOuterGain !== undefined) source.setConeOuterGain(options.coneOuterGain);
    if (options.detune !== undefined) source.setDetune(options.detune);

    // 加载并播放音频
    if (this.bufferCache.has(url)) {
      // 如果已缓存，则直接播放
      const buffer = this.bufferCache.get(url);
      if (buffer) {
        source.setBuffer(buffer);
        source.play(options.offset, options.duration);
        this.playingSources.add(source);
        return true;
      }
    } else if (this.autoLoad) {
      // 如果未缓存且启用自动加载，则加载并播放
      this.loadAudio(
        url,
        buffer => {
          source?.setBuffer(buffer);
          source?.play(options.offset, options.duration);
          if (source) this.playingSources.add(source);
        },
        error => {
          console.error('播放音频失败:', error);
        }
      );
      return true;
    }

    return false;
  }

  /**
   * 停止音频
   * @param id 音频源ID
   * @returns 是否成功停止
   */
  public stop(id: string): boolean {
    const source = this.getSource(id);
    if (!source) return false;

    source.stop();
    this.playingSources.delete(source);

    return true;
  }

  /**
   * 暂停音频
   * @param id 音频源ID
   * @returns 是否成功暂停
   */
  public pause(id: string): boolean {
    const source = this.getSource(id);
    if (!source) return false;

    source.pause();

    return true;
  }

  /**
   * 恢复音频
   * @param id 音频源ID
   * @returns 是否成功恢复
   */
  public resume(id: string): boolean {
    const source = this.getSource(id);
    if (!source) return false;

    source.resume();

    return true;
  }

  /**
   * 停止所有音频
   */
  public stopAll(): void {
    for (const source of Array.from(this.playingSources)) {
      source.stop();
    }

    this.playingSources.clear();
  }

  /**
   * 暂停所有音频
   */
  public pauseAll(): void {
    for (const source of Array.from(this.playingSources)) {
      source.pause();
    }
  }

  /**
   * 恢复所有音频
   */
  public resumeAll(): void {
    for (const source of Array.from(this.playingSources)) {
      source.resume();
    }
  }

  /**
   * 停止指定类型的所有音频
   * @param type 音频类型
   */
  public stopAllByType(type: AudioType): void {
    for (const source of Array.from(this.playingSources)) {
      if (source.getType() === type) {
        source.stop();
        this.playingSources.delete(source);
      }
    }
  }

  /**
   * 暂停指定类型的所有音频
   * @param type 音频类型
   */
  public pauseAllByType(type: AudioType): void {
    for (const source of Array.from(this.playingSources)) {
      if (source.getType() === type) {
        source.pause();
      }
    }
  }

  /**
   * 恢复指定类型的所有音频
   * @param type 音频类型
   */
  public resumeAllByType(type: AudioType): void {
    for (const source of Array.from(this.playingSources)) {
      if (source.getType() === type) {
        source.resume();
      }
    }
  }

  /**
   * 设置全局音量
   * @param volume 音量（0-1）
   */
  public setVolume(volume: number): void {
    this.volume = Math.max(0, Math.min(1, volume));

    // 更新主音量节点
    if (this.masterGain) {
      this.masterGain.gain.value = this.muted ? 0 : this.volume;
    }

    // 触发音量变化事件
    this.eventEmitter.emit(AudioEventType.VOLUME_CHANGE, { volume: this.volume });
  }

  /**
   * 获取全局音量
   * @returns 音量（0-1）
   */
  public getVolume(): number {
    return this.volume;
  }

  /**
   * 设置全局静音
   * @param muted 是否静音
   */
  public setMuted(muted: boolean): void {
    this.muted = muted;

    // 更新主音量节点
    if (this.masterGain) {
      this.masterGain.gain.value = this.muted ? 0 : this.volume;
    }

    // 触发静音变化事件
    this.eventEmitter.emit(AudioEventType.MUTE_CHANGE, { muted: this.muted });
  }

  /**
   * 获取全局静音
   * @returns 是否静音
   */
  public getMuted(): boolean {
    return this.muted;
  }

  /**
   * 设置音频类型音量
   * @param type 音频类型
   * @param volume 音量（0-1）
   */
  public setTypeVolume(type: AudioType, volume: number): void {
    volume = Math.max(0, Math.min(1, volume));
    this.typeVolumes.set(type, volume);

    // 更新类型音量节点
    const gain = this.typeGains.get(type);
    const muted = this.typeMuted.get(type) || false;
    if (gain) {
      gain.gain.value = muted ? 0 : volume;
    }

    // 触发音量变化事件
    this.eventEmitter.emit(AudioEventType.VOLUME_CHANGE, { type, volume });
  }

  /**
   * 获取音频类型音量
   * @param type 音频类型
   * @returns 音量（0-1）
   */
  public getTypeVolume(type: AudioType): number {
    return this.typeVolumes.get(type) || 1.0;
  }

  /**
   * 设置音频类型静音
   * @param type 音频类型
   * @param muted 是否静音
   */
  public setTypeMuted(type: AudioType, muted: boolean): void {
    this.typeMuted.set(type, muted);

    // 更新类型音量节点
    const gain = this.typeGains.get(type);
    const volume = this.typeVolumes.get(type) || 1.0;
    if (gain) {
      gain.gain.value = muted ? 0 : volume;
    }

    // 触发静音变化事件
    this.eventEmitter.emit(AudioEventType.MUTE_CHANGE, { type, muted });
  }

  /**
   * 获取音频类型静音
   * @param type 音频类型
   * @returns 是否静音
   */
  public getTypeMuted(type: AudioType): boolean {
    return this.typeMuted.get(type) || false;
  }

  /**
   * 设置是否启用
   * @param enabled 是否启用
   */
  public setAudioEnabled(enabled: boolean): void {
    this.audioEnabled = enabled;

    // 如果禁用，则暂停所有音频
    if (!enabled) {
      this.pauseAll();
      this.onDisable();
    } else {
      // 如果启用，则恢复音频上下文
      this.resumeContext();
      this.onEnable();
    }
  }

  /**
   * 获取是否启用
   * @returns 是否启用
   */
  public getAudioEnabled(): boolean {
    return this.audioEnabled;
  }

  /**
   * 设置是否启用空间音频
   * @param enabled 是否启用
   */
  public setSpatialAudio(enabled: boolean): void {
    this.spatialAudio = enabled;

    // 更新所有音频源
    for (const source of Array.from(this.sources.values())) {
      source.setSpatial(enabled);
    }
  }

  /**
   * 获取是否启用空间音频
   * @returns 是否启用
   */
  public getSpatialAudio(): boolean {
    return this.spatialAudio;
  }

  /**
   * 获取音频监听器
   * @returns 音频监听器
   */
  public getListener(): AudioListener | null {
    return this.listener;
  }

  /**
   * 获取音频上下文
   * @returns 音频上下文
   */
  public getContext(): AudioContext | null {
    return this.context;
  }

  /**
   * 获取是否支持Web Audio API
   * @returns 是否支持
   */
  public isSupported(): boolean {
    return this.supported;
  }

  /**
   * 清除音频缓存
   */
  public clearCache(): void {
    this.bufferCache.clear();
  }

  /**
   * 添加事件监听器
   * @param type 事件类型
   * @param listener 监听器函数
   * @returns 当前实例，用于链式调用
   */
  public on(event: string, callback: EventCallback): this {
    // 使用私有的事件发射器处理事件
    this.eventEmitter.on(event, callback);
    return this;
  }

  /**
   * 移除事件监听器
   * @param type 事件类型
   * @param listener 监听器函数
   * @returns 当前实例，用于链式调用
   */
  public off(event: string, callback?: EventCallback): this {
    // 使用私有的事件发射器处理事件
    this.eventEmitter.off(event, callback);
    return this;
  }

  /**
   * 销毁系统
   */
  public dispose(): void {
    if (this.destroyed) return;

    // 停止所有音频
    this.stopAll();

    // 销毁所有音频源
    for (const source of Array.from(this.sources.values())) {
      (source as any).dispose();
    }

    this.sources.clear();
    this.playingSources.clear();
    this.bufferCache.clear();

    // 断开所有节点
    for (const gain of Array.from(this.typeGains.values())) {
      gain.disconnect();
    }

    if (this.masterGain) {
      this.masterGain.disconnect();
    }

    // 关闭音频上下文
    if (this.context && this.context.state !== 'closed') {
      this.context.close().catch(error => {
        console.error('关闭音频上下文失败:', error);
      });
    }

    // 销毁音频监听器
    if (this.listener) {
      (this.listener as any).dispose();
    }

    // 移除所有事件监听器
    this.eventEmitter.removeAllListeners();

    this.typeGains.clear();
    this.masterGain = null;
    this.context = null;
    this.listener = null;

    this.initialized = false;
    this.destroyed = true;

    (super as any).dispose();
  }
}