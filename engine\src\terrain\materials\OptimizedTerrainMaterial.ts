/**
 * 优化的地形材质
 * 用于提高地形渲染性能
 */
import * as THREE from 'three';
import { optimizedTerrainVertexShader } from '../shaders/OptimizedTerrainVertexShader';
import { optimizedTerrainFragmentShader } from '../shaders/OptimizedTerrainFragmentShader';
import { TerrainTextureLayer } from '../components/TerrainComponent';
import { Debug } from '../../utils/Debug';

/**
 * 优化的地形材质选项接口
 */
export interface OptimizedTerrainMaterialOptions {
  /** 纹理层 */
  layers?: TerrainTextureLayer[];
  /** 最大高度 */
  maxHeight?: number;
  /** 是否使用LOD */
  useLOD?: boolean;
  /** LOD距离 */
  lodDistance?: number;
  /** LOD因子 */
  lodFactor?: number;
  /** 是否使用雾 */
  useFog?: boolean;
  /** 雾颜色 */
  fogColor?: THREE.Color;
  /** 雾近距离 */
  fogNear?: number;
  /** 雾远距离 */
  fogFar?: number;
  /** 环境光颜色 */
  ambientColor?: THREE.Color;
  /** 光源颜色 */
  lightColor?: THREE.Color;
  /** 光源位置 */
  lightPosition?: THREE.Vector3;
  /** 是否使用裁剪平面 */
  useClipPlane?: boolean;
  /** 裁剪平面 */
  clipPlane?: THREE.Vector4;
  /** 是否使用混合贴图 */
  useBlendMaps?: boolean;
  /** 是否使用线框 */
  wireframe?: boolean;
}

/**
 * 优化的地形材质类
 */
export class OptimizedTerrainMaterial extends THREE.ShaderMaterial {
  /** 纹理层 */
  private layers: TerrainTextureLayer[];

  /** 低分辨率纹理渲染目标 */
  private lowResRenderTarget: THREE.WebGLRenderTarget | null = null;

  /** 低分辨率相机 */
  private lowResCamera: THREE.OrthographicCamera | null = null;

  /** 低分辨率场景 */
  private lowResScene: THREE.Scene | null = null;

  /** 低分辨率网格 */
  private lowResMesh: THREE.Mesh | null = null;

  /** 是否需要更新低分辨率纹理 */
  private needsLowResUpdate: boolean = true;

  /** 渲染器 */
  private renderer: THREE.WebGLRenderer | null = null;

  /**
   * 创建优化的地形材质
   * @param options 选项
   */
  constructor(options: OptimizedTerrainMaterialOptions = {}) {
    // 默认选项
    const layers = options.layers || [];
    const maxHeight = options.maxHeight || 100;
    const useLOD = options.useLOD !== undefined ? options.useLOD : true;
    const lodDistance = options.lodDistance || 300;
    const lodFactor = options.lodFactor || 0.01;
    const useFog = options.useFog !== undefined ? options.useFog : true;
    const fogColor = options.fogColor || new THREE.Color(0xcccccc);
    const fogNear = options.fogNear || 500;
    const fogFar = options.fogFar || 1000;
    const ambientColor = options.ambientColor || new THREE.Color(0x666666);
    const lightColor = options.lightColor || new THREE.Color(0xffffff);
    const lightPosition = options.lightPosition || new THREE.Vector3(1000, 1000, 1000);
    const useClipPlane = options.useClipPlane !== undefined ? options.useClipPlane : false;
    const clipPlane = options.clipPlane || new THREE.Vector4(0, 0, 0, 0);
    const useBlendMaps = options.useBlendMaps !== undefined ? options.useBlendMaps : false;
    const wireframe = options.wireframe !== undefined ? options.wireframe : false;

    // 创建纹理数组
    const textures: THREE.Texture[] = [];
    const normalMaps: THREE.Texture[] = [];
    const roughnessMaps: THREE.Texture[] = [];
    const aoMaps: THREE.Texture[] = [];
    const blendMaps: THREE.Texture[] = [];
    const tilingFactors: number[] = [];
    const minHeights: number[] = [];
    const maxHeights: number[] = [];
    const minSlopes: number[] = [];
    const maxSlopes: number[] = [];

    // 填充纹理数组
    for (let i = 0; i < layers.length; i++) {
      const layer = layers[i];

      textures.push(layer.texture || new THREE.Texture());
      normalMaps.push(layer.normalMap || new THREE.Texture());
      roughnessMaps.push(layer.roughnessMap || new THREE.Texture());
      aoMaps.push(layer.aoMap || new THREE.Texture());
      blendMaps.push(layer.blendMap || new THREE.Texture());

      tilingFactors.push(layer.tiling || 1);
      minHeights.push(layer.minHeight || 0);
      maxHeights.push(layer.maxHeight || 1);
      minSlopes.push(layer.minSlope || 0);
      maxSlopes.push(layer.maxSlope || 1);
    }

    // 创建低分辨率纹理
    const lowResTexture = new THREE.Texture();

    // 创建着色器材质
    super({
      vertexShader: optimizedTerrainVertexShader,
      fragmentShader: optimizedTerrainFragmentShader,
      uniforms: {
        // 基础参数
        uMaxHeight: { value: maxHeight },
        uUseLOD: { value: useLOD },
        uLODDistance: { value: lodDistance },
        uLODFactor: { value: lodFactor },
        uUseFog: { value: useFog },
        uFogColor: { value: fogColor },
        uFogNear: { value: fogNear },
        uFogFar: { value: fogFar },
        uAmbientColor: { value: ambientColor },
        uLightColor: { value: lightColor },
        uLightPosition: { value: lightPosition },
        uUseClipPlane: { value: useClipPlane },
        uClipPlane: { value: clipPlane },
        uUseBlendMaps: { value: useBlendMaps },
        uLayerCount: { value: layers.length },

        // 纹理数组
        uTextures: { value: textures },
        uNormalMaps: { value: normalMaps },
        uRoughnessMaps: { value: roughnessMaps },
        uAOMaps: { value: aoMaps },
        uBlendMaps: { value: blendMaps },

        // 纹理参数
        uTilingFactors: { value: tilingFactors },
        uMinHeights: { value: minHeights },
        uMaxHeights: { value: maxHeights },
        uMinSlopes: { value: minSlopes },
        uMaxSlopes: { value: maxSlopes },

        // LOD纹理
        uLowResTexture: { value: lowResTexture }
      },
      wireframe: wireframe,
      lights: false,
      fog: false
    });

    // 存储层
    this.layers = layers;

    // 初始化低分辨率渲染
    if (useLOD) {
      this.initLowResRendering();
    }
  }

  /**
   * 初始化低分辨率渲染
   */
  private initLowResRendering(): void {
    try {
      // 创建低分辨率渲染目标
      this.lowResRenderTarget = new THREE.WebGLRenderTarget(256, 256, {
        minFilter: THREE.LinearFilter,
        magFilter: THREE.LinearFilter,
        format: THREE.RGBAFormat,
        stencilBuffer: false,
        depthBuffer: true
      });

      // 创建正交相机
      this.lowResCamera = new THREE.OrthographicCamera(-0.5, 0.5, 0.5, -0.5, 0.1, 10);
      (this.lowResCamera as any).getPosition().z = 1;

      // 创建场景
      this.lowResScene = new THREE.Scene();

      // 创建平面几何体
      const geometry = new THREE.PlaneGeometry(1, 1);

      // 创建简化的材质
      const material = new THREE.MeshBasicMaterial({
        map: null,
        color: 0xffffff
      });

      // 创建网格
      this.lowResMesh = new THREE.Mesh(geometry, material);
      this.lowResScene.add(this.lowResMesh);

      // 设置低分辨率纹理
      this.uniforms.uLowResTexture.value = this.lowResRenderTarget.texture;
    } catch (error) {
      Debug.warn('OptimizedTerrainMaterial', '初始化低分辨率渲染失败:', error);
      this.lowResRenderTarget = null;
      this.lowResCamera = null;
      this.lowResScene = null;
      this.lowResMesh = null;
    }
  }

  /**
   * 设置渲染器
   * @param renderer 渲染器
   */
  public setRenderer(renderer: THREE.WebGLRenderer): void {
    this.renderer = renderer;
  }

  /**
   * 更新低分辨率纹理
   * @param geometry 几何体
   */
  public updateLowResTexture(geometry: THREE.BufferGeometry): void {
    if (!this.lowResRenderTarget || !this.lowResCamera || !this.lowResScene || !this.lowResMesh || !this.renderer) {
      return;
    }

    try {
      // 更新低分辨率网格的几何体
      this.lowResMesh.geometry = geometry;

      // 更新低分辨率网格的材质
      if (this.lowResMesh.material instanceof THREE.MeshBasicMaterial) {
        // 创建临时材质
        const tempMaterial = new THREE.MeshBasicMaterial({
          map: this.uniforms.uTextures.value[0],
          color: 0xffffff
        });

        // 设置材质
        this.lowResMesh.material = tempMaterial;
      }

      // 渲染到低分辨率纹理
      const currentRenderTarget = this.renderer.getRenderTarget();
      this.renderer.setRenderTarget(this.lowResRenderTarget);
      this.renderer.render(this.lowResScene, this.lowResCamera);
      this.renderer.setRenderTarget(currentRenderTarget);

      // 标记不需要更新
      this.needsLowResUpdate = false;
    } catch (error) {
      Debug.warn('OptimizedTerrainMaterial', '更新低分辨率纹理失败:', error);
    }
  }

  /**
   * 标记需要更新低分辨率纹理
   */
  public markLowResTextureNeedsUpdate(): void {
    this.needsLowResUpdate = true;
  }

  /**
   * 检查是否需要更新低分辨率纹理
   * @returns 是否需要更新
   */
  public needsLowResTextureUpdate(): boolean {
    return this.needsLowResUpdate;
  }

  /**
   * 设置LOD参数
   * @param distance LOD距离
   * @param factor LOD因子
   */
  public setLODParams(distance: number, factor: number): void {
    this.uniforms.uLODDistance.value = distance;
    this.uniforms.uLODFactor.value = factor;
  }

  /**
   * 设置雾参数
   * @param useFog 是否使用雾
   * @param fogColor 雾颜色
   * @param fogNear 雾近距离
   * @param fogFar 雾远距离
   */
  public setFogParams(useFog: boolean, fogColor?: THREE.Color, fogNear?: number, fogFar?: number): void {
    this.uniforms.uUseFog.value = useFog;

    if (fogColor) {
      this.uniforms.uFogColor.value = fogColor;
    }

    if (fogNear !== undefined) {
      this.uniforms.uFogNear.value = fogNear;
    }

    if (fogFar !== undefined) {
      this.uniforms.uFogFar.value = fogFar;
    }
  }

  /**
   * 设置光照参数
   * @param ambientColor 环境光颜色
   * @param lightColor 光源颜色
   * @param lightPosition 光源位置
   */
  public setLightParams(ambientColor?: THREE.Color, lightColor?: THREE.Color, lightPosition?: THREE.Vector3): void {
    if (ambientColor) {
      this.uniforms.uAmbientColor.value = ambientColor;
    }

    if (lightColor) {
      this.uniforms.uLightColor.value = lightColor;
    }

    if (lightPosition) {
      this.uniforms.uLightPosition.value = lightPosition;
    }
  }

  /**
   * 设置裁剪平面
   * @param useClipPlane 是否使用裁剪平面
   * @param clipPlane 裁剪平面
   */
  public setClipPlane(useClipPlane: boolean, clipPlane?: THREE.Vector4): void {
    this.uniforms.uUseClipPlane.value = useClipPlane;

    if (clipPlane) {
      this.uniforms.uClipPlane.value = clipPlane;
    }
  }

  /**
   * 更新纹理层
   * @param layers 纹理层
   */
  public updateLayers(layers: TerrainTextureLayer[]): void {
    // 更新层数
    this.uniforms.uLayerCount.value = layers.length;

    // 创建纹理数组
    const textures: THREE.Texture[] = [];
    const normalMaps: THREE.Texture[] = [];
    const roughnessMaps: THREE.Texture[] = [];
    const aoMaps: THREE.Texture[] = [];
    const blendMaps: THREE.Texture[] = [];
    const tilingFactors: number[] = [];
    const minHeights: number[] = [];
    const maxHeights: number[] = [];
    const minSlopes: number[] = [];
    const maxSlopes: number[] = [];

    // 填充纹理数组
    for (let i = 0; i < layers.length; i++) {
      const layer = layers[i];

      textures.push(layer.texture || new THREE.Texture());
      normalMaps.push(layer.normalMap || new THREE.Texture());
      roughnessMaps.push(layer.roughnessMap || new THREE.Texture());
      aoMaps.push(layer.aoMap || new THREE.Texture());
      blendMaps.push(layer.blendMap || new THREE.Texture());

      tilingFactors.push(layer.tiling || 1);
      minHeights.push(layer.minHeight || 0);
      maxHeights.push(layer.maxHeight || 1);
      minSlopes.push(layer.minSlope || 0);
      maxSlopes.push(layer.maxSlope || 1);
    }

    // 更新uniforms
    this.uniforms.uTextures.value = textures;
    this.uniforms.uNormalMaps.value = normalMaps;
    this.uniforms.uRoughnessMaps.value = roughnessMaps;
    this.uniforms.uAOMaps.value = aoMaps;
    this.uniforms.uBlendMaps.value = blendMaps;
    this.uniforms.uTilingFactors.value = tilingFactors;
    this.uniforms.uMinHeights.value = minHeights;
    this.uniforms.uMaxHeights.value = maxHeights;
    this.uniforms.uMinSlopes.value = minSlopes;
    this.uniforms.uMaxSlopes.value = maxSlopes;

    // 存储层
    this.layers = layers;

    // 标记需要更新低分辨率纹理
    this.markLowResTextureNeedsUpdate();
  }

  /**
   * 释放资源
   */
  public dispose(): void {
    // 调用父类的dispose方法
    (super as any).dispose();

    // 释放低分辨率渲染目标
    if (this.lowResRenderTarget) {
      (this.lowResRenderTarget as any).dispose();
      this.lowResRenderTarget = null;
    }

    // 释放低分辨率网格
    if (this.lowResMesh) {
      if (this.lowResMesh.geometry) {
        (this.lowResMesh.geometry as any).dispose();
      }

      if (this.lowResMesh.material) {
        if (Array.isArray(this.lowResMesh.material)) {
          this.lowResMesh.material.forEach(material => (material as any).dispose());
        } else {
          (this.lowResMesh.material as any).dispose();
        }
      }

      this.lowResMesh = null;
    }

    // 清空场景
    if (this.lowResScene) {
      this.lowResScene = null;
    }

    // 清空相机
    this.lowResCamera = null;

    // 清空渲染器
    this.renderer = null;
  }
}
