/**
 * 体积雾效果渲染器
 * 用于在地下环境中创建体积雾效果
 */
import * as THREE from 'three';
// 使用类型断言导入后处理模块
// @ts-ignore
import { EffectComposer } from 'three/examples/jsm/postprocessing/EffectComposer.js';
// @ts-ignore
import { ShaderPass } from 'three/examples/jsm/postprocessing/ShaderPass.js';
// @ts-ignore
import { RenderPass } from 'three/examples/jsm/postprocessing/RenderPass.js';
import { PerformanceMonitor, PerformanceMetricType } from '../utils/PerformanceMonitor';

/**
 * 体积雾参数
 */
export interface VolumetricFogParams {
  /** 雾密度 */
  density: number;
  /** 雾颜色 */
  color: THREE.Color;
  /** 雾高度 */
  height: number;
  /** 雾范围 */
  range: number;
  /** 雾散射系数 */
  scattering: number;
  /** 雾吸收系数 */
  absorption: number;
  /** 雾相位函数参数 */
  phase: number;
  /** 雾噪声缩放 */
  noiseScale: number;
  /** 雾噪声速度 */
  noiseSpeed: number;
  /** 雾噪声强度 */
  noiseStrength: number;
  /** 是否启用性能优化 */
  enableOptimization: boolean;
  /** 是否启用自适应质量 */
  enableAdaptiveQuality: boolean;
  /** 最大采样步数 */
  maxSteps: number;
}

/**
 * 体积雾区域
 */
export interface VolumetricFogVolume {
  /** 位置 */
  position: THREE.Vector3;
  /** 大小 */
  size: THREE.Vector3;
  /** 密度 */
  density: number;
  /** 颜色 */
  color: THREE.Color;
}

/**
 * 体积雾着色器
 */
const VolumetricFogShader = {
  uniforms: {
    'tDiffuse': { value: null },
    'tDepth': { value: null },
    'cameraNear': { value: 0.1 },
    'cameraFar': { value: 1000 },
    'fogColor': { value: new THREE.Color(0xcccccc) },
    'fogDensity': { value: 0.1 },
    'fogHeight': { value: 10.0 },
    'fogRange': { value: 100.0 },
    'scattering': { value: 0.2 },
    'absorption': { value: 0.1 },
    'phase': { value: 0.5 },
    'noiseScale': { value: 0.1 },
    'noiseSpeed': { value: 0.05 },
    'noiseStrength': { value: 0.2 },
    'time': { value: 0.0 },
    'fogVolumes': { value: [] },
    'maxSteps': { value: 100 },
    'projectionMatrixInverse': { value: new THREE.Matrix4() },
    'viewMatrixInverse': { value: new THREE.Matrix4() },
    'cameraPosition': { value: new THREE.Vector3() }
  },

  vertexShader: `
    varying vec2 vUv;
    void main() {
      vUv = uv;
      gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
    }
  `,

  fragmentShader: `
    uniform sampler2D tDiffuse;
    uniform sampler2D tDepth;
    uniform float cameraNear;
    uniform float cameraFar;
    uniform vec3 fogColor;
    uniform float fogDensity;
    uniform float fogHeight;
    uniform float fogRange;
    uniform float scattering;
    uniform float absorption;
    uniform float phase;
    uniform float noiseScale;
    uniform float noiseSpeed;
    uniform float noiseStrength;
    uniform float time;
    uniform int maxSteps;
    uniform mat4 projectionMatrixInverse;
    uniform mat4 viewMatrixInverse;
    uniform vec3 cameraPosition;

    varying vec2 vUv;

    // 简化的噪声函数
    float noise(vec3 p) {
      vec3 i = floor(p);
      vec3 f = fract(p);
      f = f * f * (3.0 - 2.0 * f);

      // 使用简单的伪随机函数
      float a = sin(dot(i, vec3(12.9898, 78.233, 37.719))) * 43758.5453;
      float b = sin(dot(i + vec3(1.0, 0.0, 0.0), vec3(12.9898, 78.233, 37.719))) * 43758.5453;
      float c = sin(dot(i + vec3(0.0, 1.0, 0.0), vec3(12.9898, 78.233, 37.719))) * 43758.5453;
      float d = sin(dot(i + vec3(1.0, 1.0, 0.0), vec3(12.9898, 78.233, 37.719))) * 43758.5453;

      float i1 = mix(fract(a), fract(b), f.x);
      float i2 = mix(fract(c), fract(d), f.x);

      return mix(i1, i2, f.y);
    }

    // 相位函数
    float henyeyGreenstein(float cosTheta, float g) {
      float g2 = g * g;
      return (1.0 - g2) / pow(1.0 + g2 - 2.0 * g * cosTheta, 1.5);
    }

    // 从深度纹理获取世界空间位置
    vec3 getWorldPos(float depth) {
      // 将深度值转换为NDC空间
      float z = depth * 2.0 - 1.0;

      // 构建裁剪空间坐标
      vec4 clipSpacePosition = vec4(vUv * 2.0 - 1.0, z, 1.0);

      // 转换到视图空间
      vec4 viewSpacePosition = projectionMatrixInverse * clipSpacePosition;
      viewSpacePosition /= viewSpacePosition.w;

      // 转换到世界空间
      vec4 worldSpacePosition = viewMatrixInverse * viewSpacePosition;
      return worldSpacePosition.xyz;
    }

    // 线性化深度值
    float linearizeDepth(float depth) {
      float z = depth * 2.0 - 1.0; // 转换到NDC
      return (2.0 * cameraNear * cameraFar) / (cameraFar + cameraNear - z * (cameraFar - cameraNear));
    }

    void main() {
      // 获取原始颜色和深度
      vec4 texel = texture2D(tDiffuse, vUv);
      float depth = texture2D(tDepth, vUv).x;

      // 如果是背景，直接返回原始颜色
      if (depth >= 1.0) {
        gl_FragColor = texel;
        return;
      }

      // 获取世界空间位置
      vec3 worldPos = getWorldPos(depth);
      vec3 rayOrigin = cameraPosition;
      vec3 rayDir = normalize(worldPos - rayOrigin);

      // 计算光线长度
      float rayLength = length(worldPos - rayOrigin);

      // 体积雾渲染
      float transmittance = 1.0;
      vec3 scatteredLight = vec3(0.0);

      // 光线步进
      float stepSize = maxSteps > 0 ? rayLength / float(maxSteps) : rayLength;
      vec3 step = rayDir * stepSize;
      vec3 currentPos = rayOrigin;

      // 如果光线长度太短，直接返回原始颜色
      if (rayLength < 0.001) {
        gl_FragColor = texel;
        return;
      }

      for (int i = 0; i < 200; i++) {
        if (i >= maxSteps) break;

        // 计算当前位置的雾密度
        float height = currentPos.y;
        float heightFactor = exp(-max(0.0, height - fogHeight) / fogRange);

        // 添加噪声
        float noiseValue = noise(currentPos * noiseScale + vec3(0.0, 0.0, time * noiseSpeed));
        float density = fogDensity * heightFactor * (1.0 + noiseStrength * (noiseValue - 0.5));

        // 如果密度太低，跳过
        if (density <= 0.0001) {
          currentPos += step;
          continue;
        }

        // 计算光散射
        float cosTheta = dot(rayDir, normalize(vec3(0.0, 1.0, 0.0)));
        float phaseValue = henyeyGreenstein(cosTheta, phase);

        // 计算光吸收和散射
        float extinction = density * (absorption + scattering);
        float scatter = density * scattering * phaseValue;

        // 更新透射率和散射光
        float segmentTransmittance = exp(-extinction * stepSize);
        float luminance = scatter * stepSize;

        scatteredLight += transmittance * (1.0 - segmentTransmittance) * fogColor * luminance;
        transmittance *= segmentTransmittance;

        // 如果透射率太低，提前退出
        if (transmittance < 0.01) {
          break;
        }

        // 更新位置
        currentPos += step;

        // 如果到达了终点，退出
        if (length(currentPos - rayOrigin) >= rayLength) {
          break;
        }
      }

      // 混合原始颜色和雾效果
      gl_FragColor = vec4(texel.rgb * transmittance + scatteredLight, texel.a);
    }
  `
};

/**
 * 体积雾渲染器
 */
export class VolumetricFogRenderer {
  /** 效果合成器 */
  private composer: EffectComposer;
  /** 渲染通道 */
  private renderPass: RenderPass;
  /** 体积雾通道 */
  private fogPass: ShaderPass;
  /** 性能监视器 */
  private performanceMonitor: PerformanceMonitor = PerformanceMonitor.getInstance();
  /** 雾体积列表 */
  private fogVolumes: VolumetricFogVolume[] = [];
  /** 时间 */
  private time: number = 0;
  /** 是否已初始化 */
  private initialized: boolean = false;
  /** 参数 */
  private params: VolumetricFogParams;

  /**
   * 构造函数
   * @param renderer 渲染器
   * @param scene 场景
   * @param camera 相机
   * @param params 体积雾参数
   */
  constructor(
    private renderer: THREE.WebGLRenderer,
    private scene: THREE.Scene,
    private camera: THREE.PerspectiveCamera,
    params: VolumetricFogParams
  ) {
    this.params = params;
    this.initialize();
  }

  /**
   * 初始化
   */
  private initialize(): void {
    // 获取渲染器尺寸
    const size = new THREE.Vector2();
    this.renderer.getSize(size);

    // 创建深度纹理
    const depthTexture = new THREE.DepthTexture(size.width, size.height);
    depthTexture.type = THREE.UnsignedShortType;

    // 创建带深度纹理的渲染目标
    const renderTarget = new THREE.WebGLRenderTarget(
      size.width,
      size.height,
      {
        minFilter: THREE.LinearFilter,
        magFilter: THREE.LinearFilter,
        format: THREE.RGBAFormat,
        depthTexture: depthTexture,
        depthBuffer: true
      }
    );

    // 创建效果合成器
    this.composer = new EffectComposer(this.renderer, renderTarget);

    // 创建渲染通道
    this.renderPass = new RenderPass(this.scene, this.camera);
    this.composer.addPass(this.renderPass);

    // 创建体积雾通道
    this.fogPass = new ShaderPass(VolumetricFogShader);
    this.fogPass.uniforms['cameraNear'].value = this.camera.near;
    this.fogPass.uniforms['cameraFar'].value = this.camera.far;

    // 设置深度纹理
    this.fogPass.uniforms['tDepth'].value = depthTexture;

    this.updateFogParams(this.params);
    this.composer.addPass(this.fogPass);

    this.initialized = true;
  }

  /**
   * 更新体积雾参数
   * @param params 体积雾参数
   */
  public updateFogParams(params: VolumetricFogParams): void {
    this.params = params;

    if (!this.initialized) return;

    // 更新着色器参数
    this.fogPass.uniforms['fogColor'].value = params.color;
    this.fogPass.uniforms['fogDensity'].value = params.density;
    this.fogPass.uniforms['fogHeight'].value = params.height;
    this.fogPass.uniforms['fogRange'].value = params.range;
    this.fogPass.uniforms['scattering'].value = params.scattering;
    this.fogPass.uniforms['absorption'].value = params.absorption;
    this.fogPass.uniforms['phase'].value = params.phase;
    this.fogPass.uniforms['noiseScale'].value = params.noiseScale;
    this.fogPass.uniforms['noiseSpeed'].value = params.noiseSpeed;
    this.fogPass.uniforms['noiseStrength'].value = params.noiseStrength;
    this.fogPass.uniforms['maxSteps'].value = params.maxSteps;
  }

  /**
   * 添加雾体积
   * @param volume 雾体积
   */
  public addFogVolume(volume: VolumetricFogVolume): void {
    this.fogVolumes.push(volume);
    this.updateFogVolumes();
  }

  /**
   * 移除雾体积
   * @param volume 雾体积
   */
  public removeFogVolume(volume: VolumetricFogVolume): void {
    const index = this.fogVolumes.indexOf(volume);
    if (index !== -1) {
      this.fogVolumes.splice(index, 1);
      this.updateFogVolumes();
    }
  }

  /**
   * 清除所有雾体积
   */
  public clearFogVolumes(): void {
    this.fogVolumes = [];
    this.updateFogVolumes();
  }

  /**
   * 更新雾体积
   */
  private updateFogVolumes(): void {
    if (!this.initialized) return;
    this.fogPass.uniforms['fogVolumes'].value = this.fogVolumes;
  }

  /**
   * 创建洞穴体积雾
   * @param position 位置
   * @param size 大小
   * @param color 颜色
   * @param density 密度
   * @returns 体积雾体积
   */
  public createCaveFog(position: THREE.Vector3, size: THREE.Vector3, color: THREE.Color, density: number): VolumetricFogVolume {
    const volume: VolumetricFogVolume = {
      position: position.clone(),
      size: size.clone(),
      density: density,
      color: color.clone()
    };

    this.addFogVolume(volume);
    return volume;
  }

  /**
   * 创建地下河流体积雾
   * @param riverPath 河流路径点
   * @param width 宽度
   * @param height 高度
   * @param color 颜色
   * @param density 密度
   * @returns 体积雾体积数组
   */
  public createUndergroundRiverFog(riverPath: THREE.Vector3[], width: number, height: number, color: THREE.Color, density: number): VolumetricFogVolume[] {
    const volumes: VolumetricFogVolume[] = [];

    // 沿着河流路径创建多个体积雾
    for (let i = 0; i < riverPath.length - 1; i++) {
      const start = riverPath[i];
      const end = riverPath[i + 1];

      // 计算段中点
      const midPoint = new THREE.Vector3().addVectors(start, end).multiplyScalar(0.5);

      // 计算段长度
      const length = start.distanceTo(end);

      // 计算段方向
      const direction = new THREE.Vector3().subVectors(end, start).normalize();

      // 创建体积雾
      const volume = this.createCaveFog(
        midPoint,
        new THREE.Vector3(width, height, length),
        color,
        density * (0.8 + Math.random() * 0.4) // 添加一些随机变化
      );

      // 计算旋转
      const quaternion = new THREE.Quaternion().setFromUnitVectors(
        new THREE.Vector3(0, 0, 1), // 默认朝向
        direction
      );

      // 应用旋转
      const euler = new THREE.Euler().setFromQuaternion(quaternion);
      (volume as any).rotation = euler;

      volumes.push(volume);
    }

    return volumes;
  }

  /**
   * 渲染
   * @param delta 时间增量
   */
  public render(delta: number): void {
    if (!this.initialized) return;

    // 开始性能监视
    this.performanceMonitor.beginMeasure('volumetricFogRender');

    // 更新时间
    this.time += delta;
    this.fogPass.uniforms['time'].value = this.time;

    // 更新相机相关的uniform
    this.fogPass.uniforms['projectionMatrixInverse'].value = this.camera.projectionMatrixInverse;
    this.fogPass.uniforms['viewMatrixInverse'].value = this.camera.matrixWorld;
    this.fogPass.uniforms['cameraPosition'].value = this.camera.position;

    // 自适应质量
    if (this.params.enableAdaptiveQuality) {
      this.adaptQuality();
    }

    // 渲染
    this.composer.render();

    // 结束性能监视
    this.performanceMonitor.endMeasure('volumetricFogRender');
  }

  /**
   * 自适应质量
   */
  private adaptQuality(): void {
    // 获取FPS指标来判断性能
    const fpsMetric = this.performanceMonitor.getMetric(PerformanceMetricType.FPS);
    const currentFPS = fpsMetric ? fpsMetric.value : 60;

    // 如果FPS过低，降低质量
    if (currentFPS < 30) {
      this.params.maxSteps = Math.max(10, this.params.maxSteps - 5);
      this.fogPass.uniforms['maxSteps'].value = this.params.maxSteps;
    }
    // 如果FPS很高，提高质量
    else if (currentFPS > 55) {
      this.params.maxSteps = Math.min(200, this.params.maxSteps + 1);
      this.fogPass.uniforms['maxSteps'].value = this.params.maxSteps;
    }
  }

  /**
   * 调整大小
   * @param width 宽度
   * @param height 高度
   */
  public resize(width: number, height: number): void {
    if (!this.initialized) return;
    this.composer.setSize(width, height);
  }

  /**
   * 销毁
   */
  public dispose(): void {
    if (!this.initialized) return;

    // 清理效果合成器
    if (this.composer) {
      // 移除所有通道
      while (this.composer.passes.length > 0) {
        this.composer.removePass(this.composer.passes[0]);
      }

      // 清理渲染目标
      const composerAny = this.composer as any;
      if (composerAny.renderTarget1) {
        composerAny.(renderTarget1 as any).dispose();
      }
      if (composerAny.renderTarget2) {
        composerAny.(renderTarget2 as any).dispose();
      }
      if (composerAny.writeBuffer) {
        composerAny.(writeBuffer as any).dispose();
      }
      if (composerAny.readBuffer) {
        composerAny.(readBuffer as any).dispose();
      }
    }

    // 清理着色器通道
    if (this.fogPass) {
      const passAny = this.fogPass as any;
      if (passAny.material) {
        passAny.(material as any).dispose();
      }
    }

    // 清理雾体积
    this.fogVolumes = [];

    this.initialized = false;
  }
}
