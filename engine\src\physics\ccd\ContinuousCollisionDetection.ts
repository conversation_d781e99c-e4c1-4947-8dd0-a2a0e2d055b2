/**
 * 连续碰撞检测 (CCD) 实现
 * 基于Cannon.js的连续碰撞检测实现，用于解决高速移动物体的穿透问题
 */
import * as THREE from 'three';
import * as CANNON from 'cannon-es';
import type { Entity } from '../../core/Entity';
import type { PhysicsBody } from '../PhysicsBody';

/**
 * 连续碰撞检测选项
 */
export interface CCDOptions {
  /** 最大子步数 */
  maxSubSteps?: number;
  /** 最小子步时间 */
  minSubStepTime?: number;
  /** 速度阈值 - 超过此值启用CCD */
  velocityThreshold?: number;
  /** 是否对所有物体启用CCD */
  enableForAll?: boolean;
}

/**
 * 连续碰撞检测
 */
export class ContinuousCollisionDetection {
  /** 物理世界 */
  private world: CANNON.World;

  /** 最大子步数 */
  private maxSubSteps: number;

  /** 最小子步时间 */
  private minSubStepTime: number;

  /** 速度阈值 - 超过此值启用CCD */
  private velocityThreshold: number;

  /** 是否对所有物体启用CCD */
  private enableForAll: boolean;

  /** 启用CCD的物体 */
  private enabledBodies: Set<CANNON.Body> = new Set();

  /** 物体的上一个位置 */
  private previousPositions: Map<CANNON.Body, CANNON.Vec3> = new Map();

  /** 物体的上一个旋转 */
  private previousQuaternions: Map<CANNON.Body, CANNON.Quaternion> = new Map();

  /**
   * 创建连续碰撞检测
   * @param world 物理世界
   * @param options 选项
   */
  constructor(world: CANNON.World, options: CCDOptions = {}) {
    this.world = world;
    this.maxSubSteps = options.maxSubSteps || 5;
    this.minSubStepTime = options.minSubStepTime || 1 / 240;
    this.velocityThreshold = options.velocityThreshold || 5;
    this.enableForAll = options.enableForAll || false;

    // 保存所有物体的初始位置和旋转
    this.updatePreviousStates();
  }

  /**
   * 更新所有物体的上一个状态
   */
  private updatePreviousStates(): void {
    for (const body of this.world.bodies) {
      this.previousPositions.set(body, body.position.clone());
      this.previousQuaternions.set(body, body.quaternion.clone());
    }
  }

  /**
   * 启用物体的CCD
   * @param body 物理体
   */
  public enableBodyCCD(body: CANNON.Body): void {
    this.enabledBodies.add(body);
  }

  /**
   * 禁用物体的CCD
   * @param body 物理体
   */
  public disableBodyCCD(body: CANNON.Body): void {
    this.enabledBodies.delete(body);
  }

  /**
   * 检查物体是否启用CCD
   * @param body 物理体
   * @returns 是否启用CCD
   */
  public isBodyCCDEnabled(body: CANNON.Body): boolean {
    return this.enabledBodies.has(body);
  }

  /**
   * 启用实体的CCD
   * @param entity 实体
   */
  public enableEntityCCD(entity: Entity): void {
    const physicsBody = entity.getComponent<PhysicsBody>('PhysicsBody');
    if (physicsBody) {
      const cannonBody = physicsBody.getCannonBody();
      if (cannonBody) {
        this.enableBodyCCD(cannonBody);
      }
    }
  }

  /**
   * 禁用实体的CCD
   * @param entity 实体
   */
  public disableEntityCCD(entity: Entity): void {
    const physicsBody = entity.getComponent<PhysicsBody>('PhysicsBody');
    if (physicsBody) {
      const cannonBody = physicsBody.getCannonBody();
      if (cannonBody) {
        this.disableBodyCCD(cannonBody);
      }
    }
  }

  /**
   * 检查实体是否启用CCD
   * @param entity 实体
   * @returns 是否启用CCD
   */
  public isEntityCCDEnabled(entity: Entity): boolean {
    const physicsBody = entity.getComponent<PhysicsBody>('PhysicsBody');
    if (physicsBody) {
      const cannonBody = physicsBody.getCannonBody();
      if (cannonBody) {
        return this.isBodyCCDEnabled(cannonBody);
      }
    }
    return false;
  }

  /**
   * 更新连续碰撞检测
   * @param deltaTime 时间步长
   */
  public update(deltaTime: number): void {
    // 检查是否需要进行子步进
    const needsSubStepping = this.checkNeedsSubStepping();

    if (needsSubStepping) {
      // 计算子步数
      const numSubSteps = Math.min(Math.ceil(deltaTime / this.minSubStepTime), this.maxSubSteps);
      const subStepTime = deltaTime / numSubSteps;

      // 保存当前状态
      const originalPositions = new Map<CANNON.Body, CANNON.Vec3>();
      const originalQuaternions = new Map<CANNON.Body, CANNON.Quaternion>();

      for (const body of this.world.bodies) {
        originalPositions.set(body, body.position.clone());
        originalQuaternions.set(body, body.quaternion.clone());
      }

      // 执行子步进
      for (let i = 0; i < numSubSteps; i++) {
        // 更新物体位置
        this.updateBodyPositions(subStepTime, i, numSubSteps, originalPositions, originalQuaternions);

        // 执行物理步进
        this.world.step(subStepTime);

        // 更新上一个状态
        this.updatePreviousStates();
      }
    } else {
      // 正常步进
      this.world.step(deltaTime);

      // 更新上一个状态
      this.updatePreviousStates();
    }
  }

  /**
   * 检查是否需要进行子步进
   * @returns 是否需要子步进
   */
  private checkNeedsSubStepping(): boolean {
    // 如果对所有物体启用CCD，则始终进行子步进
    if (this.enableForAll) {
      return true;
    }

    // 检查是否有启用CCD的物体速度超过阈值
    for (const body of this.enabledBodies) {
      const velocity = body.velocity.length();
      if (velocity > this.velocityThreshold) {
        return true;
      }
    }

    return false;
  }

  /**
   * 更新物体位置
   * @param subStepTime 子步时间
   * @param currentStep 当前子步
   * @param totalSteps 总子步数
   * @param originalPositions 原始位置
   * @param originalQuaternions 原始旋转
   */
  private updateBodyPositions(
    subStepTime: number,
    currentStep: number,
    totalSteps: number,
    originalPositions: Map<CANNON.Body, CANNON.Vec3>,
    originalQuaternions: Map<CANNON.Body, CANNON.Quaternion>
  ): void {
    const t = (currentStep + 1) / totalSteps;

    for (const body of this.world.bodies) {
      if (body.type === CANNON.Body.DYNAMIC) {
        const originalPosition = originalPositions.get(body);
        const originalQuaternion = originalQuaternions.get(body);

        if (originalPosition && originalQuaternion) {
          // 线性插值位置和旋转
          (body as any).getPosition().x = originalPosition.x + (body.velocity.x * subStepTime * (currentStep + 1));
          (body as any).getPosition().y = originalPosition.y + (body.velocity.y * subStepTime * (currentStep + 1));
          (body as any).getPosition().z = originalPosition.z + (body.velocity.z * subStepTime * (currentStep + 1));

          // 简单的角速度积分
          const angularDisplacement = new CANNON.Vec3();
          angularDisplacement.copy(body.angularVelocity);
          angularDisplacement.scale(subStepTime * (currentStep + 1), angularDisplacement);

          // 创建旋转四元数
          const rotationQuat = new CANNON.Quaternion();
          const angularMagnitude = angularDisplacement.length();
          if (angularMagnitude > 0) {
            const axis = angularDisplacement.clone();
            axis.scale(1 / angularMagnitude, axis);
            rotationQuat.setFromAxisAngle(axis, angularMagnitude);

            // 应用旋转 - 正确的参数顺序
            body.quaternion = body.quaternion.mult(rotationQuat);
          }
        }
      }
    }
  }
}
