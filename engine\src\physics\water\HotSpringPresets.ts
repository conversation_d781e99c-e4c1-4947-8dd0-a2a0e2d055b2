/**
 * 温泉预设
 * 提供各种类型的温泉预设配置
 */
import * as THREE from 'three';
import { type Entity  } from '../../core/Entity';
import type { World } from '../../core/World';
import { HotSpringComponent, HotSpringConfig, HotSpringType } from './HotSpringComponent';
import { Debug } from '../../utils/Debug';

/**
 * 温泉预设配置
 */
export interface HotSpringPresetConfig {
  /** 预设类型 */
  type: HotSpringType;
  /** 温泉宽度 */
  width?: number;
  /** 温泉高度 */
  height?: number;
  /** 温泉深度 */
  depth?: number;
  /** 温泉位置 */
  position?: THREE.Vector3;
  /** 温泉旋转 */
  rotation?: THREE.Euler;
  /** 温泉颜色 */
  color?: THREE.Color;
  /** 温泉不透明度 */
  opacity?: number;
  /** 温泉温度 */
  temperature?: number;
  /** 温泉波动强度 */
  waveAmplitude?: number;
  /** 温泉波动频率 */
  waveFrequency?: number;
  /** 温泉波动速度 */
  waveSpeed?: number;
  /** 是否启用气泡效果 */
  enableBubbleEffect?: boolean;
  /** 气泡效果强度 */
  bubbleEffectStrength?: number;
  /** 气泡大小范围 */
  bubbleSizeRange?: [number, number];
  /** 气泡速度范围 */
  bubbleSpeedRange?: [number, number];
  /** 气泡密度 */
  bubbleDensity?: number;
  /** 气泡分布范围 */
  bubbleDistributionRadius?: number;
  /** 是否启用气泡爆裂效果 */
  enableBubbleBurstEffect?: boolean;
  /** 气泡爆裂效果强度 */
  bubbleBurstEffectStrength?: number;
  /** 是否启用水蒸气效果 */
  enableSteamEffect?: boolean;
  /** 水蒸气效果强度 */
  steamEffectStrength?: number;
  /** 水蒸气颜色 */
  steamColor?: THREE.Color;
  /** 水蒸气密度 */
  steamDensity?: number;
  /** 水蒸气大小范围 */
  steamSizeRange?: [number, number];
  /** 水蒸气速度范围 */
  steamSpeedRange?: [number, number];
  /** 水蒸气上升高度 */
  steamRiseHeight?: number;
  /** 是否启用声音效果 */
  enableSoundEffect?: boolean;
  /** 声音效果音量 */
  soundEffectVolume?: number;
  /** 是否启用热扩散效果 */
  enableHeatDiffusion?: boolean;
  /** 热扩散范围 */
  heatDiffusionRange?: number;
  /** 是否启用矿物质效果 */
  enableMineralEffect?: boolean;
  /** 矿物质颜色 */
  mineralColor?: THREE.Color;
}

/**
 * 温泉预设
 */
export class HotSpringPresets {
  /**
   * 创建温泉预设
   * @param world 世界
   * @param config 预设配置
   * @returns 温泉实体
   */
  public static createPreset(world: World, config: HotSpringPresetConfig): Entity {
    // 创建温泉实体
    const entity = new Entity(`hotspring_${config.type}`);

    // 创建温泉配置
    const hotSpringConfig: HotSpringConfig = {
      // 设置温泉类型
      hotSpringType: config.type
    };

    // 应用基本配置
    if (config.width !== undefined) hotSpringConfig.width = config.width;
    if (config.height !== undefined) hotSpringConfig.height = config.height;
    if (config.depth !== undefined) hotSpringConfig.depth = config.depth;
    if (config.position) hotSpringConfig.position = config.position;
    if (config.rotation) hotSpringConfig.rotation = config.rotation;
    if (config.color) hotSpringConfig.color = config.color;
    if (config.opacity !== undefined) hotSpringConfig.opacity = config.opacity;
    if (config.temperature !== undefined) hotSpringConfig.temperature = config.temperature;
    if (config.waveAmplitude !== undefined) hotSpringConfig.waveAmplitude = config.waveAmplitude;
    if (config.waveFrequency !== undefined) hotSpringConfig.waveFrequency = config.waveFrequency;
    if (config.waveSpeed !== undefined) hotSpringConfig.waveSpeed = config.waveSpeed;

    // 应用气泡效果配置
    if (config.enableBubbleEffect !== undefined) hotSpringConfig.enableBubbleEffect = config.enableBubbleEffect;
    if (config.bubbleEffectStrength !== undefined) hotSpringConfig.bubbleEffectStrength = config.bubbleEffectStrength;
    if (config.bubbleSizeRange !== undefined) hotSpringConfig.bubbleSizeRange = config.bubbleSizeRange;
    if (config.bubbleSpeedRange !== undefined) hotSpringConfig.bubbleSpeedRange = config.bubbleSpeedRange;
    if (config.bubbleDensity !== undefined) hotSpringConfig.bubbleDensity = config.bubbleDensity;
    if (config.bubbleDistributionRadius !== undefined) hotSpringConfig.bubbleDistributionRadius = config.bubbleDistributionRadius;

    // 应用气泡爆裂效果配置
    if (config.enableBubbleBurstEffect !== undefined) hotSpringConfig.enableBubbleBurstEffect = config.enableBubbleBurstEffect;
    if (config.bubbleBurstEffectStrength !== undefined) hotSpringConfig.bubbleBurstEffectStrength = config.bubbleBurstEffectStrength;

    // 应用水蒸气效果配置
    if (config.enableSteamEffect !== undefined) hotSpringConfig.enableSteamEffect = config.enableSteamEffect;
    if (config.steamEffectStrength !== undefined) hotSpringConfig.steamEffectStrength = config.steamEffectStrength;
    if (config.steamColor) hotSpringConfig.steamColor = config.steamColor;
    if (config.steamDensity !== undefined) hotSpringConfig.steamDensity = config.steamDensity;
    if (config.steamSizeRange !== undefined) hotSpringConfig.steamSizeRange = config.steamSizeRange;
    if (config.steamSpeedRange !== undefined) hotSpringConfig.steamSpeedRange = config.steamSpeedRange;
    if (config.steamRiseHeight !== undefined) hotSpringConfig.steamRiseHeight = config.steamRiseHeight;

    // 应用其他效果配置
    if (config.enableSoundEffect !== undefined) hotSpringConfig.enableSoundEffect = config.enableSoundEffect;
    if (config.soundEffectVolume !== undefined) hotSpringConfig.soundEffectVolume = config.soundEffectVolume;
    if (config.enableHeatDiffusion !== undefined) hotSpringConfig.enableHeatDiffusion = config.enableHeatDiffusion;
    if (config.heatDiffusionRange !== undefined) hotSpringConfig.heatDiffusionRange = config.heatDiffusionRange;
    if (config.enableMineralEffect !== undefined) hotSpringConfig.enableMineralEffect = config.enableMineralEffect;
    if (config.mineralColor) hotSpringConfig.mineralColor = config.mineralColor;

    // 根据预设类型应用特定配置
    switch (config.type) {
      case HotSpringType.STANDARD:
        this.applyStandardPreset(hotSpringConfig);
        break;
      case HotSpringType.HIGH_TEMPERATURE:
        this.applyHighTemperaturePreset(hotSpringConfig);
        break;
      case HotSpringType.LOW_TEMPERATURE:
        this.applyLowTemperaturePreset(hotSpringConfig);
        break;
      case HotSpringType.LARGE:
        this.applyLargePreset(hotSpringConfig);
        break;
      case HotSpringType.SMALL:
        this.applySmallPreset(hotSpringConfig);
        break;
      case HotSpringType.SULFUR:
        this.applySulfurPreset(hotSpringConfig);
        break;
      case HotSpringType.MINERAL:
        this.applyMineralPreset(hotSpringConfig);
        break;
      case HotSpringType.UNDERGROUND:
        this.applyUndergroundPreset(hotSpringConfig);
        break;
      default:
        this.applyStandardPreset(hotSpringConfig);
        break;
    }

    // 创建温泉组件
    new HotSpringComponent(entity, hotSpringConfig);

    // 添加到世界
    world.addEntity(entity);

    Debug.log('HotSpringPresets', `创建温泉预设: ${config.type}`);

    return entity;
  }

  /**
   * 应用标准温泉预设
   * @param config 温泉配置
   */
  private static applyStandardPreset(config: HotSpringConfig): void {
    // 设置默认尺寸
    if (!config.width) config.width = 10;
    if (!config.height) config.height = 1;
    if (!config.depth) config.depth = 10;

    // 设置默认颜色和不透明度
    if (!config.color) config.color = new THREE.Color(0x88ccff);
    if (config.opacity === undefined) config.opacity = 0.8;

    // 设置默认温度和波动参数
    if (config.temperature === undefined) config.temperature = 60;
    if (config.waveAmplitude === undefined) config.waveAmplitude = 0.1;
    if (config.waveFrequency === undefined) config.waveFrequency = 2.0;
    if (config.waveSpeed === undefined) config.waveSpeed = 0.5;

    // 设置默认气泡效果
    if (config.enableBubbleEffect === undefined) config.enableBubbleEffect = true;
    if (config.bubbleEffectStrength === undefined) config.bubbleEffectStrength = 1.0;
    if (config.bubbleSizeRange === undefined) config.bubbleSizeRange = [0.05, 0.2];
    if (config.bubbleSpeedRange === undefined) config.bubbleSpeedRange = [0.1, 0.3];
    if (config.bubbleDensity === undefined) config.bubbleDensity = 1.0;
    if (config.bubbleDistributionRadius === undefined) config.bubbleDistributionRadius = 0.8;

    // 设置默认气泡爆裂效果
    if (config.enableBubbleBurstEffect === undefined) config.enableBubbleBurstEffect = true;
    if (config.bubbleBurstEffectStrength === undefined) config.bubbleBurstEffectStrength = 1.0;

    // 设置默认水蒸气效果
    if (config.enableSteamEffect === undefined) config.enableSteamEffect = true;
    if (config.steamEffectStrength === undefined) config.steamEffectStrength = 1.0;
    if (!config.steamColor) config.steamColor = new THREE.Color(0xffffff);
    if (config.steamDensity === undefined) config.steamDensity = 1.0;
    if (config.steamSizeRange === undefined) config.steamSizeRange = [0.5, 1.5];
    if (config.steamSpeedRange === undefined) config.steamSpeedRange = [0.05, 0.1];
    if (config.steamRiseHeight === undefined) config.steamRiseHeight = 2.0;

    // 设置默认声音效果
    if (config.enableSoundEffect === undefined) config.enableSoundEffect = true;
    if (config.soundEffectVolume === undefined) config.soundEffectVolume = 1.0;

    // 设置默认热扩散效果
    if (config.enableHeatDiffusion === undefined) config.enableHeatDiffusion = true;
    if (config.heatDiffusionRange === undefined) config.heatDiffusionRange = 5.0;

    // 设置默认矿物质效果
    if (config.enableMineralEffect === undefined) config.enableMineralEffect = true;
    if (!config.mineralColor) config.mineralColor = new THREE.Color(0xc0a080);
  }

  /**
   * 应用高温温泉预设
   * @param config 温泉配置
   */
  private static applyHighTemperaturePreset(config: HotSpringConfig): void {
    // 设置默认尺寸
    if (!config.width) config.width = 8;
    if (!config.height) config.height = 1;
    if (!config.depth) config.depth = 8;

    // 设置默认颜色和不透明度
    if (!config.color) config.color = new THREE.Color(0x99ccff);
    if (config.opacity === undefined) config.opacity = 0.7;

    // 设置默认温度和波动参数
    if (config.temperature === undefined) config.temperature = 90;
    if (config.waveAmplitude === undefined) config.waveAmplitude = 0.15;
    if (config.waveFrequency === undefined) config.waveFrequency = 2.5;
    if (config.waveSpeed === undefined) config.waveSpeed = 0.7;

    // 设置默认气泡效果
    if (config.enableBubbleEffect === undefined) config.enableBubbleEffect = true;
    if (config.bubbleEffectStrength === undefined) config.bubbleEffectStrength = 1.5;
    if (config.bubbleSizeRange === undefined) config.bubbleSizeRange = [0.08, 0.25];
    if (config.bubbleSpeedRange === undefined) config.bubbleSpeedRange = [0.2, 0.5];
    if (config.bubbleDensity === undefined) config.bubbleDensity = 1.5;
    if (config.bubbleDistributionRadius === undefined) config.bubbleDistributionRadius = 0.9;

    // 设置默认气泡爆裂效果
    if (config.enableBubbleBurstEffect === undefined) config.enableBubbleBurstEffect = true;
    if (config.bubbleBurstEffectStrength === undefined) config.bubbleBurstEffectStrength = 1.5;

    // 设置默认水蒸气效果
    if (config.enableSteamEffect === undefined) config.enableSteamEffect = true;
    if (config.steamEffectStrength === undefined) config.steamEffectStrength = 1.8;
    if (!config.steamColor) config.steamColor = new THREE.Color(0xffffff);
    if (config.steamDensity === undefined) config.steamDensity = 1.5;
    if (config.steamSizeRange === undefined) config.steamSizeRange = [0.8, 2.5];
    if (config.steamSpeedRange === undefined) config.steamSpeedRange = [0.08, 0.15];
    if (config.steamRiseHeight === undefined) config.steamRiseHeight = 3.0;

    // 设置默认声音效果
    if (config.enableSoundEffect === undefined) config.enableSoundEffect = true;
    if (config.soundEffectVolume === undefined) config.soundEffectVolume = 1.2;

    // 设置默认热扩散效果
    if (config.enableHeatDiffusion === undefined) config.enableHeatDiffusion = true;
    if (config.heatDiffusionRange === undefined) config.heatDiffusionRange = 7.0;

    // 设置默认矿物质效果
    if (config.enableMineralEffect === undefined) config.enableMineralEffect = true;
    if (!config.mineralColor) config.mineralColor = new THREE.Color(0xd0b090);
  }

  /**
   * 应用低温温泉预设
   * @param config 温泉配置
   */
  private static applyLowTemperaturePreset(config: HotSpringConfig): void {
    // 设置默认尺寸
    if (!config.width) config.width = 12;
    if (!config.height) config.height = 1;
    if (!config.depth) config.depth = 12;

    // 设置默认颜色和不透明度
    if (!config.color) config.color = new THREE.Color(0xaaddff);
    if (config.opacity === undefined) config.opacity = 0.9;

    // 设置默认温度和波动参数
    if (config.temperature === undefined) config.temperature = 40;
    if (config.waveAmplitude === undefined) config.waveAmplitude = 0.05;
    if (config.waveFrequency === undefined) config.waveFrequency = 1.5;
    if (config.waveSpeed === undefined) config.waveSpeed = 0.3;

    // 设置默认气泡效果
    if (config.enableBubbleEffect === undefined) config.enableBubbleEffect = true;
    if (config.bubbleEffectStrength === undefined) config.bubbleEffectStrength = 0.6;
    if (config.bubbleSizeRange === undefined) config.bubbleSizeRange = [0.03, 0.15];
    if (config.bubbleSpeedRange === undefined) config.bubbleSpeedRange = [0.05, 0.2];
    if (config.bubbleDensity === undefined) config.bubbleDensity = 0.7;
    if (config.bubbleDistributionRadius === undefined) config.bubbleDistributionRadius = 0.7;

    // 设置默认气泡爆裂效果
    if (config.enableBubbleBurstEffect === undefined) config.enableBubbleBurstEffect = true;
    if (config.bubbleBurstEffectStrength === undefined) config.bubbleBurstEffectStrength = 0.5;

    // 设置默认水蒸气效果
    if (config.enableSteamEffect === undefined) config.enableSteamEffect = true;
    if (config.steamEffectStrength === undefined) config.steamEffectStrength = 0.4;
    if (!config.steamColor) config.steamColor = new THREE.Color(0xeeffff);
    if (config.steamDensity === undefined) config.steamDensity = 0.6;
    if (config.steamSizeRange === undefined) config.steamSizeRange = [0.3, 1.2];
    if (config.steamSpeedRange === undefined) config.steamSpeedRange = [0.03, 0.08];
    if (config.steamRiseHeight === undefined) config.steamRiseHeight = 1.5;

    // 设置默认声音效果
    if (config.enableSoundEffect === undefined) config.enableSoundEffect = true;
    if (config.soundEffectVolume === undefined) config.soundEffectVolume = 0.8;

    // 设置默认热扩散效果
    if (config.enableHeatDiffusion === undefined) config.enableHeatDiffusion = true;
    if (config.heatDiffusionRange === undefined) config.heatDiffusionRange = 4.0;

    // 设置默认矿物质效果
    if (config.enableMineralEffect === undefined) config.enableMineralEffect = true;
    if (!config.mineralColor) config.mineralColor = new THREE.Color(0xb09070);
  }

  /**
   * 应用大型温泉预设
   * @param config 温泉配置
   */
  private static applyLargePreset(config: HotSpringConfig): void {
    // 设置默认尺寸
    if (!config.width) config.width = 20;
    if (!config.height) config.height = 1.5;
    if (!config.depth) config.depth = 20;

    // 设置默认颜色和不透明度
    if (!config.color) config.color = new THREE.Color(0x88ccff);
    if (config.opacity === undefined) config.opacity = 0.8;

    // 设置默认温度和波动参数
    if (config.temperature === undefined) config.temperature = 55;
    if (config.waveAmplitude === undefined) config.waveAmplitude = 0.12;
    if (config.waveFrequency === undefined) config.waveFrequency = 1.8;
    if (config.waveSpeed === undefined) config.waveSpeed = 0.4;

    // 设置默认气泡效果
    if (config.enableBubbleEffect === undefined) config.enableBubbleEffect = true;
    if (config.bubbleEffectStrength === undefined) config.bubbleEffectStrength = 1.2;
    if (config.bubbleSizeRange === undefined) config.bubbleSizeRange = [0.05, 0.22];
    if (config.bubbleSpeedRange === undefined) config.bubbleSpeedRange = [0.1, 0.3];
    if (config.bubbleDensity === undefined) config.bubbleDensity = 1.2;
    if (config.bubbleDistributionRadius === undefined) config.bubbleDistributionRadius = 0.85;

    // 设置默认气泡爆裂效果
    if (config.enableBubbleBurstEffect === undefined) config.enableBubbleBurstEffect = true;
    if (config.bubbleBurstEffectStrength === undefined) config.bubbleBurstEffectStrength = 1.1;

    // 设置默认水蒸气效果
    if (config.enableSteamEffect === undefined) config.enableSteamEffect = true;
    if (config.steamEffectStrength === undefined) config.steamEffectStrength = 1.1;
    if (!config.steamColor) config.steamColor = new THREE.Color(0xffffff);
    if (config.steamDensity === undefined) config.steamDensity = 1.1;
    if (config.steamSizeRange === undefined) config.steamSizeRange = [0.6, 2.0];
    if (config.steamSpeedRange === undefined) config.steamSpeedRange = [0.05, 0.12];
    if (config.steamRiseHeight === undefined) config.steamRiseHeight = 2.5;

    // 设置默认声音效果
    if (config.enableSoundEffect === undefined) config.enableSoundEffect = true;
    if (config.soundEffectVolume === undefined) config.soundEffectVolume = 1.2;

    // 设置默认热扩散效果
    if (config.enableHeatDiffusion === undefined) config.enableHeatDiffusion = true;
    if (config.heatDiffusionRange === undefined) config.heatDiffusionRange = 8.0;

    // 设置默认矿物质效果
    if (config.enableMineralEffect === undefined) config.enableMineralEffect = true;
    if (!config.mineralColor) config.mineralColor = new THREE.Color(0xc0a080);
  }

  /**
   * 应用小型温泉预设
   * @param config 温泉配置
   */
  private static applySmallPreset(config: HotSpringConfig): void {
    // 设置默认尺寸
    if (!config.width) config.width = 5;
    if (!config.height) config.height = 0.8;
    if (!config.depth) config.depth = 5;

    // 设置默认颜色和不透明度
    if (!config.color) config.color = new THREE.Color(0x99ccff);
    if (config.opacity === undefined) config.opacity = 0.85;

    // 设置默认温度和波动参数
    if (config.temperature === undefined) config.temperature = 65;
    if (config.waveAmplitude === undefined) config.waveAmplitude = 0.08;
    if (config.waveFrequency === undefined) config.waveFrequency = 2.2;
    if (config.waveSpeed === undefined) config.waveSpeed = 0.6;

    // 设置默认气泡效果
    if (config.enableBubbleEffect === undefined) config.enableBubbleEffect = true;
    if (config.bubbleEffectStrength === undefined) config.bubbleEffectStrength = 0.8;
    if (config.bubbleSizeRange === undefined) config.bubbleSizeRange = [0.03, 0.15];
    if (config.bubbleSpeedRange === undefined) config.bubbleSpeedRange = [0.08, 0.25];
    if (config.bubbleDensity === undefined) config.bubbleDensity = 0.8;
    if (config.bubbleDistributionRadius === undefined) config.bubbleDistributionRadius = 0.7;

    // 设置默认气泡爆裂效果
    if (config.enableBubbleBurstEffect === undefined) config.enableBubbleBurstEffect = true;
    if (config.bubbleBurstEffectStrength === undefined) config.bubbleBurstEffectStrength = 0.7;

    // 设置默认水蒸气效果
    if (config.enableSteamEffect === undefined) config.enableSteamEffect = true;
    if (config.steamEffectStrength === undefined) config.steamEffectStrength = 0.9;
    if (!config.steamColor) config.steamColor = new THREE.Color(0xffffff);
    if (config.steamDensity === undefined) config.steamDensity = 0.9;
    if (config.steamSizeRange === undefined) config.steamSizeRange = [0.4, 1.3];
    if (config.steamSpeedRange === undefined) config.steamSpeedRange = [0.04, 0.1];
    if (config.steamRiseHeight === undefined) config.steamRiseHeight = 1.7;

    // 设置默认声音效果
    if (config.enableSoundEffect === undefined) config.enableSoundEffect = true;
    if (config.soundEffectVolume === undefined) config.soundEffectVolume = 0.9;

    // 设置默认热扩散效果
    if (config.enableHeatDiffusion === undefined) config.enableHeatDiffusion = true;
    if (config.heatDiffusionRange === undefined) config.heatDiffusionRange = 3.0;

    // 设置默认矿物质效果
    if (config.enableMineralEffect === undefined) config.enableMineralEffect = true;
    if (!config.mineralColor) config.mineralColor = new THREE.Color(0xb09070);
  }

  /**
   * 应用硫磺温泉预设
   * @param config 温泉配置
   */
  private static applySulfurPreset(config: HotSpringConfig): void {
    // 设置默认尺寸
    if (!config.width) config.width = 8;
    if (!config.height) config.height = 1;
    if (!config.depth) config.depth = 8;

    // 设置默认颜色和不透明度
    if (!config.color) config.color = new THREE.Color(0xd0d080);
    if (config.opacity === undefined) config.opacity = 0.75;

    // 设置默认温度和波动参数
    if (config.temperature === undefined) config.temperature = 75;
    if (config.waveAmplitude === undefined) config.waveAmplitude = 0.12;
    if (config.waveFrequency === undefined) config.waveFrequency = 2.0;
    if (config.waveSpeed === undefined) config.waveSpeed = 0.5;

    // 设置默认气泡效果
    if (config.enableBubbleEffect === undefined) config.enableBubbleEffect = true;
    if (config.bubbleEffectStrength === undefined) config.bubbleEffectStrength = 1.3;
    if (config.bubbleSizeRange === undefined) config.bubbleSizeRange = [0.07, 0.24];
    if (config.bubbleSpeedRange === undefined) config.bubbleSpeedRange = [0.15, 0.4];
    if (config.bubbleDensity === undefined) config.bubbleDensity = 1.3;
    if (config.bubbleDistributionRadius === undefined) config.bubbleDistributionRadius = 0.85;

    // 设置默认气泡爆裂效果
    if (config.enableBubbleBurstEffect === undefined) config.enableBubbleBurstEffect = true;
    if (config.bubbleBurstEffectStrength === undefined) config.bubbleBurstEffectStrength = 1.3;

    // 设置默认水蒸气效果
    if (config.enableSteamEffect === undefined) config.enableSteamEffect = true;
    if (config.steamEffectStrength === undefined) config.steamEffectStrength = 1.5;
    if (!config.steamColor) config.steamColor = new THREE.Color(0xffffdd);
    if (config.steamDensity === undefined) config.steamDensity = 1.4;
    if (config.steamSizeRange === undefined) config.steamSizeRange = [0.7, 2.2];
    if (config.steamSpeedRange === undefined) config.steamSpeedRange = [0.07, 0.14];
    if (config.steamRiseHeight === undefined) config.steamRiseHeight = 2.8;

    // 设置默认声音效果
    if (config.enableSoundEffect === undefined) config.enableSoundEffect = true;
    if (config.soundEffectVolume === undefined) config.soundEffectVolume = 1.1;

    // 设置默认热扩散效果
    if (config.enableHeatDiffusion === undefined) config.enableHeatDiffusion = true;
    if (config.heatDiffusionRange === undefined) config.heatDiffusionRange = 6.0;

    // 设置默认矿物质效果
    if (config.enableMineralEffect === undefined) config.enableMineralEffect = true;
    if (!config.mineralColor) config.mineralColor = new THREE.Color(0xd0d060);
  }

  /**
   * 应用矿物质温泉预设
   * @param config 温泉配置
   */
  private static applyMineralPreset(config: HotSpringConfig): void {
    // 设置默认尺寸
    if (!config.width) config.width = 10;
    if (!config.height) config.height = 1;
    if (!config.depth) config.depth = 10;

    // 设置默认颜色和不透明度
    if (!config.color) config.color = new THREE.Color(0xa0c0d0);
    if (config.opacity === undefined) config.opacity = 0.7;

    // 设置默认温度和波动参数
    if (config.temperature === undefined) config.temperature = 55;
    if (config.waveAmplitude === undefined) config.waveAmplitude = 0.1;
    if (config.waveFrequency === undefined) config.waveFrequency = 1.8;
    if (config.waveSpeed === undefined) config.waveSpeed = 0.4;

    // 设置默认气泡效果
    if (config.enableBubbleEffect === undefined) config.enableBubbleEffect = true;
    if (config.bubbleEffectStrength === undefined) config.bubbleEffectStrength = 0.9;
    if (config.bubbleSizeRange === undefined) config.bubbleSizeRange = [0.04, 0.18];
    if (config.bubbleSpeedRange === undefined) config.bubbleSpeedRange = [0.1, 0.25];
    if (config.bubbleDensity === undefined) config.bubbleDensity = 0.9;
    if (config.bubbleDistributionRadius === undefined) config.bubbleDistributionRadius = 0.75;

    // 设置默认气泡爆裂效果
    if (config.enableBubbleBurstEffect === undefined) config.enableBubbleBurstEffect = true;
    if (config.bubbleBurstEffectStrength === undefined) config.bubbleBurstEffectStrength = 0.8;

    // 设置默认水蒸气效果
    if (config.enableSteamEffect === undefined) config.enableSteamEffect = true;
    if (config.steamEffectStrength === undefined) config.steamEffectStrength = 0.8;
    if (!config.steamColor) config.steamColor = new THREE.Color(0xccddff);
    if (config.steamDensity === undefined) config.steamDensity = 0.8;
    if (config.steamSizeRange === undefined) config.steamSizeRange = [0.4, 1.4];
    if (config.steamSpeedRange === undefined) config.steamSpeedRange = [0.04, 0.09];
    if (config.steamRiseHeight === undefined) config.steamRiseHeight = 1.8;

    // 设置默认声音效果
    if (config.enableSoundEffect === undefined) config.enableSoundEffect = true;
    if (config.soundEffectVolume === undefined) config.soundEffectVolume = 0.9;

    // 设置默认热扩散效果
    if (config.enableHeatDiffusion === undefined) config.enableHeatDiffusion = true;
    if (config.heatDiffusionRange === undefined) config.heatDiffusionRange = 5.0;

    // 设置默认矿物质效果
    if (config.enableMineralEffect === undefined) config.enableMineralEffect = true;
    if (!config.mineralColor) config.mineralColor = new THREE.Color(0x90b0c0);
  }

  /**
   * 应用地下温泉预设
   * @param config 温泉配置
   */
  private static applyUndergroundPreset(config: HotSpringConfig): void {
    // 设置默认尺寸
    if (!config.width) config.width = 12;
    if (!config.height) config.height = 1.2;
    if (!config.depth) config.depth = 12;

    // 设置默认颜色和不透明度
    if (!config.color) config.color = new THREE.Color(0x6090a0);
    if (config.opacity === undefined) config.opacity = 0.6;

    // 设置默认温度和波动参数
    if (config.temperature === undefined) config.temperature = 80;
    if (config.waveAmplitude === undefined) config.waveAmplitude = 0.05;
    if (config.waveFrequency === undefined) config.waveFrequency = 1.5;
    if (config.waveSpeed === undefined) config.waveSpeed = 0.3;

    // 设置默认气泡效果
    if (config.enableBubbleEffect === undefined) config.enableBubbleEffect = true;
    if (config.bubbleEffectStrength === undefined) config.bubbleEffectStrength = 1.2;
    if (config.bubbleSizeRange === undefined) config.bubbleSizeRange = [0.06, 0.2];
    if (config.bubbleSpeedRange === undefined) config.bubbleSpeedRange = [0.12, 0.3];
    if (config.bubbleDensity === undefined) config.bubbleDensity = 1.1;
    if (config.bubbleDistributionRadius === undefined) config.bubbleDistributionRadius = 0.7;

    // 设置默认气泡爆裂效果
    if (config.enableBubbleBurstEffect === undefined) config.enableBubbleBurstEffect = true;
    if (config.bubbleBurstEffectStrength === undefined) config.bubbleBurstEffectStrength = 1.1;

    // 设置默认水蒸气效果
    if (config.enableSteamEffect === undefined) config.enableSteamEffect = true;
    if (config.steamEffectStrength === undefined) config.steamEffectStrength = 1.4;
    if (!config.steamColor) config.steamColor = new THREE.Color(0xaabbcc);
    if (config.steamDensity === undefined) config.steamDensity = 1.2;
    if (config.steamSizeRange === undefined) config.steamSizeRange = [0.5, 1.8];
    if (config.steamSpeedRange === undefined) config.steamSpeedRange = [0.05, 0.1];
    if (config.steamRiseHeight === undefined) config.steamRiseHeight = 2.2;

    // 设置默认声音效果
    if (config.enableSoundEffect === undefined) config.enableSoundEffect = true;
    if (config.soundEffectVolume === undefined) config.soundEffectVolume = 1.0;

    // 设置默认热扩散效果
    if (config.enableHeatDiffusion === undefined) config.enableHeatDiffusion = true;
    if (config.heatDiffusionRange === undefined) config.heatDiffusionRange = 7.0;

    // 设置默认矿物质效果
    if (config.enableMineralEffect === undefined) config.enableMineralEffect = true;
    if (!config.mineralColor) config.mineralColor = new THREE.Color(0x708090);
  }
}
