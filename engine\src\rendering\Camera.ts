/**
 * 相机类
 * 表示场景中的相机
 */
import * as THREE from 'three';
import { Component } from '../core/Component';

export enum CameraType {
  PERSPECTIVE = 'perspective',
  ORTHOGRAPHIC = 'orthographic',
}

export interface CameraOptions {
  /** 相机类型 */
  type?: CameraType;
  /** 视野角度（透视相机） */
  fov?: number;
  /** 近裁剪面 */
  near?: number;
  /** 远裁剪面 */
  far?: number;
  /** 宽高比 */
  aspect?: number;
  /** 左平面（正交相机） */
  left?: number;
  /** 右平面（正交相机） */
  right?: number;
  /** 上平面（正交相机） */
  top?: number;
  /** 下平面（正交相机） */
  bottom?: number;
  /** 是否自动更新宽高比 */
  autoAspect?: boolean;
}

export class Camera extends Component {
  /** 组件类型 */
  public static readonly type: string = 'Camera';

  /** 相机类型 */
  private cameraType: CameraType;

  /** Three.js相机 */
  private camera: THREE.Camera;

  /** 是否自动更新宽高比 */
  private autoAspect: boolean;

  /** 调整大小监听器 */
  private resizeListener: ((event: Event) => void) | null = null;

  /**
   * 创建相机组件
   * @param options 相机选项
   */
  constructor(options: CameraOptions = {}) {
    super(Camera.type);

    this.cameraType = options.type || CameraType.PERSPECTIVE;
    this.autoAspect = options.autoAspect !== undefined ? options.autoAspect : true;

    // 创建Three.js相机
    if (this.cameraType === CameraType.PERSPECTIVE) {
      const fov = options.fov || 75;
      const aspect = options.aspect || 1;
      const near = options.near || 0.1;
      const far = options.far || 1000;

      this.camera = new THREE.PerspectiveCamera(fov, aspect, near, far);
    } else {
      const left = options.left || -1;
      const right = options.right || 1;
      const top = options.top || 1;
      const bottom = options.bottom || -1;
      const near = options.near || 0.1;
      const far = options.far || 1000;

      this.camera = new THREE.OrthographicCamera(left, right, top, bottom, near, far);
    }

    // 如果启用自动更新宽高比，添加窗口调整大小事件监听器
    if (this.autoAspect) {
      this.resizeListener = this.handleResize.bind(this);
      window.addEventListener('resize', this.resizeListener);
    }
  }

  /**
   * 当组件附加到实体时调用
   */
  protected onAttach(): void {
    if (!this.entity) return;

    // 获取实体的变换组件
    const transform = this.entity.getTransform();
    if (transform) {
      // 将相机添加到变换的Three.js对象
      transform.getObject3D().add(this.camera);
    }

    // 如果启用自动更新宽高比，立即更新宽高比
    if (this.autoAspect) {
      this.updateAspect();
    }
  }

  /**
   * 处理窗口调整大小事件
   */
  private handleResize(): void {
    this.updateAspect();
  }

  /**
   * 更新宽高比
   */
  private updateAspect(): void {
    if (this.cameraType !== CameraType.PERSPECTIVE) {
      return;
    }

    const perspectiveCamera = this.camera as THREE.PerspectiveCamera;

    // 获取窗口宽高比
    const aspect = window.innerWidth / window.innerHeight;

    // 更新相机宽高比
    perspectiveCamera.aspect = aspect;
    perspectiveCamera.updateProjectionMatrix();
  }

  /**
   * 设置视野角度（透视相机）
   * @param fov 视野角度
   */
  public setFov(fov: number): void {
    if (this.cameraType !== CameraType.PERSPECTIVE) {
      console.warn('只有透视相机才能设置视野角度');
      return;
    }

    const perspectiveCamera = this.camera as THREE.PerspectiveCamera;
    perspectiveCamera.fov = fov;
    perspectiveCamera.updateProjectionMatrix();
  }

  /**
   * 获取视野角度（透视相机）
   * @returns 视野角度
   */
  public getFov(): number {
    if (this.cameraType !== CameraType.PERSPECTIVE) {
      console.warn('只有透视相机才能获取视野角度');
      return 0;
    }

    return (this.camera as THREE.PerspectiveCamera).fov;
  }

  /**
   * 设置宽高比（透视相机）
   * @param aspect 宽高比
   */
  public setAspect(aspect: number): void {
    if (this.cameraType !== CameraType.PERSPECTIVE) {
      console.warn('只有透视相机才能设置宽高比');
      return;
    }

    const perspectiveCamera = this.camera as THREE.PerspectiveCamera;
    perspectiveCamera.aspect = aspect;
    perspectiveCamera.updateProjectionMatrix();
  }

  /**
   * 获取宽高比（透视相机）
   * @returns 宽高比
   */
  public getAspect(): number {
    if (this.cameraType !== CameraType.PERSPECTIVE) {
      console.warn('只有透视相机才能获取宽高比');
      return 0;
    }

    return (this.camera as THREE.PerspectiveCamera).aspect;
  }

  /**
   * 设置近裁剪面
   * @param near 近裁剪面
   */
  public setNear(near: number): void {
    if (this.cameraType === CameraType.PERSPECTIVE) {
      const perspectiveCamera = this.camera as THREE.PerspectiveCamera;
      perspectiveCamera.near = near;
      perspectiveCamera.updateProjectionMatrix();
    } else {
      const orthographicCamera = this.camera as THREE.OrthographicCamera;
      orthographicCamera.near = near;
      orthographicCamera.updateProjectionMatrix();
    }
  }

  /**
   * 获取近裁剪面
   * @returns 近裁剪面
   */
  public getNear(): number {
    if (this.cameraType === CameraType.PERSPECTIVE) {
      return (this.camera as THREE.PerspectiveCamera).near;
    } else {
      return (this.camera as THREE.OrthographicCamera).near;
    }
  }

  /**
   * 设置远裁剪面
   * @param far 远裁剪面
   */
  public setFar(far: number): void {
    if (this.cameraType === CameraType.PERSPECTIVE) {
      const perspectiveCamera = this.camera as THREE.PerspectiveCamera;
      perspectiveCamera.far = far;
      perspectiveCamera.updateProjectionMatrix();
    } else {
      const orthographicCamera = this.camera as THREE.OrthographicCamera;
      orthographicCamera.far = far;
      orthographicCamera.updateProjectionMatrix();
    }
  }

  /**
   * 获取远裁剪面
   * @returns 远裁剪面
   */
  public getFar(): number {
    if (this.cameraType === CameraType.PERSPECTIVE) {
      return (this.camera as THREE.PerspectiveCamera).far;
    } else {
      return (this.camera as THREE.OrthographicCamera).far;
    }
  }

  /**
   * 设置正交相机参数
   * @param left 左平面
   * @param right 右平面
   * @param top 上平面
   * @param bottom 下平面
   */
  public setOrthographicSize(left: number, right: number, top: number, bottom: number): void {
    if (this.cameraType !== CameraType.ORTHOGRAPHIC) {
      console.warn('只有正交相机才能设置正交大小');
      return;
    }

    const orthographicCamera = this.camera as THREE.OrthographicCamera;
    orthographicCamera.left = left;
    orthographicCamera.right = right;
    orthographicCamera.top = top;
    orthographicCamera.bottom = bottom;
    orthographicCamera.updateProjectionMatrix();
  }

  /**
   * 设置正交相机缩放
   * @param zoom 缩放
   */
  public setZoom(zoom: number): void {
    if (this.cameraType === CameraType.PERSPECTIVE) {
      const perspectiveCamera = this.camera as THREE.PerspectiveCamera;
      perspectiveCamera.zoom = zoom;
      perspectiveCamera.updateProjectionMatrix();
    } else {
      const orthographicCamera = this.camera as THREE.OrthographicCamera;
      orthographicCamera.zoom = zoom;
      orthographicCamera.updateProjectionMatrix();
    }
  }

  /**
   * 获取正交相机缩放
   * @returns 缩放
   */
  public getZoom(): number {
    if (this.cameraType === CameraType.PERSPECTIVE) {
      return (this.camera as THREE.PerspectiveCamera).zoom;
    } else {
      return (this.camera as THREE.OrthographicCamera).zoom;
    }
  }

  /**
   * 获取相机类型
   * @returns 相机类型
   */
  public getType(): CameraType {
    return this.cameraType;
  }

  /**
   * 获取Three.js相机
   * @returns Three.js相机
   */
  public getThreeCamera(): THREE.Camera {
    return this.camera;
  }

  /**
   * 获取视图矩阵
   * @returns 视图矩阵
   */
  public getViewMatrix(): THREE.Matrix4 {
    return this.camera.matrixWorldInverse.clone();
  }

  /**
   * 获取投影矩阵
   * @returns 投影矩阵
   */
  public getProjectionMatrix(): THREE.Matrix4 {
    return this.camera.projectionMatrix.clone();
  }

  /**
   * 获取视图投影矩阵
   * @returns 视图投影矩阵
   */
  public getViewProjectionMatrix(): THREE.Matrix4 {
    const viewProjectionMatrix = new THREE.Matrix4();
    return viewProjectionMatrix.multiplyMatrices(
      this.camera.projectionMatrix,
      this.camera.matrixWorldInverse
    );
  }

  /**
   * 获取世界位置
   * @returns 世界位置
   */
  public getPosition(): THREE.Vector3 {
    const position = new THREE.Vector3();
    this.camera.getWorldPosition(position);
    return position;
  }

  /**
   * 获取前方向
   * @returns 前方向
   */
  public getForward(): THREE.Vector3 {
    const forward = new THREE.Vector3(0, 0, -1);
    forward.applyQuaternion(this.camera.quaternion);
    return forward;
  }

  /**
   * 获取右方向
   * @returns 右方向
   */
  public getRight(): THREE.Vector3 {
    const right = new THREE.Vector3(1, 0, 0);
    right.applyQuaternion(this.camera.quaternion);
    return right;
  }

  /**
   * 获取上方向
   * @returns 上方向
   */
  public getUp(): THREE.Vector3 {
    const up = new THREE.Vector3(0, 1, 0);
    up.applyQuaternion(this.camera.quaternion);
    return up;
  }

  /**
   * 设置自动更新宽高比
   * @param autoAspect 是否自动更新宽高比
   */
  public setAutoAspect(autoAspect: boolean): void {
    if (this.autoAspect === autoAspect) {
      return;
    }

    this.autoAspect = autoAspect;

    if (autoAspect) {
      // 添加窗口调整大小事件监听器
      if (!this.resizeListener) {
        this.resizeListener = this.handleResize.bind(this);
      }
      window.addEventListener('resize', this.resizeListener);

      // 立即更新宽高比
      this.updateAspect();
    } else {
      // 移除窗口调整大小事件监听器
      if (this.resizeListener) {
        window.removeEventListener('resize', this.resizeListener);
      }
    }
  }

  /**
   * 是否自动更新宽高比
   * @returns 是否自动更新宽高比
   */
  public isAutoAspect(): boolean {
    return this.autoAspect;
  }

  /**
   * 销毁组件
   */
  public dispose(): void {
    // 移除窗口调整大小事件监听器
    if (this.resizeListener) {
      window.removeEventListener('resize', this.resizeListener);
      this.resizeListener = null;
    }

    (super as any).dispose();
  }
}
