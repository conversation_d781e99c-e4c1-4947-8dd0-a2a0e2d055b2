/**
 * InteractionEventComponent.ts
 *
 * 交互事件组件，用于处理交互事件
 */

import { Component } from '../../core/Component';
import type { Entity } from '../../core/Entity';
import { EventEmitter } from '../../utils/EventEmitter';

/**
 * 交互事件类型枚举
 */
export enum InteractionEventType {
  /** 交互开始 */
  INTERACTION_START = 'interactionStart',
  /** 交互结束 */
  INTERACTION_END = 'interactionEnd',
  /** 进入交互范围 */
  ENTER_RANGE = 'enterRange',
  /** 离开交互范围 */
  EXIT_RANGE = 'exitRange',
  /** 高亮开始 */
  HIGHLIGHT_START = 'highlightStart',
  /** 高亮结束 */
  HIGHLIGHT_END = 'highlightEnd',
  /** 悬停开始 */
  HOVER_START = 'hoverStart',
  /** 悬停结束 */
  HOVER_END = 'hoverEnd'
}

/**
 * 交互事件数据
 */
export interface InteractionEventData {
  /** 事件类型 */
  type: InteractionEventType;
  /** 目标实体 */
  target: Entity;
  /** 源实体 */
  source?: Entity;
  /** 时间戳 */
  timestamp: number;
  /** 其他数据 */
  [key: string]: any;
}

/**
 * 交互事件
 */
export class InteractionEvent {
  /** 事件类型 */
  type: InteractionEventType;
  /** 目标实体 */
  target: Entity;
  /** 源实体 */
  source?: Entity;
  /** 时间戳 */
  timestamp: number;
  /** 事件数据 */
  data: any;

  /**
   * 构造函数
   * @param type 事件类型
   * @param target 目标实体
   * @param source 源实体
   * @param data 事件数据
   */
  constructor(type: InteractionEventType, target: Entity, source?: Entity, data: any = {}) {
    this.type = type;
    this.target = target;
    this.source = source;
    this.timestamp = Date.now();
    this.data = data;
  }
}

/**
 * 交互事件监听器类型
 */
export type InteractionEventListener = (event: InteractionEvent) => void;

/**
 * 交互事件组件配置
 */
export interface InteractionEventComponentConfig {
  /** 是否启用 */
  enabled?: boolean;
}

/**
 * 交互事件组件
 * 用于处理交互事件
 */
export class InteractionEventComponent extends Component {
  /** 事件发射器 */
  private eventEmitter: EventEmitter = new EventEmitter();

  /** 事件历史记录 */
  private eventHistory: InteractionEvent[] = [];

  /** 最大历史记录数量 */
  private readonly maxHistorySize: number = 10;

  /**
   * 构造函数
   * @param entity 关联的实体
   * @param config 组件配置
   */
  constructor(entity: Entity, config: InteractionEventComponentConfig = {}) {
    // 调用基类构造函数，传入组件类型名称
    super('InteractionEvent');

    // 设置实体引用
    this.setEntity(entity);

    // 初始化属性
    this.enabled = config.enabled !== undefined ? config.enabled : true;
  }



  /**
   * 添加事件监听器
   * @param type 事件类型
   * @param listener 监听器函数
   */
  addEventListener(type: InteractionEventType, listener: InteractionEventListener): void {
    this.eventEmitter.on(type, listener);
  }

  /**
   * 移除事件监听器
   * @param type 事件类型
   * @param listener 监听器函数
   */
  removeEventListener(type: InteractionEventType, listener?: InteractionEventListener): void {
    this.eventEmitter.off(type, listener);
  }

  /**
   * 分发事件
   * @param event 交互事件
   */
  dispatchEvent(event: InteractionEvent): void {
    // 如果未启用，则返回
    if (!this.enabled) return;

    // 添加到历史记录
    this.addToHistory(event);

    // 分发事件
    this.eventEmitter.emit(event.type, event);
  }

  /**
   * 创建并分发事件
   * @param type 事件类型
   * @param target 目标实体
   * @param source 源实体
   * @param data 事件数据
   */
  createAndDispatchEvent(type: InteractionEventType, target: Entity, source?: Entity, data: any = {}): void {
    const event = new InteractionEvent(type, target, source, data);
    this.dispatchEvent(event);
  }

  /**
   * 添加到历史记录
   * @param event 交互事件
   */
  private addToHistory(event: InteractionEvent): void {
    // 添加到历史记录
    this.eventHistory.unshift(event);

    // 如果超过最大数量，则移除最旧的
    if (this.eventHistory.length > this.maxHistorySize) {
      this.eventHistory.pop();
    }
  }

  /**
   * 获取事件历史记录
   * @returns 事件历史记录
   */
  getEventHistory(): InteractionEvent[] {
    return [...this.eventHistory];
  }

  /**
   * 清除事件历史记录
   */
  clearEventHistory(): void {
    this.eventHistory = [];
  }

  /**
   * 处理交互开始
   * @param target 目标实体
   * @param source 源实体
   * @param data 事件数据
   */
  handleInteractionStart(target: Entity, source?: Entity, data: any = {}): void {
    this.createAndDispatchEvent(InteractionEventType.INTERACTION_START, target, source, data);
  }

  /**
   * 处理交互结束
   * @param target 目标实体
   * @param source 源实体
   * @param data 事件数据
   */
  handleInteractionEnd(target: Entity, source?: Entity, data: any = {}): void {
    this.createAndDispatchEvent(InteractionEventType.INTERACTION_END, target, source, data);
  }

  /**
   * 处理进入交互范围
   * @param target 目标实体
   * @param source 源实体
   * @param data 事件数据
   */
  handleEnterRange(target: Entity, source?: Entity, data: any = {}): void {
    this.createAndDispatchEvent(InteractionEventType.ENTER_RANGE, target, source, data);
  }

  /**
   * 处理离开交互范围
   * @param target 目标实体
   * @param source 源实体
   * @param data 事件数据
   */
  handleExitRange(target: Entity, source?: Entity, data: any = {}): void {
    this.createAndDispatchEvent(InteractionEventType.EXIT_RANGE, target, source, data);
  }

  /**
   * 处理高亮开始
   * @param target 目标实体
   * @param source 源实体
   * @param data 事件数据
   */
  handleHighlightStart(target: Entity, source?: Entity, data: any = {}): void {
    this.createAndDispatchEvent(InteractionEventType.HIGHLIGHT_START, target, source, data);
  }

  /**
   * 处理高亮结束
   * @param target 目标实体
   * @param source 源实体
   * @param data 事件数据
   */
  handleHighlightEnd(target: Entity, source?: Entity, data: any = {}): void {
    this.createAndDispatchEvent(InteractionEventType.HIGHLIGHT_END, target, source, data);
  }

  /**
   * 处理悬停开始
   * @param target 目标实体
   * @param source 源实体
   * @param data 事件数据
   */
  handleHoverStart(target: Entity, source?: Entity, data: any = {}): void {
    this.createAndDispatchEvent(InteractionEventType.HOVER_START, target, source, data);
  }

  /**
   * 处理悬停结束
   * @param target 目标实体
   * @param source 源实体
   * @param data 事件数据
   */
  handleHoverEnd(target: Entity, source?: Entity, data: any = {}): void {
    this.createAndDispatchEvent(InteractionEventType.HOVER_END, target, source, data);
  }

  /**
   * 销毁组件
   */
  dispose(): void {
    // 移除所有事件监听器
    this.eventEmitter.removeAllListeners();

    // 清空历史记录
    this.clearEventHistory();

    // 调用基类的销毁方法
    (super as any).dispose();
  }
}
