/**
 * 雨水预设
 * 提供各种类型的雨水预设配置
 */
import * as THREE from 'three';
import  { Entity } from '../../core/Entity';
import type { World } from '../../core/World';
import { RainWaterComponent, RainWaterConfig, RainWaterType } from './RainWaterComponent';
import { Debug } from '../../utils/Debug';

/**
 * 雨水预设类型
 */
export enum RainWaterPresetType {
  /** 轻雨 */
  LIGHT = 'light',
  /** 中雨 */
  MEDIUM = 'medium',
  /** 暴雨 */
  HEAVY = 'heavy',
  /** 雷雨 */
  THUNDERSTORM = 'thunderstorm',
  /** 季风雨 */
  MONSOON = 'monsoon',
  /** 春雨 */
  SPRING_RAIN = 'spring_rain',
  /** 夏雨 */
  SUMMER_RAIN = 'summer_rain',
  /** 秋雨 */
  AUTUMN_RAIN = 'autumn_rain',
  /** 冬雨 */
  WINTER_RAIN = 'winter_rain'
}

/**
 * 雨水预设配置
 */
export interface RainWaterPresetConfig {
  /** 预设类型 */
  type: RainWaterPresetType;
  /** 位置 */
  position?: THREE.Vector3;
  /** 旋转 */
  rotation?: THREE.Euler;
  /** 尺寸 */
  size?: { width: number, height: number, depth: number };
  /** 是否启用 */
  enabled?: boolean;
}

/**
 * 雨水预设
 */
export class RainWaterPresets {
  /**
   * 创建雨水预设
   * @param world 世界
   * @param config 预设配置
   * @returns 雨水实体
   */
  public static createPreset(world: World, config: RainWaterPresetConfig): Entity {
    // 创建实体
    const entity = new Entity();

    // 创建雨水配置
    const rainWaterConfig: RainWaterConfig = {};

    // 设置位置
    if (config.position) {
      rainWaterConfig.position = config.position;
    }

    // 设置旋转
    if (config.rotation) {
      rainWaterConfig.rotation = config.rotation;
    }

    // 设置尺寸
    if (config.size) {
      rainWaterConfig.width = config.size.width;
      rainWaterConfig.height = config.size.height;
      rainWaterConfig.depth = config.size.depth;
    }

    // 设置是否启用
    if (config.enabled !== undefined) {
      rainWaterConfig.enabled = config.enabled;
    }

    // 根据预设类型应用特定配置
    switch (config.type) {
      case RainWaterPresetType.LIGHT:
        this.applyLightPreset(rainWaterConfig);
        break;
      case RainWaterPresetType.MEDIUM:
        this.applyMediumPreset(rainWaterConfig);
        break;
      case RainWaterPresetType.HEAVY:
        this.applyHeavyPreset(rainWaterConfig);
        break;
      case RainWaterPresetType.THUNDERSTORM:
        this.applyThunderstormPreset(rainWaterConfig);
        break;
      case RainWaterPresetType.MONSOON:
        this.applyMonsoonPreset(rainWaterConfig);
        break;
      case RainWaterPresetType.SPRING_RAIN:
        this.applySpringRainPreset(rainWaterConfig);
        break;
      case RainWaterPresetType.SUMMER_RAIN:
        this.applySummerRainPreset(rainWaterConfig);
        break;
      case RainWaterPresetType.AUTUMN_RAIN:
        this.applyAutumnRainPreset(rainWaterConfig);
        break;
      case RainWaterPresetType.WINTER_RAIN:
        this.applyWinterRainPreset(rainWaterConfig);
        break;
      default:
        this.applyMediumPreset(rainWaterConfig);
        break;
    }

    // 创建雨水组件
    const rainWaterComponent = new RainWaterComponent(entity, rainWaterConfig);

    // 添加到世界
    world.addEntity(entity);

    Debug.log('RainWaterPresets', `创建雨水预设: ${config.type}`);

    return entity;
  }

  /**
   * 应用轻雨预设
   * @param config 雨水配置
   */
  private static applyLightPreset(config: RainWaterConfig): void {
    // 设置默认尺寸
    if (!config.width) config.width = 20;
    if (!config.height) config.height = 0.05;
    if (!config.depth) config.depth = 20;

    // 设置默认颜色和不透明度
    config.color = new THREE.Color(0xaaccff);
    config.opacity = 0.6;

    // 设置默认流速
    config.flowSpeed = 0.5;

    // 设置雨水类型和强度
    config.rainWaterType = RainWaterType.LIGHT;
    config.rainIntensity = 0.5;

    // 设置雨滴参数
    config.raindropSize = 0.05;
    config.raindropFrequency = 0.5;
    config.raindropLifetime = 0.8;

    // 设置效果参数
    config.enableSplashEffect = true;
    config.splashEffectStrength = 0.5;
    config.enableRippleEffect = true;
    config.rippleEffectStrength = 0.7;
    config.enableFlowEffect = true;
    config.flowEffectStrength = 0.5;

    // 设置声音效果
    config.enableSoundEffect = true;
    config.soundEffectVolume = 0.5;

    // 设置水流动力学
    config.enableFluidDynamics = true;
  }

  /**
   * 应用中雨预设
   * @param config 雨水配置
   */
  private static applyMediumPreset(config: RainWaterConfig): void {
    // 设置默认尺寸
    if (!config.width) config.width = 20;
    if (!config.height) config.height = 0.1;
    if (!config.depth) config.depth = 20;

    // 设置默认颜色和不透明度
    config.color = new THREE.Color(0x88aaff);
    config.opacity = 0.7;

    // 设置默认流速
    config.flowSpeed = 1.0;

    // 设置雨水类型和强度
    config.rainWaterType = RainWaterType.MEDIUM;
    config.rainIntensity = 1.0;

    // 设置雨滴参数
    config.raindropSize = 0.1;
    config.raindropFrequency = 1.0;
    config.raindropLifetime = 1.0;

    // 设置效果参数
    config.enableSplashEffect = true;
    config.splashEffectStrength = 1.0;
    config.enableRippleEffect = true;
    config.rippleEffectStrength = 1.0;
    config.enableFlowEffect = true;
    config.flowEffectStrength = 1.0;

    // 设置声音效果
    config.enableSoundEffect = true;
    config.soundEffectVolume = 0.8;

    // 设置水流动力学
    config.enableFluidDynamics = true;
  }

  /**
   * 应用暴雨预设
   * @param config 雨水配置
   */
  private static applyHeavyPreset(config: RainWaterConfig): void {
    // 设置默认尺寸
    if (!config.width) config.width = 20;
    if (!config.height) config.height = 0.15;
    if (!config.depth) config.depth = 20;

    // 设置默认颜色和不透明度
    config.color = new THREE.Color(0x6688dd);
    config.opacity = 0.8;

    // 设置默认流速
    config.flowSpeed = 1.5;

    // 设置雨水类型和强度
    config.rainWaterType = RainWaterType.HEAVY;
    config.rainIntensity = 2.0;

    // 设置雨滴参数
    config.raindropSize = 0.15;
    config.raindropFrequency = 2.0;
    config.raindropLifetime = 1.2;

    // 设置效果参数
    config.enableSplashEffect = true;
    config.splashEffectStrength = 1.5;
    config.enableRippleEffect = true;
    config.rippleEffectStrength = 1.2;
    config.enableFlowEffect = true;
    config.flowEffectStrength = 1.5;

    // 设置声音效果
    config.enableSoundEffect = true;
    config.soundEffectVolume = 1.0;

    // 设置水流动力学
    config.enableFluidDynamics = true;
  }

  /**
   * 应用雷雨预设
   * @param config 雨水配置
   */
  private static applyThunderstormPreset(config: RainWaterConfig): void {
    // 设置默认尺寸
    if (!config.width) config.width = 20;
    if (!config.height) config.height = 0.2;
    if (!config.depth) config.depth = 20;

    // 设置默认颜色和不透明度
    config.color = new THREE.Color(0x5577cc);
    config.opacity = 0.9;

    // 设置默认流速
    config.flowSpeed = 2.0;

    // 设置雨水类型和强度
    config.rainWaterType = RainWaterType.THUNDERSTORM;
    config.rainIntensity = 3.0;

    // 设置雨滴参数
    config.raindropSize = 0.2;
    config.raindropFrequency = 3.0;
    config.raindropLifetime = 1.5;

    // 设置效果参数
    config.enableSplashEffect = true;
    config.splashEffectStrength = 2.0;
    config.enableRippleEffect = true;
    config.rippleEffectStrength = 1.5;
    config.enableFlowEffect = true;
    config.flowEffectStrength = 2.0;

    // 设置声音效果
    config.enableSoundEffect = true;
    config.soundEffectVolume = 1.2;

    // 设置水流动力学
    config.enableFluidDynamics = true;
  }

  /**
   * 应用季风雨预设
   * @param config 雨水配置
   */
  private static applyMonsoonPreset(config: RainWaterConfig): void {
    // 设置默认尺寸
    if (!config.width) config.width = 20;
    if (!config.height) config.height = 0.25;
    if (!config.depth) config.depth = 20;

    // 设置默认颜色和不透明度
    config.color = new THREE.Color(0x4466bb);
    config.opacity = 1.0;

    // 设置默认流速
    config.flowSpeed = 2.5;

    // 设置雨水类型和强度
    config.rainWaterType = RainWaterType.MONSOON;
    config.rainIntensity = 4.0;

    // 设置雨滴参数
    config.raindropSize = 0.25;
    config.raindropFrequency = 4.0;
    config.raindropLifetime = 2.0;

    // 设置效果参数
    config.enableSplashEffect = true;
    config.splashEffectStrength = 2.5;
    config.enableRippleEffect = true;
    config.rippleEffectStrength = 2.0;
    config.enableFlowEffect = true;
    config.flowEffectStrength = 2.5;

    // 设置声音效果
    config.enableSoundEffect = true;
    config.soundEffectVolume = 1.5;

    // 设置水流动力学
    config.enableFluidDynamics = true;
  }

  /**
   * 应用春雨预设
   * @param config 雨水配置
   */
  private static applySpringRainPreset(config: RainWaterConfig): void {
    // 设置默认尺寸
    if (!config.width) config.width = 20;
    if (!config.height) config.height = 0.05;
    if (!config.depth) config.depth = 20;

    // 设置默认颜色和不透明度
    config.color = new THREE.Color(0xaaddff);
    config.opacity = 0.6;

    // 设置默认流速
    config.flowSpeed = 0.7;

    // 设置雨水类型和强度
    config.rainWaterType = RainWaterType.LIGHT;
    config.rainIntensity = 0.7;

    // 设置雨滴参数
    config.raindropSize = 0.07;
    config.raindropFrequency = 0.7;
    config.raindropLifetime = 0.9;

    // 设置效果参数
    config.enableSplashEffect = true;
    config.splashEffectStrength = 0.7;
    config.enableRippleEffect = true;
    config.rippleEffectStrength = 0.8;
    config.enableFlowEffect = true;
    config.flowEffectStrength = 0.7;

    // 设置声音效果
    config.enableSoundEffect = true;
    config.soundEffectVolume = 0.6;

    // 设置水流动力学
    config.enableFluidDynamics = true;
  }

  /**
   * 应用夏雨预设
   * @param config 雨水配置
   */
  private static applySummerRainPreset(config: RainWaterConfig): void {
    // 设置默认尺寸
    if (!config.width) config.width = 20;
    if (!config.height) config.height = 0.15;
    if (!config.depth) config.depth = 20;

    // 设置默认颜色和不透明度
    config.color = new THREE.Color(0x88bbff);
    config.opacity = 0.8;

    // 设置默认流速
    config.flowSpeed = 1.5;

    // 设置雨水类型和强度
    config.rainWaterType = RainWaterType.HEAVY;
    config.rainIntensity = 1.8;

    // 设置雨滴参数
    config.raindropSize = 0.15;
    config.raindropFrequency = 1.8;
    config.raindropLifetime = 1.2;

    // 设置效果参数
    config.enableSplashEffect = true;
    config.splashEffectStrength = 1.5;
    config.enableRippleEffect = true;
    config.rippleEffectStrength = 1.3;
    config.enableFlowEffect = true;
    config.flowEffectStrength = 1.5;

    // 设置声音效果
    config.enableSoundEffect = true;
    config.soundEffectVolume = 1.0;

    // 设置水流动力学
    config.enableFluidDynamics = true;
  }

  /**
   * 应用秋雨预设
   * @param config 雨水配置
   */
  private static applyAutumnRainPreset(config: RainWaterConfig): void {
    // 设置默认尺寸
    if (!config.width) config.width = 20;
    if (!config.height) config.height = 0.1;
    if (!config.depth) config.depth = 20;

    // 设置默认颜色和不透明度
    config.color = new THREE.Color(0x7799dd);
    config.opacity = 0.7;

    // 设置默认流速
    config.flowSpeed = 1.2;

    // 设置雨水类型和强度
    config.rainWaterType = RainWaterType.MEDIUM;
    config.rainIntensity = 1.2;

    // 设置雨滴参数
    config.raindropSize = 0.12;
    config.raindropFrequency = 1.2;
    config.raindropLifetime = 1.1;

    // 设置效果参数
    config.enableSplashEffect = true;
    config.splashEffectStrength = 1.2;
    config.enableRippleEffect = true;
    config.rippleEffectStrength = 1.1;
    config.enableFlowEffect = true;
    config.flowEffectStrength = 1.2;

    // 设置声音效果
    config.enableSoundEffect = true;
    config.soundEffectVolume = 0.9;

    // 设置水流动力学
    config.enableFluidDynamics = true;
  }

  /**
   * 应用冬雨预设
   * @param config 雨水配置
   */
  private static applyWinterRainPreset(config: RainWaterConfig): void {
    // 设置默认尺寸
    if (!config.width) config.width = 20;
    if (!config.height) config.height = 0.08;
    if (!config.depth) config.depth = 20;

    // 设置默认颜色和不透明度
    config.color = new THREE.Color(0x99aacc);
    config.opacity = 0.65;

    // 设置默认流速
    config.flowSpeed = 0.8;

    // 设置雨水类型和强度
    config.rainWaterType = RainWaterType.MEDIUM;
    config.rainIntensity = 0.8;

    // 设置雨滴参数
    config.raindropSize = 0.08;
    config.raindropFrequency = 0.8;
    config.raindropLifetime = 0.9;

    // 设置效果参数
    config.enableSplashEffect = true;
    config.splashEffectStrength = 0.8;
    config.enableRippleEffect = true;
    config.rippleEffectStrength = 0.9;
    config.enableFlowEffect = true;
    config.flowEffectStrength = 0.8;

    // 设置声音效果
    config.enableSoundEffect = true;
    config.soundEffectVolume = 0.7;

    // 设置水流动力学
    config.enableFluidDynamics = true;
  }
}
