/**
 * 增强版喷泉组件
 * 提供更真实的喷泉物理模拟和渲染效果
 */
import * as THREE from 'three';
import { WaterBodyType } from './WaterBodyComponent';
import { FountainComponent, FountainConfig, FountainType } from './FountainComponent';
import type { Entity } from '../../core/Entity';
import { AudioSystem } from '../../audio/AudioSystem';
import { UnderwaterParticleSystem } from '../../rendering/water/UnderwaterParticleSystem';
import { WaterInstancedRenderer, WaterEffectType } from '../../rendering/water/WaterInstancedRenderer';
import { Debug } from '../../utils/Debug';
import { PerformanceMonitor } from '../../utils/PerformanceMonitor';

/**
 * 增强版喷泉类型
 */
export enum EnhancedFountainType {
  /** 标准喷泉 */
  STANDARD = 'standard',
  /** 高喷泉 */
  HIGH = 'high',
  /** 宽喷泉 */
  WIDE = 'wide',
  /** 多喷头喷泉 */
  MULTI_JET = 'multi_jet',
  /** 舞蹈喷泉 */
  DANCING = 'dancing',
  /** 音乐喷泉 */
  MUSICAL = 'musical',
  /** 脉冲喷泉 */
  PULSE = 'pulse',
  /** 交替喷泉 */
  ALTERNATING = 'alternating',
  /** 序列喷泉 */
  SEQUENCE = 'sequence',
  /** 随机喷泉 */
  RANDOM = 'random',
  /** 螺旋喷泉 */
  SPIRAL = 'spiral',
  /** 圆锥喷泉 */
  CONE = 'cone',
  /** 花朵喷泉 */
  FLOWER = 'flower',
  /** 彩虹喷泉 */
  RAINBOW = 'rainbow',
  /** 交互式喷泉 */
  INTERACTIVE = 'interactive'
}

/**
 * 喷泉喷射形状
 */
export enum FountainJetShape {
  /** 圆柱形 */
  CYLINDER = 'cylinder',
  /** 圆锥形 */
  CONE = 'cone',
  /** 球形 */
  SPHERE = 'sphere',
  /** 扁平 */
  FLAT = 'flat',
  /** 螺旋 */
  SPIRAL = 'spiral',
  /** 花朵 */
  FLOWER = 'flower',
  /** 自定义 */
  CUSTOM = 'custom'
}

/**
 * 增强版喷泉配置
 */
export interface EnhancedFountainConfig extends FountainConfig {
  /** 喷泉喷射形状 */
  jetShape?: FountainJetShape;
  /** 喷泉喷射高度变化速度 */
  jetHeightChangeSpeed?: number;
  /** 喷泉喷射宽度变化速度 */
  jetWidthChangeSpeed?: number;
  /** 喷泉喷射角度变化速度 */
  jetAngleChangeSpeed?: number;
  /** 喷泉喷射密度 */
  jetDensity?: number;
  /** 喷泉喷射粒子大小 */
  jetParticleSize?: number;
  /** 喷泉喷射粒子大小变化范围 */
  jetParticleSizeVariation?: number;
  /** 喷泉喷射粒子颜色 */
  jetParticleColor?: THREE.Color;
  /** 喷泉喷射粒子颜色变化范围 */
  jetParticleColorVariation?: number;
  /** 喷泉喷射粒子寿命 */
  jetParticleLifetime?: number;
  /** 喷泉喷射粒子寿命变化范围 */
  jetParticleLifetimeVariation?: number;
  /** 喷泉喷射粒子速度 */
  jetParticleSpeed?: number;
  /** 喷泉喷射粒子速度变化范围 */
  jetParticleSpeedVariation?: number;
  /** 喷泉喷射粒子重力系数 */
  jetParticleGravityFactor?: number;
  /** 喷泉喷射粒子阻力系数 */
  jetParticleDragFactor?: number;
  /** 喷泉喷射粒子旋转速度 */
  jetParticleRotationSpeed?: number;
  /** 喷泉喷射粒子旋转速度变化范围 */
  jetParticleRotationSpeedVariation?: number;
  /** 是否启用喷泉喷射粒子轨迹 */
  enableJetParticleTrails?: boolean;
  /** 喷泉喷射粒子轨迹长度 */
  jetParticleTrailLength?: number;
  /** 是否启用喷泉喷射粒子光散射 */
  enableJetParticleScattering?: boolean;
  /** 喷泉喷射粒子光散射强度 */
  jetParticleScatteringStrength?: number;
  /** 是否启用喷泉喷射粒子反射 */
  enableJetParticleReflections?: boolean;
  /** 喷泉喷射粒子反射强度 */
  jetParticleReflectionStrength?: number;
  /** 是否启用喷泉喷射粒子折射 */
  enableJetParticleRefractions?: boolean;
  /** 喷泉喷射粒子折射强度 */
  jetParticleRefractionStrength?: number;
  /** 是否启用喷泉喷射粒子声音 */
  enableJetParticleSounds?: boolean;
  /** 喷泉喷射粒子声音音量 */
  jetParticleSoundVolume?: number;
  /** 是否启用喷泉喷射粒子性能优化 */
  enableJetParticleOptimization?: boolean;
  /** 喷泉喷射粒子LOD距离 */
  jetParticleLODDistances?: number[];
  /** 是否启用GPU加速 */
  enableGPUAcceleration?: boolean;
  /** 是否启用彩色照明 */
  enableColoredLighting?: boolean;
  /** 彩色照明颜色 */
  coloredLightingColors?: THREE.Color[];
  /** 彩色照明变化速度 */
  coloredLightingChangeSpeed?: number;
  /** 是否启用音乐同步 */
  enableMusicSync?: boolean;
  /** 音乐同步灵敏度 */
  musicSyncSensitivity?: number;
  /** 音乐同步频率范围 */
  musicSyncFrequencyRange?: [number, number];
  /** 是否启用交互式控制 */
  enableInteractiveControl?: boolean;
  /** 交互式控制灵敏度 */
  interactiveControlSensitivity?: number;
}

/**
 * 喷泉粒子
 */
interface FountainParticle {
  /** 实例ID */
  id: string;
  /** 位置 */
  position: THREE.Vector3;
  /** 速度 */
  velocity: THREE.Vector3;
  /** 加速度 */
  acceleration: THREE.Vector3;
  /** 颜色 */
  color: THREE.Color;
  /** 大小 */
  size: number;
  /** 寿命 */
  lifetime: number;
  /** 当前年龄 */
  age: number;
  /** 是否活跃 */
  active: boolean;
  /** 用户数据 */
  userData: any;
}

/**
 * 喷泉喷射点
 */
interface FountainJet {
  /** 位置 */
  position: THREE.Vector3;
  /** 方向 */
  direction: THREE.Vector3;
  /** 强度 */
  strength: number;
  /** 高度 */
  height: number;
  /** 宽度 */
  width: number;
  /** 角度 */
  angle: number;
  /** 形状 */
  shape: FountainJetShape;
  /** 颜色 */
  color: THREE.Color;
  /** 是否活跃 */
  active: boolean;
  /** 用户数据 */
  userData: any;
}

/**
 * 增强版喷泉组件
 */
export class EnhancedFountainComponent extends FountainComponent {
  /** 喷泉喷射形状 */
  private jetShape: FountainJetShape = FountainJetShape.CYLINDER;
  /** 喷泉喷射高度变化速度 */
  private jetHeightChangeSpeed: number = 1.0;
  /** 喷泉喷射宽度变化速度 */
  private jetWidthChangeSpeed: number = 1.0;
  /** 喷泉喷射角度变化速度 */
  private jetAngleChangeSpeed: number = 1.0;
  /** 喷泉喷射密度 */
  private jetDensity: number = 1.0;
  /** 喷泉喷射粒子大小 */
  private jetParticleSize: number = 0.1;
  /** 喷泉喷射粒子大小变化范围 */
  private jetParticleSizeVariation: number = 0.05;
  /** 喷泉喷射粒子颜色 */
  private jetParticleColor: THREE.Color = new THREE.Color(0x88ccff);
  /** 喷泉喷射粒子颜色变化范围 */
  private jetParticleColorVariation: number = 0.2;
  /** 喷泉喷射粒子寿命 */
  private jetParticleLifetime: number = 2.0;
  /** 喷泉喷射粒子寿命变化范围 */
  private jetParticleLifetimeVariation: number = 0.5;
  /** 喷泉喷射粒子速度 */
  private jetParticleSpeed: number = 5.0;
  /** 喷泉喷射粒子速度变化范围 */
  private jetParticleSpeedVariation: number = 1.0;
  /** 喷泉喷射粒子重力系数 */
  private jetParticleGravityFactor: number = 1.0;
  /** 喷泉喷射粒子阻力系数 */
  private jetParticleDragFactor: number = 0.1;
  /** 喷泉喷射粒子旋转速度 */
  private jetParticleRotationSpeed: number = 1.0;
  /** 喷泉喷射粒子旋转速度变化范围 */
  private jetParticleRotationSpeedVariation: number = 0.5;
  /** 是否启用喷泉喷射粒子轨迹 */
  private enableJetParticleTrails: boolean = true;
  /** 喷泉喷射粒子轨迹长度 */
  private jetParticleTrailLength: number = 1.0;
  /** 是否启用喷泉喷射粒子光散射 */
  private enableJetParticleScattering: boolean = true;
  /** 喷泉喷射粒子光散射强度 */
  private jetParticleScatteringStrength: number = 1.0;
  /** 是否启用喷泉喷射粒子反射 */
  private enableJetParticleReflections: boolean = true;
  /** 喷泉喷射粒子反射强度 */
  private jetParticleReflectionStrength: number = 1.0;
  /** 是否启用喷泉喷射粒子折射 */
  private enableJetParticleRefractions: boolean = true;
  /** 喷泉喷射粒子折射强度 */
  private jetParticleRefractionStrength: number = 1.0;
  /** 是否启用喷泉喷射粒子声音 */
  private enableJetParticleSounds: boolean = true;
  /** 喷泉喷射粒子声音音量 */
  private jetParticleSoundVolume: number = 1.0;
  /** 是否启用喷泉喷射粒子性能优化 */
  private enableJetParticleOptimization: boolean = true;
  /** 喷泉喷射粒子LOD距离 */
  private jetParticleLODDistances: number[] = [10, 20, 50, 100];
  /** 是否启用GPU加速 */
  private enableGPUAcceleration: boolean = true;
  /** 是否启用彩色照明 */
  private enableColoredLighting: boolean = false;
  /** 彩色照明颜色 */
  private coloredLightingColors: THREE.Color[] = [
    new THREE.Color(0xff0000),
    new THREE.Color(0x00ff00),
    new THREE.Color(0x0000ff),
    new THREE.Color(0xffff00),
    new THREE.Color(0xff00ff),
    new THREE.Color(0x00ffff)
  ];
  /** 彩色照明变化速度 */
  private coloredLightingChangeSpeed: number = 1.0;
  /** 是否启用音乐同步 */
  private enableMusicSync: boolean = false;
  /** 音乐同步灵敏度 */
  private musicSyncSensitivity: number = 1.0;
  /** 音乐同步频率范围 */
  private musicSyncFrequencyRange: [number, number] = [20, 200];
  /** 是否启用交互式控制 */
  private enableInteractiveControl: boolean = false;
  /** 交互式控制灵敏度 */
  private interactiveControlSensitivity: number = 1.0;
  /** 性能监控器 */
  private performanceMonitor: PerformanceMonitor = PerformanceMonitor.getInstance();
  /** 喷泉粒子列表 */
  private particles: FountainParticle[] = [];
  /** 喷泉喷射点列表 */
  private jets: FountainJet[] = [];
  /** 水体实例化渲染器 */
  private waterInstancedRenderer: WaterInstancedRenderer | null = null;
  /** 水下粒子系统 */
  private underwaterParticleSystem: UnderwaterParticleSystem | null = null;
  /** 音频系统 */
  private audioSystem: AudioSystem | null = null;
  /** 音频源ID */
  private audioSourceId: string = '';
  /** 彩色照明灯光列表 */
  private coloredLights: THREE.PointLight[] = [];
  /** 彩色照明当前颜色索引 */
  private coloredLightingCurrentColorIndex: number = 0;
  /** 彩色照明计时器 */
  private coloredLightingTimer: number = 0;
  /** 粒子生成计时器 */
  private particleGenerationTimer: number = 0;
  /** 粒子生成间隔 */
  private particleGenerationInterval: number = 0.05;
  /** 粒子最大数量 */
  private maxParticles: number = 1000;
  /** 当前粒子数量 */
  private currentParticleCount: number = 0;
  /** 喷泉高度变化计时器 */
  private heightChangeTimer: number = 0;
  /** 喷泉宽度变化计时器 */
  private widthChangeTimer: number = 0;
  /** 喷泉角度变化计时器 */
  private angleChangeTimer: number = 0;
  /** 喷泉高度目标值 */
  private targetHeight: number = 5.0;
  /** 喷泉宽度目标值 */
  private targetWidth: number = 1.0;
  /** 喷泉角度目标值 */
  private targetAngle: number = 0.0;

  /**
   * 构造函数
   * @param entity 实体
   * @param config 配置
   */
  constructor(entity: Entity, config: EnhancedFountainConfig = {}) {
    super(entity, config);

    // 应用配置
    this.applyEnhancedConfig(config);
  }

  /**
   * 应用增强配置
   * @param config 配置
   */
  private applyEnhancedConfig(config: EnhancedFountainConfig): void {
    // 应用基本配置
    if (config.jetShape !== undefined) this.jetShape = config.jetShape;
    if (config.jetHeightChangeSpeed !== undefined) this.jetHeightChangeSpeed = config.jetHeightChangeSpeed;
    if (config.jetWidthChangeSpeed !== undefined) this.jetWidthChangeSpeed = config.jetWidthChangeSpeed;
    if (config.jetAngleChangeSpeed !== undefined) this.jetAngleChangeSpeed = config.jetAngleChangeSpeed;
    if (config.jetDensity !== undefined) this.jetDensity = config.jetDensity;
    if (config.jetParticleSize !== undefined) this.jetParticleSize = config.jetParticleSize;
    if (config.jetParticleSizeVariation !== undefined) this.jetParticleSizeVariation = config.jetParticleSizeVariation;
    if (config.jetParticleColor !== undefined) this.jetParticleColor = config.jetParticleColor;
    if (config.jetParticleColorVariation !== undefined) this.jetParticleColorVariation = config.jetParticleColorVariation;
    if (config.jetParticleLifetime !== undefined) this.jetParticleLifetime = config.jetParticleLifetime;
    if (config.jetParticleLifetimeVariation !== undefined) this.jetParticleLifetimeVariation = config.jetParticleLifetimeVariation;
    if (config.jetParticleSpeed !== undefined) this.jetParticleSpeed = config.jetParticleSpeed;
    if (config.jetParticleSpeedVariation !== undefined) this.jetParticleSpeedVariation = config.jetParticleSpeedVariation;
    if (config.jetParticleGravityFactor !== undefined) this.jetParticleGravityFactor = config.jetParticleGravityFactor;
    if (config.jetParticleDragFactor !== undefined) this.jetParticleDragFactor = config.jetParticleDragFactor;
    if (config.jetParticleRotationSpeed !== undefined) this.jetParticleRotationSpeed = config.jetParticleRotationSpeed;
    if (config.jetParticleRotationSpeedVariation !== undefined) this.jetParticleRotationSpeedVariation = config.jetParticleRotationSpeedVariation;
    if (config.enableJetParticleTrails !== undefined) this.enableJetParticleTrails = config.enableJetParticleTrails;
    if (config.jetParticleTrailLength !== undefined) this.jetParticleTrailLength = config.jetParticleTrailLength;
    if (config.enableJetParticleScattering !== undefined) this.enableJetParticleScattering = config.enableJetParticleScattering;
    if (config.jetParticleScatteringStrength !== undefined) this.jetParticleScatteringStrength = config.jetParticleScatteringStrength;
    if (config.enableJetParticleReflections !== undefined) this.enableJetParticleReflections = config.enableJetParticleReflections;
    if (config.jetParticleReflectionStrength !== undefined) this.jetParticleReflectionStrength = config.jetParticleReflectionStrength;
    if (config.enableJetParticleRefractions !== undefined) this.enableJetParticleRefractions = config.enableJetParticleRefractions;
    if (config.jetParticleRefractionStrength !== undefined) this.jetParticleRefractionStrength = config.jetParticleRefractionStrength;
    if (config.enableJetParticleSounds !== undefined) this.enableJetParticleSounds = config.enableJetParticleSounds;
    if (config.jetParticleSoundVolume !== undefined) this.jetParticleSoundVolume = config.jetParticleSoundVolume;
    if (config.enableJetParticleOptimization !== undefined) this.enableJetParticleOptimization = config.enableJetParticleOptimization;
    if (config.jetParticleLODDistances !== undefined) this.jetParticleLODDistances = config.jetParticleLODDistances;
    if (config.enableGPUAcceleration !== undefined) this.enableGPUAcceleration = config.enableGPUAcceleration;
    if (config.enableColoredLighting !== undefined) this.enableColoredLighting = config.enableColoredLighting;
    if (config.coloredLightingColors !== undefined) this.coloredLightingColors = config.coloredLightingColors;
    if (config.coloredLightingChangeSpeed !== undefined) this.coloredLightingChangeSpeed = config.coloredLightingChangeSpeed;
    if (config.enableMusicSync !== undefined) this.enableMusicSync = config.enableMusicSync;
    if (config.musicSyncSensitivity !== undefined) this.musicSyncSensitivity = config.musicSyncSensitivity;
    if (config.musicSyncFrequencyRange !== undefined) this.musicSyncFrequencyRange = config.musicSyncFrequencyRange;
    if (config.enableInteractiveControl !== undefined) this.enableInteractiveControl = config.enableInteractiveControl;
    if (config.interactiveControlSensitivity !== undefined) this.interactiveControlSensitivity = config.interactiveControlSensitivity;
  }

  /**
   * 初始化组件
   */
  public initialize(): void {
    // 调用父类初始化
    super.initialize();

    // 获取世界
    const world = this.entity.getWorld();

    // 获取水体实例化渲染器
    this.waterInstancedRenderer = world.getSystem(WaterInstancedRenderer) as WaterInstancedRenderer;

    // 获取水下粒子系统
    try {
      this.underwaterParticleSystem = world.getSystem(UnderwaterParticleSystem as any) as unknown as UnderwaterParticleSystem;
    } catch (error) {
      Debug.warn('EnhancedFountainComponent', '无法获取水下粒子系统:', error);
      this.underwaterParticleSystem = null;
    }

    // 获取音频系统
    this.audioSystem = world.getSystem(AudioSystem) as AudioSystem;

    // 初始化喷泉喷射点
    this.initializeJets();

    // 初始化彩色照明
    if (this.enableColoredLighting) {
      this.initializeColoredLighting();
    }

    // 初始化音频
    this.initializeEnhancedAudio();

    // 设置初始目标值
    this.targetHeight = this.getJetHeight();
    this.targetWidth = this.getJetWidth();
    this.targetAngle = this.getJetAngle();

    Debug.log('EnhancedFountainComponent', '增强版喷泉组件初始化完成');
  }

  /**
   * 获取喷泉类型
   * @returns 喷泉类型
   */
  private getFountainType(): FountainType {
    return (this as any).fountainType || FountainType.STANDARD;
  }

  /**
   * 初始化喷泉喷射点
   */
  private initializeJets(): void {
    // 清空现有喷射点
    this.jets = [];

    // 获取喷泉位置
    const position = this.getPosition();

    // 获取喷泉类型
    const fountainType = this.getFountainType();

    // 根据喷泉类型创建喷射点
    switch (fountainType) {
      case FountainType.STANDARD:
        // 标准喷泉（单个喷射点）
        this.createStandardJet(position);
        break;
      case FountainType.DOME:
        // 圆顶喷泉（高喷射点）
        this.createHighJet(position);
        break;
      case FountainType.WATERFALL:
        // 瀑布喷泉（宽喷射点）
        this.createWideJet(position);
        break;
      case FountainType.MULTI_JET:
        // 多喷头喷泉（多个喷射点）
        this.createMultiJet(position);
        break;
      case FountainType.DANCING:
        // 舞蹈喷泉（多个变化的喷射点）
        this.createDancingJet(position);
        break;
      case FountainType.MUSICAL:
        // 音乐喷泉（随音乐变化的喷射点）
        this.createMusicalJet(position);
        break;
      case FountainType.SPIRAL:
        // 螺旋喷泉（脉冲效果）
        this.createPulseJet(position);
        break;
      case FountainType.FLOWER:
        // 花朵喷泉（交替效果）
        this.createAlternatingJet(position);
        break;
      case FountainType.CROSSING:
        // 交叉喷泉（序列效果）
        this.createSequenceJet(position);
        break;
      case FountainType.INTERACTIVE:
        // 交互式喷泉（单个喷射点）
        this.createInteractiveJet(position);
        break;
      default:
        // 默认单个喷射点
        this.createStandardJet(position);
        break;
    }



    Debug.log('EnhancedFountainComponent', `初始化喷射点完成，共${this.jets.length}个喷射点`);
  }

  /**
   * 创建标准喷射点
   * @param position 位置
   */
  private createStandardJet(position: THREE.Vector3): void {
    const jet: FountainJet = {
      position: new THREE.Vector3(position.x, position.y, position.z),
      direction: new THREE.Vector3(0, 1, 0),
      strength: 1.0,
      height: 5.0,
      width: 1.0,
      angle: 0.0,
      shape: this.jetShape,
      color: this.jetParticleColor.clone(),
      active: true,
      userData: {}
    };
    this.jets.push(jet);
  }

  /**
   * 创建高喷射点
   * @param position 位置
   */
  private createHighJet(position: THREE.Vector3): void {
    const jet: FountainJet = {
      position: new THREE.Vector3(position.x, position.y, position.z),
      direction: new THREE.Vector3(0, 1, 0),
      strength: 1.5,
      height: 10.0,
      width: 0.8,
      angle: 0.0,
      shape: this.jetShape,
      color: this.jetParticleColor.clone(),
      active: true,
      userData: {}
    };
    this.jets.push(jet);
  }

  /**
   * 创建宽喷射点
   * @param position 位置
   */
  private createWideJet(position: THREE.Vector3): void {
    const jet: FountainJet = {
      position: new THREE.Vector3(position.x, position.y, position.z),
      direction: new THREE.Vector3(0, 1, 0),
      strength: 1.2,
      height: 4.0,
      width: 2.0,
      angle: 0.0,
      shape: this.jetShape,
      color: this.jetParticleColor.clone(),
      active: true,
      userData: {}
    };
    this.jets.push(jet);
  }

  /**
   * 创建多喷头喷射点
   * @param position 位置
   */
  private createMultiJet(position: THREE.Vector3): void {
    // 获取喷泉尺寸
    const size = this.getSize();
    const radius = size.width / 2 * 0.8;

    // 创建中心喷射点
    const centerJet: FountainJet = {
      position: new THREE.Vector3(position.x, position.y, position.z),
      direction: new THREE.Vector3(0, 1, 0),
      strength: 1.5,
      height: 8.0,
      width: 0.8,
      angle: 0.0,
      shape: this.jetShape,
      color: this.jetParticleColor.clone(),
      active: true,
      userData: {}
    };
    this.jets.push(centerJet);

    // 创建周围喷射点
    const jetCount = 6;
    for (let i = 0; i < jetCount; i++) {
      const angle = (i / jetCount) * Math.PI * 2;
      const x = position.x + Math.cos(angle) * radius;
      const z = position.z + Math.sin(angle) * radius;
      const jet: FountainJet = {
        position: new THREE.Vector3(x, position.y, z),
        direction: new THREE.Vector3(0, 1, 0),
        strength: 1.0,
        height: 5.0,
        width: 0.6,
        angle: 0.0,
        shape: this.jetShape,
        color: this.jetParticleColor.clone(),
        active: true,
        userData: {
          index: i,
          angle: angle
        }
      };
      this.jets.push(jet);
    }
  }

  /**
   * 创建舞蹈喷射点
   * @param position 位置
   */
  private createDancingJet(position: THREE.Vector3): void {
    // 获取喷泉尺寸
    const size = this.getSize();
    const radius = size.width / 2 * 0.8;

    // 创建多个喷射点
    const jetCount = 8;
    for (let i = 0; i < jetCount; i++) {
      const angle = (i / jetCount) * Math.PI * 2;
      const x = position.x + Math.cos(angle) * radius;
      const z = position.z + Math.sin(angle) * radius;

      // 创建喷射方向（略微倾斜）
      const direction = new THREE.Vector3(
        Math.cos(angle) * 0.2,
        1.0,
        Math.sin(angle) * 0.2
      ).normalize();

      const jet: FountainJet = {
        position: new THREE.Vector3(x, position.y, z),
        direction: direction,
        strength: 1.0 + Math.random() * 0.5,
        height: 4.0 + Math.random() * 2.0,
        width: 0.5 + Math.random() * 0.3,
        angle: angle,
        shape: this.jetShape,
        color: this.getRandomColor(),
        active: true,
        userData: {
          index: i,
          angle: angle,
          phaseOffset: Math.random() * Math.PI * 2,
          heightRange: 1.0 + Math.random() * 2.0,
          widthRange: 0.2 + Math.random() * 0.3
        }
      };
      this.jets.push(jet);
    }
  }

  /**
   * 创建音乐喷射点
   * @param position 位置
   */
  private createMusicalJet(position: THREE.Vector3): void {
    // 获取喷泉尺寸
    const size = this.getSize();
    const radius = size.width / 2 * 0.8;

    // 创建多个喷射点（代表不同频率范围）
    const jetCount = 10;
    for (let i = 0; i < jetCount; i++) {
      const angle = (i / jetCount) * Math.PI * 2;
      const x = position.x + Math.cos(angle) * radius;
      const z = position.z + Math.sin(angle) * radius;

      // 创建喷射方向（略微倾斜向中心）
      const direction = new THREE.Vector3(
        -Math.cos(angle) * 0.1,
        1.0,
        -Math.sin(angle) * 0.1
      ).normalize();

      const jet: FountainJet = {
        position: new THREE.Vector3(x, position.y, z),
        direction: direction,
        strength: 1.0,
        height: 3.0,
        width: 0.5,
        angle: 0.0,
        shape: this.jetShape,
        color: this.getColorFromFrequency(i / jetCount),
        active: true,
        userData: {
          index: i,
          angle: angle,
          frequencyMin: this.musicSyncFrequencyRange[0] + (i / jetCount) * (this.musicSyncFrequencyRange[1] - this.musicSyncFrequencyRange[0]),
          frequencyMax: this.musicSyncFrequencyRange[0] + ((i + 1) / jetCount) * (this.musicSyncFrequencyRange[1] - this.musicSyncFrequencyRange[0]),
          baseHeight: 3.0
        }
      };
      this.jets.push(jet);
    }
  }

  /**
   * 创建脉冲喷射点
   * @param position 位置
   */
  private createPulseJet(position: THREE.Vector3): void {
    // 创建中心喷射点
    const centerJet: FountainJet = {
      position: new THREE.Vector3(position.x, position.y, position.z),
      direction: new THREE.Vector3(0, 1, 0),
      strength: 1.0,
      height: 5.0,
      width: 1.0,
      angle: 0.0,
      shape: this.jetShape,
      color: this.jetParticleColor.clone(),
      active: true,
      userData: {
        pulsePhase: 0.0,
        pulseFrequency: 0.5, // Hz
        minHeight: 1.0,
        maxHeight: 8.0
      }
    };
    this.jets.push(centerJet);
  }

  /**
   * 创建交替喷射点
   * @param position 位置
   */
  private createAlternatingJet(position: THREE.Vector3): void {
    // 获取喷泉尺寸
    const size = this.getSize();
    const radius = size.width / 3;

    // 创建两组喷射点
    const jetCount = 5;
    for (let group = 0; group < 2; group++) {
      for (let i = 0; i < jetCount; i++) {
        const angle = (i / jetCount) * Math.PI * 2;
        const r = radius * (group === 0 ? 0.5 : 1.0);
        const x = position.x + Math.cos(angle) * r;
        const z = position.z + Math.sin(angle) * r;

        const jet: FountainJet = {
          position: new THREE.Vector3(x, position.y, z),
          direction: new THREE.Vector3(0, 1, 0),
          strength: 1.0,
          height: group === 0 ? 6.0 : 4.0,
          width: 0.5,
          angle: 0.0,
          shape: this.jetShape,
          color: group === 0 ? new THREE.Color(0x88ccff) : new THREE.Color(0x66aaff),
          active: group === 0, // 第一组初始激活
          userData: {
            group: group,
            index: i,
            switchTime: 2.0 // 切换时间（秒）
          }
        };
        this.jets.push(jet);
      }
    }
  }

  /**
   * 创建序列喷射点
   * @param position 位置
   */
  private createSequenceJet(position: THREE.Vector3): void {
    // 获取喷泉尺寸
    const size = this.getSize();
    const radius = size.width / 2 * 0.8;

    // 创建多个喷射点
    const jetCount = 12;
    for (let i = 0; i < jetCount; i++) {
      const angle = (i / jetCount) * Math.PI * 2;
      const x = position.x + Math.cos(angle) * radius;
      const z = position.z + Math.sin(angle) * radius;

      const jet: FountainJet = {
        position: new THREE.Vector3(x, position.y, z),
        direction: new THREE.Vector3(0, 1, 0),
        strength: 1.0,
        height: 5.0,
        width: 0.5,
        angle: 0.0,
        shape: this.jetShape,
        color: this.getColorFromIndex(i, jetCount),
        active: i === 0, // 只有第一个初始激活
        userData: {
          index: i,
          sequenceTime: 0.2, // 序列间隔时间（秒）
          totalCount: jetCount
        }
      };
      this.jets.push(jet);
    }
  }

  /**
   * 创建随机喷射点
   * @param position 位置
   */
  private createRandomJet(position: THREE.Vector3): void {
    // 获取喷泉尺寸
    const size = this.getSize();
    const radius = size.width / 2 * 0.8;

    // 创建多个喷射点
    const jetCount = 15;
    for (let i = 0; i < jetCount; i++) {
      // 随机位置
      const angle = Math.random() * Math.PI * 2;
      const r = Math.random() * radius;
      const x = position.x + Math.cos(angle) * r;
      const z = position.z + Math.sin(angle) * r;

      const jet: FountainJet = {
        position: new THREE.Vector3(x, position.y, z),
        direction: new THREE.Vector3(0, 1, 0),
        strength: 0.8 + Math.random() * 0.4,
        height: 3.0 + Math.random() * 3.0,
        width: 0.3 + Math.random() * 0.4,
        angle: 0.0,
        shape: this.jetShape,
        color: this.getRandomColor(),
        active: Math.random() < 0.3, // 30%概率初始激活
        userData: {
          minActiveTime: 0.5, // 最小激活时间（秒）
          maxActiveTime: 2.0, // 最大激活时间（秒）
          minInactiveTime: 0.5, // 最小非激活时间（秒）
          maxInactiveTime: 3.0, // 最大非激活时间（秒）
          nextStateChangeTime: Math.random() * 2.0 // 下一次状态变化时间
        }
      };
      this.jets.push(jet);
    }
  }

  /**
   * 创建交互式喷射点
   * @param position 位置
   */
  private createInteractiveJet(position: THREE.Vector3): void {
    // 创建中心喷射点
    const centerJet: FountainJet = {
      position: new THREE.Vector3(position.x, position.y, position.z),
      direction: new THREE.Vector3(0, 1, 0),
      strength: 1.0,
      height: 5.0,
      width: 1.0,
      angle: 0.0,
      shape: this.jetShape,
      color: this.jetParticleColor.clone(),
      active: true,
      userData: {
        interactive: true,
        baseHeight: 5.0,
        baseWidth: 1.0
      }
    };
    this.jets.push(centerJet);
  }

  /**
   * 初始化彩色照明
   */
  private initializeColoredLighting(): void {
    // 如果未启用彩色照明，则返回
    if (!this.enableColoredLighting) {
      return;
    }

    // 清理现有灯光
    this.clearColoredLights();

    // 获取喷泉位置
    const position = this.getPosition();

    // 创建彩色灯光
    const lightCount = this.coloredLightingColors.length;
    for (let i = 0; i < lightCount; i++) {
      // 创建点光源
      const light = new THREE.PointLight(this.coloredLightingColors[i], 1.0, 10.0);
      (light as any).setPosition(position.x, position.y + 0.5, position.z);
      light.castShadow = false;
      light.intensity = 0.0; // 初始强度为0

      // 添加到场景
      this.entity.getTransform().getObject3D().add(light);

      // 添加到灯光列表
      this.coloredLights.push(light);
    }

    Debug.log('EnhancedFountainComponent', `初始化彩色照明完成，共${lightCount}个灯光`);
  }

  /**
   * 初始化增强音频
   */
  private initializeEnhancedAudio(): void {
    // 如果未启用喷泉喷射粒子声音或没有音频系统，则返回
    if (!this.enableJetParticleSounds || !this.audioSystem) {
      return;
    }

    // 创建音频源
    const audioSource = this.audioSystem.createSource(this.entity.id);
    if (audioSource) {
      this.audioSourceId = this.entity.id;
      // 设置音频源属性
      audioSource.setVolume(this.jetParticleSoundVolume);
      audioSource.setLoop(true);
      audioSource.setSpatial(true);
      const pos = this.getPosition();
      audioSource.setPosition(pos.x, pos.y, pos.z);
      audioSource.setRefDistance(5.0);
      audioSource.setMaxDistance(50.0);
    }

    // 播放喷泉声音
    this.audioSystem.play(this.audioSourceId, 'sounds/fountain.mp3', {
      loop: true,
      volume: this.jetParticleSoundVolume
    });

    Debug.log('EnhancedFountainComponent', '初始化音频完成');
  }

  /**
   * 获取随机颜色
   * @returns 随机颜色
   */
  private getRandomColor(): THREE.Color {
    return new THREE.Color(
      Math.random() * this.jetParticleColorVariation + (1 - this.jetParticleColorVariation) * this.jetParticleColor.r,
      Math.random() * this.jetParticleColorVariation + (1 - this.jetParticleColorVariation) * this.jetParticleColor.g,
      Math.random() * this.jetParticleColorVariation + (1 - this.jetParticleColorVariation) * this.jetParticleColor.b
    );
  }

  /**
   * 根据频率获取颜色
   * @param normalizedFrequency 归一化频率（0-1）
   * @returns 颜色
   */
  private getColorFromFrequency(normalizedFrequency: number): THREE.Color {
    // 使用HSL颜色空间，频率映射到色相
    const hue = normalizedFrequency * 360;
    const saturation = 0.8;
    const lightness = 0.6;

    // 创建颜色
    const color = new THREE.Color();
    color.setHSL(hue / 360, saturation, lightness);

    return color;
  }

  /**
   * 根据索引获取颜色
   * @param index 索引
   * @param total 总数
   * @returns 颜色
   */
  private getColorFromIndex(index: number, total: number): THREE.Color {
    return this.getColorFromFrequency(index / total);
  }

  /**
   * 获取喷射高度
   * @returns 喷射高度
   */
  private getJetHeight(): number {
    // 如果没有喷射点，返回默认值
    if (this.jets.length === 0) {
      return 5.0;
    }

    // 返回第一个喷射点的高度
    return this.jets[0].height;
  }

  /**
   * 获取喷射宽度
   * @returns 喷射宽度
   */
  private getJetWidth(): number {
    // 如果没有喷射点，返回默认值
    if (this.jets.length === 0) {
      return 1.0;
    }

    // 返回第一个喷射点的宽度
    return this.jets[0].width;
  }

  /**
   * 获取喷射角度
   * @returns 喷射角度
   */
  private getJetAngle(): number {
    // 如果没有喷射点，返回默认值
    if (this.jets.length === 0) {
      return 0.0;
    }

    // 返回第一个喷射点的角度
    return this.jets[0].angle;
  }

  /**
   * 更新组件
   * @param deltaTime 帧间隔时间（秒）
   */
  public update(deltaTime: number): void {
    // 调用父类更新
    super.update(deltaTime);

    // 如果未启用，则不更新
    if (!this.isEnabled()) {
      return;
    }

    // 开始性能监控
    this.performanceMonitor.beginMeasure('enhancedFountainUpdate');

    // 更新喷射点
    this.updateEnhancedJets(deltaTime);

    // 更新粒子生成
    this.updateParticleGeneration(deltaTime);

    // 更新粒子
    this.updateParticles(deltaTime);

    // 更新彩色照明
    if (this.enableColoredLighting) {
      this.updateColoredLighting(deltaTime);
    }

    // 结束性能监控
    this.performanceMonitor.endMeasure('enhancedFountainUpdate');
  }

  /**
   * 更新增强喷射点
   * @param deltaTime 帧间隔时间（秒）
   */
  private updateEnhancedJets(deltaTime: number): void {
    // 获取喷泉类型
    const fountainType = this.getFountainType();

    // 根据喷泉类型更新喷射点
    switch (fountainType) {
      case FountainType.STANDARD:
      case FountainType.DOME:
      case FountainType.WATERFALL:
        // 这些类型的喷射点相对静态，只需更新目标值
        this.updateTargetValues(deltaTime);
        break;
      case FountainType.MULTI_JET:
        // 多喷头喷泉
        this.updateMultiJet(deltaTime);
        break;
      case FountainType.DANCING:
        // 舞蹈喷泉
        this.updateDancingJet(deltaTime);
        break;
      case FountainType.MUSICAL:
        // 音乐喷泉
        this.updateMusicalJet(deltaTime);
        break;
      case FountainType.SPIRAL:
        // 螺旋喷泉（脉冲效果）
        this.updatePulseJet(deltaTime);
        break;
      case FountainType.FLOWER:
        // 花朵喷泉（交替效果）
        this.updateAlternatingJet(deltaTime);
        break;
      case FountainType.CROSSING:
        // 交叉喷泉（序列效果）
        this.updateSequenceJet(deltaTime);
        break;
      case FountainType.INTERACTIVE:
        // 交互式喷泉
        this.updateInteractiveJet(deltaTime);
        break;
      default:
        // 默认更新
        this.updateTargetValues(deltaTime);
        break;
    }

    // 更新所有喷射点的高度、宽度和角度
    for (const jet of this.jets) {
      // 平滑过渡到目标值
      jet.height = this.smoothStep(jet.height, jet.userData.targetHeight || jet.height, deltaTime * this.jetHeightChangeSpeed);
      jet.width = this.smoothStep(jet.width, jet.userData.targetWidth || jet.width, deltaTime * this.jetWidthChangeSpeed);
      jet.angle = this.smoothStep(jet.angle, jet.userData.targetAngle || jet.angle, deltaTime * this.jetAngleChangeSpeed);
    }
  }

  /**
   * 更新目标值
   * @param deltaTime 帧间隔时间（秒）
   */
  private updateTargetValues(deltaTime: number): void {
    // 更新高度变化计时器
    this.heightChangeTimer += deltaTime;
    if (this.heightChangeTimer >= 5.0) {
      this.heightChangeTimer = 0.0;
      this.targetHeight = 3.0 + Math.random() * 5.0;
    }

    // 更新宽度变化计时器
    this.widthChangeTimer += deltaTime;
    if (this.widthChangeTimer >= 7.0) {
      this.widthChangeTimer = 0.0;
      this.targetWidth = 0.5 + Math.random() * 1.0;
    }

    // 更新角度变化计时器
    this.angleChangeTimer += deltaTime;
    if (this.angleChangeTimer >= 10.0) {
      this.angleChangeTimer = 0.0;
      this.targetAngle = (Math.random() - 0.5) * 0.2;
    }

    // 更新所有喷射点的目标值
    for (const jet of this.jets) {
      jet.userData.targetHeight = this.targetHeight;
      jet.userData.targetWidth = this.targetWidth;
      jet.userData.targetAngle = this.targetAngle;
    }
  }

  /**
   * 更新多喷头喷泉
   * @param deltaTime 帧间隔时间（秒）
   */
  private updateMultiJet(deltaTime: number): void {
    // 更新中心喷射点
    if (this.jets.length > 0) {
      const centerJet = this.jets[0];
      centerJet.userData.targetHeight = 6.0 + Math.sin(performance.now() * 0.001) * 2.0;
      centerJet.userData.targetWidth = 0.8 + Math.sin(performance.now() * 0.0005) * 0.2;
    }

    // 更新周围喷射点
    for (let i = 1; i < this.jets.length; i++) {
      const jet = this.jets[i];
      const angle = jet.userData.angle;
      const phase = performance.now() * 0.001 + angle;
      jet.userData.targetHeight = 4.0 + Math.sin(phase) * 1.5;
      jet.userData.targetWidth = 0.5 + Math.sin(phase * 0.5) * 0.2;
    }
  }

  /**
   * 更新舞蹈喷泉
   * @param deltaTime 帧间隔时间（秒）
   */
  private updateDancingJet(deltaTime: number): void {
    // 更新所有喷射点
    for (const jet of this.jets) {
      const index = jet.userData.index;
      const phaseOffset = jet.userData.phaseOffset;
      const heightRange = jet.userData.heightRange;
      const widthRange = jet.userData.widthRange;

      // 使用正弦波创建舞蹈效果
      const time = performance.now() * 0.001;
      const phase = time + phaseOffset;

      // 更新高度和宽度
      jet.userData.targetHeight = 4.0 + Math.sin(phase) * heightRange;
      jet.userData.targetWidth = 0.5 + Math.sin(phase * 0.5) * widthRange;

      // 更新方向（略微摆动）
      const swingAngle = Math.sin(phase * 0.3) * 0.1;
      const direction = new THREE.Vector3(
        Math.cos(jet.userData.angle) * swingAngle,
        1.0,
        Math.sin(jet.userData.angle) * swingAngle
      ).normalize();
      jet.direction.copy(direction);
    }
  }

  /**
   * 更新音乐喷泉
   * @param deltaTime 帧间隔时间（秒）
   */
  private updateMusicalJet(deltaTime: number): void {
    // 如果未启用音乐同步，使用模拟的音频数据
    if (!this.enableMusicSync) {
      // 模拟音频数据
      const time = performance.now() * 0.001;
      for (const jet of this.jets) {
        const index = jet.userData.index;
        const baseHeight = jet.userData.baseHeight;

        // 使用正弦波模拟不同频率的音频响应
        const frequency = 0.5 + index * 0.2;
        const amplitude = 0.5 + 0.5 * Math.sin(time * frequency);

        // 更新高度
        jet.userData.targetHeight = baseHeight * (1.0 + amplitude * 2.0);
      }
    } else {
      // 这里应该使用实际的音频分析数据
      // 由于没有实际的音频分析器，这里仍然使用模拟数据
      const time = performance.now() * 0.001;
      for (const jet of this.jets) {
        const index = jet.userData.index;
        const baseHeight = jet.userData.baseHeight;

        // 模拟频率响应
        const frequency = 0.5 + index * 0.2;
        const amplitude = 0.5 + 0.5 * Math.sin(time * frequency);

        // 更新高度
        jet.userData.targetHeight = baseHeight * (1.0 + amplitude * 2.0 * this.musicSyncSensitivity);
      }
    }
  }

  /**
   * 更新脉冲喷泉
   * @param deltaTime 帧间隔时间（秒）
   */
  private updatePulseJet(deltaTime: number): void {
    // 更新脉冲相位
    for (const jet of this.jets) {
      jet.userData.pulsePhase += deltaTime * jet.userData.pulseFrequency * Math.PI * 2;

      // 使用正弦波创建脉冲效果
      const pulseValue = 0.5 + 0.5 * Math.sin(jet.userData.pulsePhase);

      // 更新高度
      const minHeight = jet.userData.minHeight;
      const maxHeight = jet.userData.maxHeight;
      jet.userData.targetHeight = minHeight + pulseValue * (maxHeight - minHeight);
    }
  }

  /**
   * 更新交替喷泉
   * @param deltaTime 帧间隔时间（秒）
   */
  private updateAlternatingJet(deltaTime: number): void {
    // 更新交替计时器
    if (!this.jets[0]?.userData.switchTimer) {
      // 初始化计时器
      for (const jet of this.jets) {
        jet.userData.switchTimer = 0.0;
      }
    }

    // 增加计时器
    this.jets[0].userData.switchTimer += deltaTime;

    // 如果达到切换时间，交替激活状态
    if (this.jets[0].userData.switchTimer >= this.jets[0].userData.switchTime) {
      this.jets[0].userData.switchTimer = 0.0;

      // 交替激活状态
      for (const jet of this.jets) {
        jet.active = jet.userData.group === (jet.active ? 1 : 0);
      }
    }
  }

  /**
   * 更新序列喷泉
   * @param deltaTime 帧间隔时间（秒）
   */
  private updateSequenceJet(deltaTime: number): void {
    // 更新序列计时器
    if (!this.jets[0]?.userData.sequenceTimer) {
      // 初始化计时器
      for (const jet of this.jets) {
        jet.userData.sequenceTimer = 0.0;
        jet.userData.currentActiveIndex = 0;
      }
    }

    // 增加计时器
    this.jets[0].userData.sequenceTimer += deltaTime;

    // 如果达到序列间隔时间，激活下一个喷射点
    if (this.jets[0].userData.sequenceTimer >= this.jets[0].userData.sequenceTime) {
      this.jets[0].userData.sequenceTimer = 0.0;

      // 获取当前激活索引
      const currentIndex = this.jets[0].userData.currentActiveIndex;
      const nextIndex = (currentIndex + 1) % this.jets.length;

      // 更新激活状态
      for (const jet of this.jets) {
        jet.active = jet.userData.index === nextIndex;
      }

      // 更新当前激活索引
      this.jets[0].userData.currentActiveIndex = nextIndex;
    }
  }

  /**
   * 更新随机喷泉
   * @param deltaTime 帧间隔时间（秒）
   */
  private updateRandomJet(deltaTime: number): void {
    // 更新所有喷射点
    for (const jet of this.jets) {
      // 更新状态变化计时器
      if (!jet.userData.stateTimer) {
        jet.userData.stateTimer = 0.0;
      }
      jet.userData.stateTimer += deltaTime;

      // 如果达到下一次状态变化时间，改变激活状态
      if (jet.userData.stateTimer >= jet.userData.nextStateChangeTime) {
        jet.userData.stateTimer = 0.0;

        // 根据当前状态设置下一次状态变化时间
        if (jet.active) {
          // 当前激活，设置非激活时间
          jet.userData.nextStateChangeTime = jet.userData.minInactiveTime + Math.random() * (jet.userData.maxInactiveTime - jet.userData.minInactiveTime);
        } else {
          // 当前非激活，设置激活时间
          jet.userData.nextStateChangeTime = jet.userData.minActiveTime + Math.random() * (jet.userData.maxActiveTime - jet.userData.minActiveTime);
        }

        // 切换激活状态
        jet.active = !jet.active;
      }

      // 随机变化高度和宽度
      if (Math.random() < 0.05) {
        jet.userData.targetHeight = 3.0 + Math.random() * 3.0;
        jet.userData.targetWidth = 0.3 + Math.random() * 0.4;
      }
    }
  }

  /**
   * 更新交互式喷泉
   * @param deltaTime 帧间隔时间（秒）
   */
  private updateInteractiveJet(deltaTime: number): void {
    // 如果未启用交互式控制，使用默认行为
    if (!this.enableInteractiveControl) {
      // 使用简单的正弦波变化
      const time = performance.now() * 0.001;
      for (const jet of this.jets) {
        const baseHeight = jet.userData.baseHeight;
        const baseWidth = jet.userData.baseWidth;

        jet.userData.targetHeight = baseHeight * (1.0 + 0.5 * Math.sin(time * 0.5));
        jet.userData.targetWidth = baseWidth * (1.0 + 0.2 * Math.sin(time * 0.3));
      }
    } else {
      // 这里应该使用实际的交互输入数据
      // 由于没有实际的交互输入，这里仍然使用模拟数据
      const time = performance.now() * 0.001;
      for (const jet of this.jets) {
        const baseHeight = jet.userData.baseHeight;
        const baseWidth = jet.userData.baseWidth;

        // 模拟交互输入
        const interactionValue = 0.5 + 0.5 * Math.sin(time * 0.2);

        // 更新高度和宽度
        jet.userData.targetHeight = baseHeight * (1.0 + interactionValue * this.interactiveControlSensitivity);
        jet.userData.targetWidth = baseWidth * (1.0 + interactionValue * 0.5 * this.interactiveControlSensitivity);
      }
    }
  }

  /**
   * 更新彩色照明
   * @param deltaTime 帧间隔时间（秒）
   */
  private updateColoredLighting(deltaTime: number): void {
    // 如果未启用彩色照明或没有灯光，则返回
    if (!this.enableColoredLighting || this.coloredLights.length === 0) {
      return;
    }

    // 更新彩色照明计时器
    this.coloredLightingTimer += deltaTime * this.coloredLightingChangeSpeed;

    // 计算当前颜色索引和下一个颜色索引
    const currentIndex = Math.floor(this.coloredLightingTimer) % this.coloredLightingColors.length;
    const nextIndex = (currentIndex + 1) % this.coloredLightingColors.length;

    // 计算插值因子
    const t = this.coloredLightingTimer % 1.0;

    // 获取当前颜色和下一个颜色
    const currentColor = this.coloredLightingColors[currentIndex];
    const nextColor = this.coloredLightingColors[nextIndex];

    // 插值颜色
    const interpolatedColor = new THREE.Color(
      currentColor.r + (nextColor.r - currentColor.r) * t,
      currentColor.g + (nextColor.g - currentColor.g) * t,
      currentColor.b + (nextColor.b - currentColor.b) * t
    );

    // 更新所有灯光
    for (const light of this.coloredLights) {
      light.color.copy(interpolatedColor);

      // 根据喷泉活跃度调整灯光强度
      const activeJets = this.jets.filter(jet => jet.active).length;
      const intensity = activeJets / this.jets.length;
      light.intensity = intensity * 2.0;
    }
  }

  /**
   * 更新粒子生成
   * @param deltaTime 帧间隔时间（秒）
   */
  private updateParticleGeneration(deltaTime: number): void {
    // 更新粒子生成计时器
    this.particleGenerationTimer += deltaTime;

    // 如果达到生成间隔，生成粒子
    if (this.particleGenerationTimer >= this.particleGenerationInterval) {
      this.particleGenerationTimer = 0;
      this.generateParticles();
    }
  }

  /**
   * 生成粒子
   */
  private generateParticles(): void {
    // 如果没有水体实例化渲染器，返回
    if (!this.waterInstancedRenderer) {
      return;
    }

    // 如果已达到最大粒子数量，返回
    if (this.currentParticleCount >= this.maxParticles) {
      return;
    }

    // 遍历所有活跃的喷射点
    for (const jet of this.jets) {
      // 如果喷射点未激活，跳过
      if (!jet.active) {
        continue;
      }

      // 计算粒子数量
      const particleCount = this.calculateParticleCount(jet);

      // 生成粒子
      for (let i = 0; i < particleCount; i++) {
        // 如果已达到最大粒子数量，返回
        if (this.currentParticleCount >= this.maxParticles) {
          return;
        }

        // 创建粒子
        this.createParticle(jet);
      }
    }
  }

  /**
   * 计算粒子数量
   * @param jet 喷射点
   * @returns 粒子数量
   */
  private calculateParticleCount(jet: FountainJet): number {
    // 基础粒子数量
    const baseCount = 1;

    // 根据喷射强度和宽度调整数量
    const strengthFactor = jet.strength;
    const widthFactor = jet.width;
    const densityFactor = this.jetDensity;

    // 计算最终数量
    return Math.floor(baseCount * strengthFactor * widthFactor * densityFactor);
  }

  /**
   * 创建粒子
   * @param jet 喷射点
   */
  private createParticle(jet: FountainJet): void {
    // 如果没有水体实例化渲染器，返回
    if (!this.waterInstancedRenderer) {
      return;
    }

    // 计算粒子位置（在喷射点位置附近随机）
    const position = new THREE.Vector3(
      (jet as any).getPosition().x + (Math.random() - 0.5) * jet.width * 0.5,
      (jet as any).getPosition().y,
      (jet as any).getPosition().z + (Math.random() - 0.5) * jet.width * 0.5
    );

    // 计算粒子方向（基于喷射方向，添加随机偏移）
    const direction = new THREE.Vector3(
      jet.direction.x + (Math.random() - 0.5) * 0.2,
      jet.direction.y + (Math.random() - 0.5) * 0.1,
      jet.direction.z + (Math.random() - 0.5) * 0.2
    ).normalize();

    // 计算粒子速度
    const speed = this.jetParticleSpeed + (Math.random() - 0.5) * this.jetParticleSpeedVariation;
    const velocity = direction.clone().multiplyScalar(speed);

    // 计算粒子大小
    const size = this.jetParticleSize + (Math.random() - 0.5) * this.jetParticleSizeVariation;

    // 计算粒子颜色
    const color = jet.color.clone();
    if (this.jetParticleColorVariation > 0) {
      color.r += (Math.random() - 0.5) * this.jetParticleColorVariation;
      color.g += (Math.random() - 0.5) * this.jetParticleColorVariation;
      color.b += (Math.random() - 0.5) * this.jetParticleColorVariation;
      color.r = Math.max(0, Math.min(1, color.r));
      color.g = Math.max(0, Math.min(1, color.g));
      color.b = Math.max(0, Math.min(1, color.b));
    }

    // 计算粒子寿命
    const lifetime = this.jetParticleLifetime + (Math.random() - 0.5) * this.jetParticleLifetimeVariation;

    // 计算粒子旋转速度
    const rotationSpeed = this.jetParticleRotationSpeed + (Math.random() - 0.5) * this.jetParticleRotationSpeedVariation;

    // 创建粒子特效
    const id = this.waterInstancedRenderer.createEffectInstance(
      WaterEffectType.DROPLET,
      position,
      {
        rotation: new THREE.Euler(
          Math.random() * Math.PI * 2,
          Math.random() * Math.PI * 2,
          Math.random() * Math.PI * 2
        ),
        scale: new THREE.Vector3(size, size, size),
        color: color,
        opacity: 0.8,
        lifetime: lifetime,
        userData: {
          velocityX: velocity.x,
          velocityY: velocity.y,
          velocityZ: velocity.z,
          accelerationX: 0,
          accelerationY: -9.8 * this.jetParticleGravityFactor, // 重力加速度
          accelerationZ: 0,
          rotationSpeedX: (Math.random() - 0.5) * rotationSpeed,
          rotationSpeedY: (Math.random() - 0.5) * rotationSpeed,
          rotationSpeedZ: (Math.random() - 0.5) * rotationSpeed,
          dragFactor: this.jetParticleDragFactor,
          shape: jet.shape,
          jetId: this.jets.indexOf(jet),
          enableTrail: this.enableJetParticleTrails && Math.random() < 0.3, // 30%概率启用轨迹
          trailLength: this.jetParticleTrailLength,
          trailPositions: [],
          maxTrailPositions: Math.floor(10 * this.jetParticleTrailLength),
          trailTimer: 0,
          trailUpdateInterval: 0.05
        }
      }
    );

    // 添加到粒子列表
    if (id) {
      this.particles.push({
        id,
        position,
        velocity,
        acceleration: new THREE.Vector3(0, -9.8 * this.jetParticleGravityFactor, 0),
        color,
        size,
        lifetime,
        age: 0,
        active: true,
        userData: {
          jetId: this.jets.indexOf(jet)
        }
      });
      this.currentParticleCount++;
    }
  }

  /**
   * 更新粒子
   * @param deltaTime 帧间隔时间（秒）
   */
  private updateParticles(deltaTime: number): void {
    // 如果没有水体实例化渲染器，返回
    if (!this.waterInstancedRenderer) {
      return;
    }

    // 遍历所有粒子
    for (let i = this.particles.length - 1; i >= 0; i--) {
      const particle = this.particles[i];

      // 如果粒子不活跃，跳过
      if (!particle.active) {
        continue;
      }

      // 更新粒子年龄
      particle.age += deltaTime;

      // 如果粒子超过寿命，移除
      if (particle.age >= particle.lifetime) {
        this.removeParticle(i);
        continue;
      }

      // 获取粒子实例（简化处理，直接使用粒子数据）
      const instance = {
        position: particle.position,
        rotation: new THREE.Euler(),
        opacity: 1.0,
        userData: {
          rotationSpeedX: 0,
          rotationSpeedY: 0,
          rotationSpeedZ: 0,
          dragFactor: this.jetParticleDragFactor,
          enableTrail: this.enableJetParticleTrails,
          trailLength: this.jetParticleTrailLength,
          trailPositions: [],
          maxTrailPositions: 10,
          trailTimer: 0,
          trailUpdateInterval: 0.05
        }
      };

      // 更新粒子速度（考虑阻力）
      const dragFactor = instance.userData.dragFactor;
      particle.velocity.x += particle.acceleration.x * deltaTime - particle.velocity.x * dragFactor * deltaTime;
      particle.velocity.y += particle.acceleration.y * deltaTime - particle.velocity.y * dragFactor * deltaTime;
      particle.velocity.z += particle.acceleration.z * deltaTime - particle.velocity.z * dragFactor * deltaTime;

      // 更新粒子位置
      (particle as any).getPosition().x += particle.velocity.x * deltaTime;
      (particle as any).getPosition().y += particle.velocity.y * deltaTime;
      (particle as any).getPosition().z += particle.velocity.z * deltaTime;

      // 更新实例位置
      instance.position.copy(particle.position);

      // 更新实例旋转
      instance.rotation.x += instance.userData.rotationSpeedX * deltaTime;
      instance.rotation.y += instance.userData.rotationSpeedY * deltaTime;
      instance.rotation.z += instance.userData.rotationSpeedZ * deltaTime;

      // 更新实例不透明度（随年龄衰减）
      const normalizedAge = particle.age / particle.lifetime;
      instance.opacity = 0.8 * (1.0 - Math.pow(normalizedAge, 2));

      // 更新粒子轨迹
      if (instance.userData.enableTrail) {
        this.updateParticleTrail(instance, deltaTime);
      }
    }
  }

  /**
   * 更新粒子轨迹
   * @param instance 粒子实例
   * @param deltaTime 帧间隔时间（秒）
   */
  private updateParticleTrail(instance: any, deltaTime: number): void {
    // 更新轨迹计时器
    instance.userData.trailTimer += deltaTime;

    // 如果达到更新间隔，添加新的轨迹点
    if (instance.userData.trailTimer >= instance.userData.trailUpdateInterval) {
      instance.userData.trailTimer = 0;

      // 添加当前位置到轨迹
      instance.userData.trailPositions.push(instance.position.clone());

      // 如果轨迹点数量超过最大值，移除最旧的点
      if (instance.userData.trailPositions.length > instance.userData.maxTrailPositions) {
        instance.userData.trailPositions.shift();
      }
    }

    // 如果有轨迹点，创建或更新轨迹线
    if (instance.userData.trailPositions.length > 1) {
      // 这里应该创建或更新轨迹线
      // 由于这需要更复杂的实现，这里只是占位
    }
  }

  /**
   * 移除粒子
   * @param index 粒子索引
   */
  private removeParticle(index: number): void {
    // 如果没有水体实例化渲染器，返回
    if (!this.waterInstancedRenderer) {
      return;
    }

    // 获取粒子
    const particle = this.particles[index];

    // 移除粒子实例
    this.waterInstancedRenderer.destroyEffectInstance(particle.id);

    // 从列表中移除
    this.particles.splice(index, 1);
    this.currentParticleCount = Math.max(0, this.currentParticleCount - 1);
  }

  /**
   * 平滑过渡
   * @param current 当前值
   * @param target 目标值
   * @param speed 速度
   * @returns 新值
   */
  private smoothStep(current: number, target: number, speed: number): number {
    return current + (target - current) * Math.min(1, speed);
  }

  /**
   * 清理彩色灯光
   */
  private clearColoredLights(): void {
    // 移除所有灯光
    for (const light of this.coloredLights) {
      this.entity.getTransform().getObject3D().remove(light);
    }

    // 清空列表
    this.coloredLights = [];
  }

  /**
   * 设置喷泉类型
   * @param type 喷泉类型
   */
  public setFountainType(type: FountainType): void {
    super.setFountainType(type);

    // 重新初始化喷射点
    this.initializeJets();
  }

  /**
   * 设置喷射形状
   * @param shape 喷射形状
   */
  public setJetShape(shape: FountainJetShape): void {
    this.jetShape = shape;

    // 更新所有喷射点的形状
    for (const jet of this.jets) {
      jet.shape = shape;
    }
  }

  /**
   * 设置喷射高度变化速度
   * @param speed 变化速度
   */
  public setJetHeightChangeSpeed(speed: number): void {
    this.jetHeightChangeSpeed = speed;
  }

  /**
   * 设置喷射宽度变化速度
   * @param speed 变化速度
   */
  public setJetWidthChangeSpeed(speed: number): void {
    this.jetWidthChangeSpeed = speed;
  }

  /**
   * 设置喷射角度变化速度
   * @param speed 变化速度
   */
  public setJetAngleChangeSpeed(speed: number): void {
    this.jetAngleChangeSpeed = speed;
  }

  /**
   * 设置喷射密度
   * @param density 密度
   */
  public setJetDensity(density: number): void {
    this.jetDensity = density;
  }

  /**
   * 设置是否启用彩色照明
   * @param enable 是否启用
   */
  public setEnableColoredLighting(enable: boolean): void {
    this.enableColoredLighting = enable;

    // 如果启用彩色照明，初始化彩色照明
    if (enable) {
      this.initializeColoredLighting();
    } else {
      this.clearColoredLights();
    }
  }

  /**
   * 设置彩色照明颜色
   * @param colors 颜色数组
   */
  public setColoredLightingColors(colors: THREE.Color[]): void {
    this.coloredLightingColors = colors;

    // 如果启用了彩色照明，重新初始化
    if (this.enableColoredLighting) {
      this.clearColoredLights();
      this.initializeColoredLighting();
    }
  }

  /**
   * 设置彩色照明变化速度
   * @param speed 变化速度
   */
  public setColoredLightingChangeSpeed(speed: number): void {
    this.coloredLightingChangeSpeed = speed;
  }

  /**
   * 设置是否启用音乐同步
   * @param enable 是否启用
   */
  public setEnableMusicSync(enable: boolean): void {
    this.enableMusicSync = enable;
  }

  /**
   * 设置音乐同步灵敏度
   * @param sensitivity 灵敏度
   */
  public setMusicSyncSensitivity(sensitivity: number): void {
    this.musicSyncSensitivity = sensitivity;
  }

  /**
   * 设置是否启用交互式控制
   * @param enable 是否启用
   */
  public setEnableInteractiveControl(enable: boolean): void {
    this.enableInteractiveControl = enable;
  }

  /**
   * 设置交互式控制灵敏度
   * @param sensitivity 灵敏度
   */
  public setInteractiveControlSensitivity(sensitivity: number): void {
    this.interactiveControlSensitivity = sensitivity;
  }

  /**
   * 销毁组件
   */
  public destroy(): void {
    // 调用父类销毁
    (super as any).dispose();

    // 清理粒子
    this.clearParticles();

    // 清理彩色灯光
    this.clearColoredLights();

    // 清理音频
    this.clearAudio();
  }

  /**
   * 清理粒子
   */
  private clearParticles(): void {
    // 如果没有水体实例化渲染器，返回
    if (!this.waterInstancedRenderer) {
      return;
    }

    // 移除所有粒子实例
    for (const particle of this.particles) {
      this.waterInstancedRenderer.destroyEffectInstance(particle.id);
    }

    // 清空列表
    this.particles = [];
    this.currentParticleCount = 0;
  }

  /**
   * 清理音频
   */
  private clearAudio(): void {
    // 如果没有音频系统或音频源ID，返回
    if (!this.audioSystem || !this.audioSourceId) {
      return;
    }

    // 停止音频
    this.audioSystem.stop(this.audioSourceId);

    // 移除音频源
    this.audioSystem.removeSource(this.audioSourceId);

    // 清空音频源ID
    this.audioSourceId = '';
  }
}
